import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import Compression from 'vite-plugin-compression';
import AutoImport from 'unplugin-auto-import/vite';
import UnoCSS from 'unocss/vite';

export default defineConfig(({ command }) => {
  const alias = {
    '@/': resolve('src') + '/',
    'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
  };
  const plugins = [
    vue(),
    UnoCSS(),
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
      imports: [
        'vue',
        'vue-router',
        // 可额外添加需要 autoImport 的组件
        { 
          'vue-i18n': ['useI18n'],
          '@/hooks/web/useMessage': ['useMessage'],
          '@/utils/dict': ['DICT_TYPE'],
          '@/utils/use-form-data': ['useFormData']
        }
      ],
      //注意这个配置和src同级
      dts: './auto-imports.d.ts'
    })
  ];
  // 开发环境全局安装
  alias['./as-needed'] = './global-import';
  // gzip压缩
  plugins.push(
    Compression({
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz'
    })
  );

  return {
    resolve: { alias },
    plugins,
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`
        }
      }
    },
    optimizeDeps: {
      include: [
        'echarts/core',
        'echarts/charts',
        'echarts/renderers',
        'echarts/components',
        'vue-echarts',
        'vuedraggable',
        'sortablejs'
      ]
    },
    build: {
      target: 'chrome63',
      chunkSizeWarningLimit: 2000
    }
  };
});
