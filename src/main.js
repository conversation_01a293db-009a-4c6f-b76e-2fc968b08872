import { createApp } from 'vue';
import App from './App.vue';
import store from './store';
import router from './router';
import permission from './utils/permission';
import DictData from '@/components/DictData/index.vue';
import UploadImg from '@/components/UploadFile/src/UploadImg.vue';
import UploadImgs from '@/components/UploadFile/src/UploadImgs.vue';
import UploadFile from '@/components/UploadFile/src/UploadFile.vue';
import installer from './as-needed';
import 'element-plus/theme-chalk/display.css';
import 'ele-admin-plus/es/style/nprogress.scss';
import i18n from './i18n';
import './styles/themes/dark.scss';
import './styles/index.scss';
import { setupMountedFocus } from '@/directives';
import { setupFormCreate } from '@/plugins/formCreate';
import '@/plugins/unocss';
import VueDOMPurifyHTML from 'vue-dompurify-html'; // 解决v-html 的安全隐患

const app = createApp(App);
setupMountedFocus(app);
setupFormCreate(app);
app.use(store);
app.use(router);
app.use(permission);
app.use(i18n);
app.use(installer);
app.use(VueDOMPurifyHTML);
app.component('DictData', DictData);
app.component('UploadImg', UploadImg);
app.component('UploadImgs', UploadImgs);
app.component('UploadFile', UploadFile);

app.mount('#app');
