<template>
  <svg
    viewBox="0 0 48 48"
    fill="none"
    stroke="currentColor"
    stroke-width="4"
    stroke-linecap="round"
  >
    <path
      d="M24 45C35 45 45 35 45 24 45 13 35 3 24 3 13 3 3 13 3 24 3 35 13 45 24 45Z"
    />
    <path
      d="M17 19C17 15 20 12 24 12 28 12 31 15 31 18 31 20 30 23 27 24 25 25 24 26 24 27V29"
    />
    <circle cx="24" cy="36" r="2.4" fill="currentColor" stroke="none" />
  </svg>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';

  export default defineComponent({
    name: 'QuestionCircleOutlined'
  });
</script>
