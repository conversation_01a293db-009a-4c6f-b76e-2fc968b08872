<!-- 字典组件 -->
<template>
  <template v-if="type === 'text'">
    <span v-for="item in valueData" :key="item.keyIndex">
      {{ item.label }}
    </span>
  </template>
  <template v-else-if="type === 'tag'">
    <el-tag
      v-for="item in valueData"
      :key="item.keyIndex"
      :disable-transitions="true"
      size="small"
      :type="item.colorType === 'default' ? 'primary' : item.colorType"
    >
      {{ item.label }}
    </el-tag>
  </template>
  <template v-else-if="type === 'radio'">
    <el-radio-group
      :disabled="disabled"
      @update:modelValue="updateValue"
      :model-value="modelValue"
    >
      <el-radio
        v-for="item in data"
        :key="item.keyIndex"
        :value="item.value"
        :label="item.label"
      />
    </el-radio-group>
  </template>
  <el-checkbox-group
    v-else-if="type === 'checkbox'"
    :disabled="disabled"
    @update:modelValue="updateValue"
    :model-value="modelValue"
  >
    <el-checkbox
      v-for="item in data"
      :key="item.keyIndex"
      :value="item.value"
      :label="item.label"
    />
  </el-checkbox-group>
  <el-select
    v-else
    @update:modelValue="updateValue"
    :model-value="modelValue"
    :clearable="true"
    :disabled="disabled"
    :placeholder="placeholder"
    :multiple="type === 'multipleSelect'"
    :teleported="teleported"
    :filterable="filterable"
    class="ele-fluid"
  >
    <el-option
      v-for="item in data"
      :key="item.keyIndex"
      :value="item.value"
      :label="item.label"
    />
  </el-select>
</template>

<script setup>
  import { computed } from 'vue';
  import { useDictStore } from '@/store/modules/dict';
  import { isBoolean, isNumber } from '@/utils/is';
  const emit = defineEmits(['update:modelValue']);
  const useDict = useDictStore();
  const props = defineProps({
    /** 字典值 */
    modelValue: [String, Number, Boolean, Array],
    /** 字典类型 */
    code: String,
    /** 组件类型 */
    type: String,
    /** 是否禁用 */
    disabled: Boolean,
    /** 提示文本 */
    placeholder: String,
    /** select的下拉是否插入到body下 */
    teleported: {
      type: Boolean,
      default: true
    },
    /** select是否可搜索 */
    filterable: Boolean
  });

  /** 字典数据 */
  const data = useDict.getDictByType(props.code);
  /** 绑定值对应的字典数据 */
  const valueData = computed(() => {
    const result = [];
    const val = props.modelValue;
    if (val == null || val === '') {
      return result;
    }
    const values = Array.isArray(val) ? val : [val];
    values.forEach((v) => {
      const temp = data.find((d) => {
        if (isNumber(v) || isBoolean(v)) {
          return d.value == [String(v)];
        } else {
          return d.value == v;
        }
      });
      if (temp != null) {
        result.push(temp);
      } else {
        result.push({ keyIndex: v, value: v, label: v });
      }
    });
    return result;
  });

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };
</script>
