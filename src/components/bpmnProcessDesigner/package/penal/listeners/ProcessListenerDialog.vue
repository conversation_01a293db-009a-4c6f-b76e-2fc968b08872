<!-- 执行器选择 -->
<template>
  <el-dialog title="请选择监听器" v-model="dialogVisible" width="1024px">
    <ElCard>
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="ProcessListenerDialogTable"
      >
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BPM_PROCESS_LISTENER_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #valueType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BPM_PROCESS_LISTENER_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #action="{ row }">
          <el-button link type="primary" @click="select(row)"> 选择 </el-button>
        </template>
      </ele-pro-table>
    </ElCard>
  </el-dialog>
</template>
<script setup lang="ts">
  import { ProcessListenerApi } from '@/api/bpm/processListener';
  import { DICT_TYPE } from '@/utils/dict';
  import { CommonStatusEnum } from '@/utils/constants';

  /** BPM 流程 表单 */
  defineOptions({ name: 'ProcessListenerDialog' });
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '名字',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'type',
        label: '类型',
        align: 'center',
        minWidth: 110,
        slot: 'type'
      },
      {
        prop: 'event',
        label: '事件',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'valueType',
        label: '值类型',
        width: 140,
        align: 'center',
        slot: 'valueType'
      },
      {
        prop: 'value',
        label: '值',
        width: 90,
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ProcessListenerApi.getProcessListenerPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };
  const dialogVisible = ref(false); // 弹窗的是否展示

  /** 打开弹窗 */
  const open = async (type: string) => {
    reload();
    dialogVisible.value = true;
  };
  defineExpose({ open }); // 提供 open 方法，用于打开弹窗

  /** 提交表单 */
  const emit = defineEmits(['success']); // 定义 success 事件，用于操作成功后的回调
  const select = async (row) => {
    dialogVisible.value = false;
    // 发送操作成功的事件
    emit('select', row);
  };
</script>
