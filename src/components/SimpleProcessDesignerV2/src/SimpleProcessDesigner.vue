<template>
  <div v-loading="loading" class="overflow-auto">
    <SimpleProcessModel
      v-if="processNodeTree"
      :flow-node="processNodeTree"
      :readonly="false"
      @save="saveSimpleFlowModel"
    />
    <el-dialog
      v-model="errorDialogVisible"
      title="保存失败"
      width="400"
      :fullscreen="false"
    >
      <div class="mb-2">以下节点内容不完善，请修改后保存</div>
      <div
        class="mb-3 b-rounded-1 bg-gray-100 p-2 line-height-normal"
        v-for="(item, index) in errorNodes"
        :key="index"
      >
        {{ item.name }} : {{ NODE_DEFAULT_TEXT.get(item.type) }}
      </div>
      <template #footer>
        <el-button type="primary" @click="errorDialogVisible = false"
          >知道了</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import SimpleProcessModel from './SimpleProcessModel.vue';
  import { updateBpmSimpleModel, getBpmSimpleModel } from '@/api/bpm/simple';
  import {
    SimpleFlowNode,
    NodeType,
    NodeId,
    NODE_DEFAULT_TEXT
  } from './consts';
  import { getModel } from '@/api/bpm/model';
  import { getForm, FormVO } from '@/api/bpm/form';
  import { handleTree } from '@/utils/tree';
  import * as RoleApi from '@/api/system/role';
  import * as DeptApi from '@/api/system/dept';
  import * as PostApi from '@/api/system/post';
  import * as UserApi from '@/api/system/user';
  import * as UserGroupApi from '@/api/bpm/userGroup';

  defineOptions({
    name: 'SimpleProcessDesigner'
  });
  const emits = defineEmits(['success']); // 保存成功事件

  const props = defineProps({
    modelId: {
      type: String,
      required: true
    }
  });

  const loading = ref(false);
  const formFields = ref<string[]>([]);
  const formType = ref(20);
  const roleOptions = ref<RoleApi.RoleVO[]>([]); // 角色列表
  const postOptions = ref<PostApi.PostVO[]>([]); // 岗位列表
  const userOptions = ref<UserApi.UserVO[]>([]); // 用户列表
  const deptOptions = ref<DeptApi.DeptVO[]>([]); // 部门列表
  const deptTreeOptions = ref();
  const userGroupOptions = ref<UserGroupApi.UserGroupVO[]>([]); // 用户组列表
  provide('formFields', formFields);
  provide('formType', formType);
  provide('roleList', roleOptions);
  provide('postList', postOptions);
  provide('userList', userOptions);
  provide('deptList', deptOptions);
  provide('userGroupList', userGroupOptions);
  provide('deptTree', deptTreeOptions);

  const message = useMessage(); // 国际化
  const processNodeTree = ref<SimpleFlowNode | undefined>();
  const errorDialogVisible = ref(false);
  let errorNodes: SimpleFlowNode[] = [];
  const saveSimpleFlowModel = async (simpleModelNode: SimpleFlowNode) => {
    if (!simpleModelNode) {
      message.error('模型数据为空');
      return;
    }
    try {
      loading.value = true;
      const data = {
        id: props.modelId,
        simpleModel: simpleModelNode
      };
      const result = await updateBpmSimpleModel(data);
      if (result) {
        message.success('修改成功');
        emits('success');
      } else {
        message.alert('修改失败');
      }
    } finally {
      loading.value = false;
    }
  };
  // 校验节点设置。 暂时以 showText 为空 未节点错误配置
  const validateNode = (
    node: SimpleFlowNode | undefined,
    errorNodes: SimpleFlowNode[]
  ) => {
    if (node) {
      const { type, showText, conditionNodes } = node;
      if (type == NodeType.END_EVENT_NODE) {
        return;
      }
      if (type == NodeType.START_USER_NODE) {
        // 发起人节点暂时不用校验，直接校验孩子节点
        validateNode(node.childNode, errorNodes);
      }

      if (
        type === NodeType.USER_TASK_NODE ||
        type === NodeType.COPY_TASK_NODE ||
        type === NodeType.CONDITION_NODE
      ) {
        if (!showText) {
          errorNodes.push(node);
        }
        validateNode(node.childNode, errorNodes);
      }

      if (
        type == NodeType.CONDITION_BRANCH_NODE ||
        type == NodeType.PARALLEL_BRANCH_NODE ||
        type == NodeType.INCLUSIVE_BRANCH_NODE
      ) {
        // 分支节点
        // 1. 先校验各个分支
        conditionNodes?.forEach((item) => {
          validateNode(item, errorNodes);
        });
        // 2. 校验孩子节点
        validateNode(node.childNode, errorNodes);
      }
    }
  };

  onMounted(async () => {
    try {
      loading.value = true;
      // 获取表单字段
      const bpmnModel = await getModel(props.modelId);
      if (bpmnModel) {
        formType.value = bpmnModel.formType;
        if (formType.value === 10) {
          const bpmnForm = (await getForm(
            bpmnModel.formId
          )) as unknown as FormVO;
          formFields.value = bpmnForm?.fields;
        }
      }
      // 获得角色列表
      roleOptions.value = await RoleApi.getSimpleRoleList();
      // 获得岗位列表
      postOptions.value = await PostApi.getSimplePostList();
      // 获得用户列表
      userOptions.value = await UserApi.getSimpleUserByTypeList([1,2]);
      // 获得部门列表
      deptOptions.value = await DeptApi.getSimpleDeptList();

      deptTreeOptions.value = handleTree(
        deptOptions.value as DeptApi.DeptVO[],
        'id'
      );
      // 获取用户组列表
      userGroupOptions.value = await UserGroupApi.getUserGroupSimpleList();

      //获取 SIMPLE 设计器模型
      const result = await getBpmSimpleModel(props.modelId);
      if (result) {
        processNodeTree.value = result;
      } else {
        // 初始值
        processNodeTree.value = {
          name: '发起人',
          type: NodeType.START_USER_NODE,
          id: NodeId.START_USER_NODE_ID,
          childNode: {
            id: NodeId.END_EVENT_NODE_ID,
            name: '结束',
            type: NodeType.END_EVENT_NODE
          }
        };
      }
    } finally {
      loading.value = false;
    }
  });
</script>
