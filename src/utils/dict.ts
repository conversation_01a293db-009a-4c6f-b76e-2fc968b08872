/**
 * 数据字典工具类
 */
import { useDictStore } from '@/store/modules/dict';

export const getDictOptions = (dictType) => {
  const dictStore = useDictStore();
  return dictStore.getDictByType(dictType) || [];
};

export const getIntDictOptions = (dictType) => {
  // 获得通用的 DictDataType 列表
  const dictOptions = getDictOptions(dictType);
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption = [];
  dictOptions.forEach((dict) => {
    dictOption.push({
      ...dict,
      value: parseInt(dict.value + '')
    });
  });
  return dictOption;
};

export const getStrDictOptions = (dictType) => {
  // 获得通用的 DictDataType 列表
  const dictOptions = getDictOptions(dictType);
  // 转换成 string 类型的 StringDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getStrDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption = [];
  dictOptions.forEach((dict) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    });
  });
  return dictOption;
};

export const getBoolDictOptions = (dictType) => {
  const dictOption = [];
  const dictOptions = getDictOptions(dictType);
  dictOptions.forEach((dict) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    });
  });
  return dictOption;
};

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType, value) => {
  const dictOptions = getDictOptions(dictType);
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict;
    }
  }
};

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType, value) => {
  const dictOptions = getDictOptions(dictType);
  const dictLabel = ref('');
  dictOptions.forEach((dict) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label;
    }
  });
  return dictLabel.value;
};

export const DICT_TYPE = {
  USER_TYPE: 'user_type',
  COMMON_STATUS: 'common_status',
  COMMON_APPROVE_STATUS: 'common_approve_status',
  APPROVE_METHODS: 'approve_method',
  TERMINAL: 'terminal', // 终端
  DATE_INTERVAL: 'date_interval', // 数据间隔
  MINI_LINK_TYPE: 'mini_link_type',

  // ========== SYSTEM 模块 ==========
  WX_MSG_TYPE: 'system_wx_msg_type',
  SYSTEM_WX_TYPE: 'system_wx_type',
  SYSTEM_USER_SEX: 'system_user_sex',
  SYSTEM_MENU_TYPE: 'system_menu_type',
  SYSTEM_ROLE_TYPE: 'system_role_type',
  SYSTEM_DATA_SCOPE: 'system_data_scope',
  SYSTEM_NOTICE_TYPE: 'system_notice_type',
  SYSTEM_LOGIN_TYPE: 'system_login_type',
  SYSTEM_LOGIN_RESULT: 'system_login_result',
  SYSTEM_SMS_CHANNEL_CODE: 'system_sms_channel_code',
  SYSTEM_SMS_TEMPLATE_TYPE: 'system_sms_template_type',
  SYSTEM_SMS_SEND_STATUS: 'system_sms_send_status',
  SYSTEM_SMS_RECEIVE_STATUS: 'system_sms_receive_status',
  SYSTEM_OAUTH2_GRANT_TYPE: 'system_oauth2_grant_type',
  SYSTEM_MAIL_SEND_STATUS: 'system_mail_send_status',
  SYSTEM_WX_SEND_STATUS: 'system_wx_send_status',
  SYSTEM_NOTIFY_TEMPLATE_TYPE: 'system_notify_template_type',
  SYSTEM_SOCIAL_TYPE: 'system_social_type',
  SYSTEM_ENABLE_STATUS: 'system_enable_status',

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING: 'infra_boolean_string',
  INFRA_JOB_STATUS: 'infra_job_status',
  INFRA_JOB_LOG_STATUS: 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS: 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE: 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE: 'infra_codegen_template_type',
  INFRA_CODEGEN_FRONT_TYPE: 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE: 'infra_codegen_scene',
  INFRA_FILE_STORAGE: 'infra_file_storage',
  INFRA_OPERATE_TYPE: 'infra_operate_type',

  // ========== BPM 模块 ==========
  BPM_MODEL_TYPE: 'bpm_model_type',
  BPM_MODEL_FORM_TYPE: 'bpm_model_form_type',
  BPM_TASK_CANDIDATE_STRATEGY: 'bpm_task_candidate_strategy',
  BPM_PROCESS_INSTANCE_STATUS: 'bpm_process_instance_status',
  BPM_TASK_STATUS: 'bpm_task_status',
  BPM_OA_LEAVE_TYPE: 'bpm_oa_leave_type',
  BPM_PROCESS_LISTENER_TYPE: 'bpm_process_listener_type',
  BPM_PROCESS_LISTENER_VALUE_TYPE: 'bpm_process_listener_value_type',

  // ========== 合同 模块 ==========
  BUSINESS_OPPORTUNITY_SOURCE: 'co_business_opportunity_source',
  BUSINESS_OPPORTUNITY_STAGE: 'co_business_opportunity_stage',
  CO_CUSTOMER_TYPE: 'co_customer_type',
  CO_SECTOR: 'co_sector',
  CO_CUSTOMER_STATUS: 'co_customer_status',
  CO_RUN_STATUS: 'co_run_status',
  CO_COMPANY_TYPE: 'co_company_type',
  CO_INTENTION_LEVEL: 'co_intention_level',
  CO_FOLLOW_TYPE: 'co_follow_type',
  CO_SOURCE_NETHOD: 'co_source_method',
  CO_STATUS: 'co_status',
  // ========== 预存金 模块 ==========
  CO_PREPAID_BILL_STATUS: 'co_prepaid_bill_status',
  CO_PREPAID_ORDER_STATUS: 'co_prepaid_order_status',
  CO_PREPAID_ORDER_OP_SUB_TYPE: 'co_prepaid_order_op_sub_type',
  CO_PREPAID_ORDER_OP_TYPE: 'co_prepaid_order_op_type',
  CO_PREPAID_ORDER_TYPE: 'co_prepaid_order_type',
  CO_PREPAID_ORDER_IS_INVOICE: 'co_prepaid_order_is_invoice',
  CO_PREPAID_INVOICE_STATUS: 'co_prepaid_invoice_status',
  CO_PREPAID_INVOICE_TYPE: 'co_prepaid_invoice_type',

  CO_PREPAID_MESSAGE_TYPE: 'co_prepaid_message_type',
  CO_PREPAID_REMIND_OBJECT: 'co_prepaid_remind_object',
  CO_PREPAID_REFUND_METHOD: 'co_prepaid_refund_method',
  CO_PREPAID_TYPE: 'co_prepaid_type',

  // ========== 押金 模块 ==========
  CO_DEPOSIT_STATUS: 'co_deposit_status', //押金-状态
  CO_DEPOSIT_OPERA_TYPE: 'co_deposit_opera_type', //押金-操作类型
  CO_DEPOSIT_RECEIVE_TYPE: 'co_deposit_receive_type', //押金-收款方式
  CO_DEPOSIT_REFUND_TYPE: 'co_deposit_refund_type', //押金-退款方式

  // ========== 长租房 模块 ==========
  CO_LONG_HOUSE_STATUS: 'co_long_house_status',
  CO_LONG_HOUSE_TYPE: 'co_long_house_type',

  // ========== 合同&长租房合同 模块 ==========
  CO_CONTRACT_STATUS: 'co_contract_status',
  CO_CONTRACT_PAY_STATUS: 'co_contract_pay_status',
  CO_CONTRACT_SIGN_CHANNEL: 'co_contract_sign_channel',
  CO_CONTRACT_TYPE: 'co_contract_type',
  CO_CONTRACT_RENTAL_PLAN: 'co_contract_rental_plan',
  // ========== 小程序 模块 ==========
  PARK_CAROUSEL_TYPE: 'park_carousel_type',
  PARK_SERVER_TYPE: 'park_server_type',
  PARK_HOT_FLAG: 'park_hot_flag',

  // ========== 小程序 模块 ==========
  CO_BILL_CONTRACT_TYPE: 'co_bill_contract_type',
  CO_CONTRACT_PAY_TYPE: 'co_contract_pay_type',
  CO_CONTRACT_PAY_SYN_STATUS: 'co_contract_pay_syn_status',
  CO_CONTRACT_IS_OVERDUE: 'co_contract_is_overdue',

  // ========== 预存金 模块 ==========
  CO_METER_READING_TYPE: 'co_meter_reading_type',

  CO_SYN_STATUS: 'co_syn_status',

  // ========== 账单退款 模块 ==========
  CO_REFUND_BILL_STATUS: 'co_refund_bill_status',
  CO_REFUND_BILL_TYPE: 'co_refund_bill_type',
  CO_CONTRACT_THROWLEASE_REASON: 'co_contract_throwLease_reason',

  // ========== 商机 模块 ==========
  CO_OPPORTUNITY_APPROVE_STATUS: 'co_opportunity_approve_status',

  // =========== 招待金 模块 ============
  CO_HOSPITALITY_AMT_GRANT_STATUS: 'co_hospitality_amt_grant_status',
  CO_HOSPITALITY_AMT_CUT_STATUS: 'co_hospitality_amt_cut_status',
  CO_HOSPITALITY_AMT_APPROVE_STATUS: 'co_hospitality_amt_approve_status'
};
