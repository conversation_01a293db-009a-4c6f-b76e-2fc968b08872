/**
 * axios实例
 */
import axios from 'axios';
import qs from 'qs';
import { ElMessageBox, ElMessage, ElNotification } from 'element-plus/es';
import { logout, toURLSearch } from '../common';
import errorCode from './errorCode';
import { config } from './config';
import * as SsoApi from '@/api/system/oauth2/sso';

import {
  getAccessToken,
  getSsoAccessToken,
  getRefreshToken,
  getTenantId,
  setToken
} from '@/utils/auth';
const tenantEnable = import.meta.env.VITE_APP_TENANT_ENABLE;
const { result_code, base_url, request_timeout } = config;

/** 创建axios实例 */
const service = axios.create({
  baseURL: base_url,
  timeout: request_timeout, // 请求超时时间
  withCredentials: false // 禁用 Cookie 等信息
});
// 请求队列
let requestList = [];
// 需要忽略的提示。忽略后，自动 Promise.reject('error')
const ignoreMsgs = [
  '无效的刷新令牌', // 刷新令牌被删除时，不用提示
  '刷新令牌已过期' // 使用刷新令牌，刷新获取新的访问令牌时，结果因为过期失败，此时需要忽略。否则，会导致继续 401，无法跳转到登出界面
];
let isRefreshToken = false;
// 请求白名单，无须token的接口
const whiteList = ['/login', '/refresh-token'];
/**
 * 添加请求拦截器
 */
service.interceptors.request.use(
  (config) => {
    // 是否需要设置 token
    let isToken = (config.headers || {}).isToken === false;
    whiteList.some((v) => {
      if (config.url) {
        config.url.indexOf(v) > -1;
        return (isToken = false);
      }
    });
    if (getSsoAccessToken) {
      config.headers['wehealplus-sso-token'] = getSsoAccessToken(); // 让每个请求携带自定义token
    }
    if (getAccessToken() && !isToken) {
      config.headers.Authorization = 'Bearer ' + getAccessToken(); // 让每个请求携带自定义token
    }
    // 设置租户
    if (tenantEnable && tenantEnable === 'true') {
      const tenantId = getTenantId();
      if (tenantId) config.headers['tenant-id'] = tenantId;
    }
    const params = config.params || {};
    const data = config.data || false;
    if (
      config.method?.toUpperCase() === 'POST' &&
      config.headers['Content-Type'] === 'application/x-www-form-urlencoded'
    ) {
      config.data = qs.stringify(data);
    }
    // get参数编码
    if (config.method?.toUpperCase() === 'GET' && params) {
      config.params = {};
      const paramsStr = toURLSearch(params, null);
      if (paramsStr) {
        config.url = config.url + '?' + paramsStr;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 添加响应拦截器
 */
service.interceptors.response.use(
  async (response) => {
    let { data } = response;
    const config = response.config;
    if (!data) {
      // 返回“[HTTP]请求没有返回值”;
      throw new Error();
    }
    // 未设置状态码则默认成功状态
    // 二进制数据则直接返回，例如说 Excel 导出
    if (
      response.request.responseType === 'blob' ||
      response.request.responseType === 'arraybuffer'
    ) {
      // 注意：如果导出的响应为 json，说明可能失败了，不直接返回进行下载
      if (response.data.type !== 'application/json') {
        return response.data;
      }
      data = await new Response(response.data).json();
    }
    const code = data.code || result_code;
    // 获取错误信息
    const msg = data.msg || errorCode[code] || errorCode['default'];
    if (ignoreMsgs.indexOf(msg) !== -1) {
      // 如果是忽略的错误码，直接返回 msg 异常
      return Promise.reject(msg);
    } else if (code === 401) {
      // 如果未认证，并且未进行刷新令牌，说明可能是访问令牌过期了
      if (!isRefreshToken) {
        isRefreshToken = true;
        // 1. 如果获取不到刷新令牌，则只能执行登出操作
        if (!getRefreshToken()) {
          return handleAuthorized();
        }
        // 2. 进行刷新访问令牌
        try {
          const refreshTokenRes = await refreshToken();
          // 2.1 刷新成功，则回放队列的请求 + 当前请求
          setToken((await refreshTokenRes).data.data);
          config.headers.Authorization = 'Bearer ' + getAccessToken();
          requestList.forEach((cb) => {
            cb();
          });
          requestList = [];
          return service(config);
        } catch (e) {
          // 为什么需要 catch 异常呢？刷新失败时，请求因为 Promise.reject 触发异常。
          // 2.2 刷新失败，只回放队列的请求
          requestList.forEach((cb) => {
            cb();
          });
          // 提示是否要登出。即不回放当前请求！不然会形成递归
          return handleAuthorized();
        } finally {
          requestList = [];
          isRefreshToken = false;
        }
      } else {
        // 添加到队列，等待刷新获取到新的令牌
        return new Promise((resolve) => {
          requestList.push(() => {
            config.headers.Authorization = 'Bearer ' + getAccessToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
            resolve(service(config));
          });
        });
      }
    } else if (code === 500) {
      ElMessage.error('服务器错误,请联系管理员!');
      return Promise.reject(new Error(msg));
    } else if (code === 901) {
      ElMessage.error({
        offset: 300,
        dangerouslyUseHTMLString: true,
        message: '服务器错误,请联系管理员!'
      });
      return Promise.reject(new Error(msg));
    } else if (code === 15) {
      //未登录或者过期了
      //先获取refreshToken
      if (localStorage.getItem('refreshToken')) {
        //去尝试刷新token,刷不到的话就要退出
        const result = await SsoApi.getSsoRefreshToken(
          localStorage.getItem('refreshToken')
        );
        localStorage.setItem('accessToken', result.accessToken);
        localStorage.setItem('refreshToken', result.refreshToken);
        config.headers['wehealplus-sso-token'] = result.accessToken;
        requestList.forEach((cb) => {
          cb();
        });
        requestList = [];
        return service(config);
      } else {
        //失败了。需要重定向到登录页面了
        const ssoUrl = await SsoApi.getSsoUrl();
        window.location.href = ssoUrl;
      }
      return handleAuthorized();
    } else if (code === -1||code === 10) {
      handleAuthorized();
      //refreshtoken也失效
    } else if (code !== 200 && code !== 1) {
      if (msg === '无效的刷新令牌') {
        // hard coding：忽略这个提示，直接登出
        return handleAuthorized();
      } else {
        ElNotification.error({ title: msg });
      }
      return Promise.reject('error');
    } else {
      return data;
    }
  },
  (error) => {
    let { message } = error;
    if (message === 'Network Error') {
      message = '操作失败,系统异常!';
    } else if (message.includes('timeout')) {
      message = '接口请求超时,请刷新页面重试!';
    } else if (message.includes('Request failed with status code')) {
      message = '请求出错，请稍候重试' + message.substr(message.length - 3);
    }
    ElMessage.error(message);
    return Promise.reject(error);
  }
);
const refreshToken = async () => {
  axios.defaults.headers.common['tenant-id'] = getTenantId();
  return await axios.post(
    base_url + '/system/auth/refresh-token?refreshToken=' + getRefreshToken()
  );
};
const handleAuthorized = () => {
  ElMessageBox.close();
  ElMessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
    confirmButtonText: '重新登录',
    callback: (action) => {
      if (action === 'confirm') {
        logout();
      }
    },
    type: 'warning',
    draggable: true
  });
};
export { service };
