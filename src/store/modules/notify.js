/**
 * 登录用户状态管理
 */
import { defineStore } from 'pinia';
import * as NotifyMessageApi from '@/api/system/notify/message';

export const useNotifyStore = defineStore('notify', {
  state: () => ({
    /** 未读消息数 */
    unreadCount: 0
  }),
  getters: {
    getUnreadCount() {
      return this.unreadCount;
    }
  },
  actions: {
    /**
     * 更新未读消息数
     */
    async setUnreadCount() {
      const count = await NotifyMessageApi.getUnreadNotifyMessageCount();
      this.unreadCount = count;
    }
  }
});
