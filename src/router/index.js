/**
 * 路由配置
 */
import NProgress from 'nprogress';
import { createRouter, createWebHistory } from 'vue-router';
import { WHITE_LIST, REDIRECT_PATH, LAYOUT_PATH } from '@/config/setting';
import { useUserStore } from '@/store/modules/user';
import { useDictStore } from '@/store/modules/dict';
import * as authUtil from '@/utils/auth';
import { setPageTitle } from '@/utils/page-title-util';
import { getSsoAccessToken } from '@/utils/auth';
import { routes, getMenuRoutes } from './routes';
import * as SsoApi from '@/api/system/oauth2/sso';
NProgress.configure({
  speed: 200,
  minimum: 0.02,
  trickleSpeed: 200,
  showSpinner: false
});
const ssoEnable = import.meta.env.VITE_APP_SSO_ENABLE;
const router = createRouter({
  routes,
  history: createWebHistory(),
  scrollBehavior: () => {
    return { top: 0 };
  }
});
// 从url中获取指定名称的参数值
const getParam = (name) => {
  var query = window.location.search.substring(1);
  var vars = query.split('&');
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=');
    if (pair[0] == name) {
      return pair[1];
    }
  }
  return null;
};
const getSsoTokenFun = async (code) => {
  try {
    const result = await SsoApi.getSsoToken(code);
    localStorage.setItem('accessToken', result.accessToken);
    localStorage.setItem('refreshToken', result.refreshToken);
    return true;
  } catch (e) {
    return false;
  }
};
const removeCodeParamAndRedirect = async () => {
  var url = new URL(window.location.href);
  url.searchParams.delete('ssoCode');
  window.location.href = url.toString();
};
const ssoLogin = async () => {
  authUtil.setTenantId(1);
  const res = await SsoApi.getSsoUseInfo();
  authUtil.setToken(res);
};
/**
 * 路由守卫
 */
router.beforeEach(async (to) => {
  if (!to.path.includes(REDIRECT_PATH)) {
    NProgress.start();
    setPageTitle(to.meta?.title);
  }
  //加上单点登录逻辑
  //1.判断是否开启单点登录，开启的话走单点逻辑
  if (ssoEnable === 'true') {
    const ssoToken = getSsoAccessToken();
    //如果拿到了登录标识，则需要判断下在当前系统是否登录
    //拿到了就是登录，不用跳转
    if (ssoToken) {
      //需要获取用户的信息
      if(!authUtil.getAccessToken()){
        await ssoLogin();
      }
      //结束，先不处理
    } else {
      //没拿到，去尝试获取授权码
      var code = getParam('ssoCode');
      if (code && (await getSsoTokenFun(code))) {
        //获取到了并重定向了首页
        await removeCodeParamAndRedirect();
      }
      const ssoUrl = await SsoApi.getSsoUrl();
      window.location.href = ssoUrl;
    }
  }
  //未开启单点登录
  else {
    if (!authUtil.getAccessToken()) {
      // 未登录跳转登录界面
      if (!WHITE_LIST.includes(to.path)) {
        const query = { from: encodeURIComponent(to.fullPath) };
        return { path: '/login', query: to.path === LAYOUT_PATH ? {} : query };
      }
      return;
    }
  }
  // 获取所有字典

  //注册动态路由
  const userStore = useUserStore();
  const dictStore = useDictStore();
  if (!userStore.menus) {
    if (!dictStore.getIsSetDict) {
      await dictStore.setDictMap();
    }
    const { menus, homePath } = await userStore.fetchUserInfo();
    if (menus) {
      getMenuRoutes(menus, homePath).forEach((r) => {
        router.addRoute(r);
      });
      return { ...to, replace: true };
    }
  }
});

router.afterEach((to) => {
  if (!to.path.includes(REDIRECT_PATH) && NProgress.isStarted()) {
    setTimeout(() => {
      NProgress.done(true);
    }, 200);
  }
});

export default router;
