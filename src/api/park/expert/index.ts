import request from '@/utils/request/server';

// 创建专家
export const createExpert = async (data) => {
  return await request.post({
    url: '/cux/expert/create',
    data: data
  });
};

// 更新专家
export const updateExpert = async (data) => {
  return await request.put({
    url: '/cux/expert/update',
    data: data
  });
};

// 删除专家
export const deleteExpert = async (id: number) => {
  return await request.delete({
    url: '/cux/expert/delete?id=' + id
  });
};

// 获得专家
export const getExpert = async (id: number) => {
  return await request.get({
    url: '/cux/expert/get?id=' + id
  });
};
