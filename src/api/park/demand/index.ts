import request from '@/utils/request/server';

// 创建需求
export const createDemand = async (data) => {
  return await request.post({
    url: '/cux/demand/create',
    data: data
  });
};

// 更新需求
export const updateDemand = async (data) => {
  return await request.put({
    url: '/cux/demand/update',
    data: data
  });
};

// 删除需求
export const deleteDemand = async (id: number) => {
  return await request.delete({
    url: '/cux/demand/delete?id=' + id
  });
};

// 获得需求详细
export const getDemand = async (id: number) => {
  return await request.get({
    url: '/cux/demand/get?id=' + id
  });
};