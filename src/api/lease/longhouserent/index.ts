import request from '@/utils/request/server';

// 长租房出租信息 VO
export interface LongHouseRentVO {
  houseId: number // 房屋id
  contractId: number // 合同id
  customerId: number // 客户id
  terminationTime: Date // 退租时间
  price: number // 价格
  contractPeriod: string // 合同周期
  rentStatus: number // 房屋状态 1-待出租 2-待入住 3-出租中 4-退租中 5-已完成
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 长租房出租信息 API
  // 查询长租房出租信息分页
export const getLongHouseRentPage = async (params: any) => {
    return await request.get({ url: `/lease/long-house-rent/page`, params })
};

  // 查询长租房出租信息详情
export const getLongHouseRent = async (id: number) => {
    return await request.get({ url: `/lease/long-house-rent/get?id=` + id })
};

  // 新增长租房出租信息
export const createLongHouseRent = async (data: LongHouseRentVO) => {
    return await request.post({ url: `/lease/long-house-rent/create`, data })
};

  // 修改长租房出租信息
export const updateLongHouseRent = async (data: LongHouseRentVO) => {
    return await request.put({ url: `/lease/long-house-rent/update`, data })
};

  // 删除长租房出租信息
export const deleteLongHouseRent = async (id: number) => {
    return await request.delete({ url: `/lease/long-house-rent/delete?id=` + id })
};

  // 导出长租房出租信息 Excel
export const exportLongHouseRent = async (params) => {
    return await request.download({ url: `/lease/long-house-rent/export-excel`, params })
};
