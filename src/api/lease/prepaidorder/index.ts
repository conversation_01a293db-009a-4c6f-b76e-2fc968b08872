import request from '@/utils/request/server';

// 预存金订单 VO
export interface PrepaidOrderVO {
  id: number // id
  orderNo: string // 订单编号
  prepaidId: number // 预存金ID
  customerId: number // 客户ID
  customerCode: string // 客户编号
  customerName: string // 客户名称
  socialCreditCode: string // 统一社会信用代码
  type: number // 操作类型, 1-增加; 2-减少;
  opType: number // 操作类型, 1-充值; 2-扣款; 3-退款;
  opSubType: number // 操作子类型, 1-充值; 2-物业缴费; 3-合同缴费; 4-合同退款;
  amount: number // 应收金额
  actualAmount: number // 实收金额
  relType: number // 关联类型
  relId: string // 关联标识
  relInfo: string // 关联信息
  relStatus: number // 关联状态
  status: number // 状态, 1-待确认; 2-处理中; 3-成功; 4-失败;
  startTime: Date // 开始时间
  endTime: Date // 结束时间
  tradeTime: Date // 交易时间
  paymentMethod: string // 收款方式
  paymentAccount: string // 收款账号
  paymentPayee: string // 收款人
  paymentDetail: string // 收款明细
  isSuccess: number // 是否成功
  thirdOrderNo: string // 三方订单编号
  callbackMsg: string // 回调信息
  isInvoice: number // 是否开票
  invoiceId: number // 开票ID
  extend: string // 扩展信息
  remark: string // 备注
}

// 预存金订单 API
  // 查询预存金订单分页
export const getPrepaidOrderPage = async (params: any) => {
    return await request.get({ url: `/lease/prepaid-order-new/page`, params })
};
//查询预存金订单数据，该处必须要传预存金ID，不然后台查询会返回空
export const getPrepaidOrderList = async (params: any) => {
  return await request.get({ url: `/lease/prepaid-order-new/list`, params })
};

  // 查询预存金订单详情
export const getPrepaidOrder = async (id: number) => {
    return await request.get({ url: `/lease/prepaid-order-new/get?id=` + id })
};

  // 新增预存金订单
export const createPrepaidOrder = async (data: PrepaidOrderVO) => {
    return await request.post({ url: `/lease/prepaid-order-new/create`, data })
};

  // 修改预存金订单
export const updatePrepaidOrder = async (data: PrepaidOrderVO) => {
    return await request.put({ url: `/lease/prepaid-order-new/update`, data })
};

  // 删除预存金订单
export const deletePrepaidOrder = async (id: number) => {
    return await request.delete({ url: `/lease/prepaid-order-new/delete?id=` + id })
};

  // 导出预存金订单 Excel
export const exportPrepaidOrder = async (params) => {
    return await request.download({ url: `/lease/prepaid-order-new/export-excel`, params })
};
