import request from '@/utils/request/server';

// 长租房合同房产信息 VO
export interface LongContractHouseVO {
  id: number // id
  longContractId: number // 合同ID
  spaceType: number // 空间类型
  spaceId: number // 空间ID
  houseId: number // 房产信息id
  area: number // 建筑面积
  rentalArea: number // 租赁面积
}

// 长租房合同房产信息 API
  // 查询长租房合同房产信息分页
export const getLongContractHousePage = async (params: any) => {
    return await request.get({ url: `/lease/long-contract-house/page`, params })
};

  // 查询长租房合同房产信息详情
export const getLongContractHouse = async (id: number) => {
    return await request.get({ url: `/lease/long-contract-house/get?id=` + id })
};

  // 新增长租房合同房产信息
export const createLongContractHouse = async (data: LongContractHouseVO) => {
    return await request.post({ url: `/lease/long-contract-house/create`, data })
};

  // 修改长租房合同房产信息
export const updateLongContractHouse = async (data: LongContractHouseVO) => {
    return await request.put({ url: `/lease/long-contract-house/update`, data })
};

  // 删除长租房合同房产信息
export const deleteLongContractHouse = async (id: number) => {
    return await request.delete({ url: `/lease/long-contract-house/delete?id=` + id })
};

  // 导出长租房合同房产信息 Excel
export const exportLongContractHouse = async (params) => {
    return await request.download({ url: `/lease/long-contract-house/export-excel`, params })
};
