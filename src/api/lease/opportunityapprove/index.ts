import request from '@/utils/request/server';

// 商机审批 VO
export interface OpportunityApproveVO {
  id: number // id
  opportunityId: string // 商机id
  approveStatus: number // 审批-状态 1-待提交，2-审批中，3-审批通过，4-审批拒绝
  approveMessage: string // 审批消息
  approveTime: Date // 审批时间
  submmitTime: Date // 提交时间
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  companyKey: string // 公司key
  approveFlag: string // 审批动作
}

// 商机审批 API
  // 查询商机审批分页
export const getOpportunityApprovePage = async (params: any) => {
    return await request.get({ url: `/lease/opportunity-approve/page`, params })
};

  // 查询商机审批详情
export const getOpportunityApprove = async (id: number) => {
    return await request.get({ url: `/lease/opportunity-approve/get?id=` + id })
};

  // 新增商机审批
export const createOpportunityApprove = async (data: OpportunityApproveVO) => {
    return await request.post({ url: `/lease/opportunity-approve/create`, data })
};

  // 修改商机审批
export const updateOpportunityApprove = async (data: OpportunityApproveVO) => {
    return await request.put({ url: `/lease/opportunity-approve/update`, data })
};

  // 删除商机审批
export const deleteOpportunityApprove = async (id: number) => {
    return await request.delete({ url: `/lease/opportunity-approve/delete?id=` + id })
};

  // 导出商机审批 Excel
export const exportOpportunityApprove = async (params) => {
    return await request.download({ url: `/lease/opportunity-approve/export-excel`, params })
};

  // 审批商机审批 Excel
  export const approveOpportunityApprove = async (data: OpportunityApproveVO) => {
    return await request.post({ url: `/lease/opportunity-approve/approve`, data })
};
