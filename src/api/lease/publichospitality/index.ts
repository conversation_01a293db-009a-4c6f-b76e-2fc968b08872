import request from '@/utils/request/server';

// 公共招待申请 VO
export interface PublicHospitalityVO {
  id: number // id
  reqReason: string // 申请原因
  peopleNumber: number // 招待人数
  allAmount: number // 招待金额
  hospitalityStandards: number // 招待标准
  status: number // 招待状态, 0-待提交 1-审批中; 2-审批通过; 3-审批拒绝
  distributionStatus: number // 招待金发放状态, 0-待发放 1-发放中; 2-发放成功; 3-发放失败
  distributionAmount: number // 发放金额
  distributionMessage: string // 发放消息
  distributionTime: Date // 发放时间
  reductionStatus: number // 招待金核减状态, 0-待发放 1-发放中; 2-发放成功; 3-发放失败
  reductionAmount: number // 核减金额
  reductionMessage: string // 核减消息
  reductionTime: Date // 核减时间
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  companyKey: string // 公司key
}

// 公共招待申请 API
  // 查询公共招待申请分页
export const getPublicHospitalityPage = async (params: any) => {
    return await request.get({ url: `/lease/public-hospitality/page`, params })
};

  // 查询公共招待申请详情
export const getPublicHospitality = async (id: number) => {
    return await request.get({ url: `/lease/public-hospitality/get?id=` + id })
};

  // 新增公共招待申请
export const createPublicHospitality = async (data: PublicHospitalityVO) => {
    return await request.post({ url: `/lease/public-hospitality/create`, data })
};

  // 修改公共招待申请
export const updatePublicHospitality = async (data: PublicHospitalityVO) => {
    return await request.put({ url: `/lease/public-hospitality/update`, data })
};

  // 删除公共招待申请
export const deletePublicHospitality = async (id: number) => {
    return await request.delete({ url: `/lease/public-hospitality/delete?id=` + id })
};

  // 导出公共招待申请 Excel
export const exportPublicHospitality = async (params) => {
    return await request.download({ url: `/lease/public-hospitality/export-excel`, params })
};

  // 提交公共招待申请
export const submitPublicHospitality = async (data: PublicHospitalityVO) => {
    return await request.post({ url: `/lease/public-hospitality/submmit`, data })
};

  // 查询公共招待申请标准
  export const getHospitalityStandards = async () => {
    return await request.get({ url: `/lease/public-hospitality/get-hospitality-standards`})
};
