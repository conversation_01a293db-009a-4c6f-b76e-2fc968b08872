import request from '@/utils/request/server';

// 长租合同付款计划 VO
export interface LongContractPaymentPlanVO {
  id: number // id
  longContractId: number // 合同ID
  code: string // 编号
  paymentType: number // 支付类型，1-计划；2-其他；
  costType: string // 费用类型
  startDate: Date // 开始时间
  endDate: Date // 结束时间
  actualPaymentDate: Date // 实收日期
  paymentDate: Date // 收款日期
  fixedRent: number // 固定租金
  floatingRent: number // 浮动租金
  isOverdue: number // 是否逾期
  status: number // 状态, 1-未收; 2-部分结清; 3-已结清; 4-已作废;
  synStatus: number // 同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败
  synMessage: string // 同步外部信息
  totalAmt: number // 应收总金额
  receivedTotalAmt: number // 已收总金额
  refundTotalAmt: number // 退款总金额
  refundableTotalAmt: number // 应退总金额
  extend: string // 扩展信息
  remark: string // 备注
}

// 长租合同付款计划 API
  // 查询长租合同付款计划分页
export const getLongContractPaymentPlanPage = async (params: any) => {
    return await request.get({ url: `/lease/long-contract-payment-plan/page`, params })
};

  // 查询长租合同付款计划详情
export const getLongContractPaymentPlan = async (id: number) => {
    return await request.get({ url: `/lease/long-contract-payment-plan/get?id=` + id })
};

  // 新增长租合同付款计划
export const createLongContractPaymentPlan = async (data: LongContractPaymentPlanVO) => {
    return await request.post({ url: `/lease/long-contract-payment-plan/create`, data })
};

  // 修改长租合同付款计划
export const updateLongContractPaymentPlan = async (data: LongContractPaymentPlanVO) => {
    return await request.put({ url: `/lease/long-contract-payment-plan/update`, data })
};

  // 删除长租合同付款计划
export const deleteLongContractPaymentPlan = async (id: number) => {
    return await request.delete({ url: `/lease/long-contract-payment-plan/delete?id=` + id })
};

  // 导出长租合同付款计划 Excel
export const exportLongContractPaymentPlan = async (params) => {
    return await request.download({ url: `/lease/long-contract-payment-plan/export-excel`, params })
};
