import request from '@/utils/request/server';

// 押金信息 VO
export interface DepositInfoVO {
  id: number // id
  contractId: number // 合同id
  contractNum: string // 合同编号
  customerId: number // 客户id
  customerNum: string // 客户名称
  shouldReceiveAmount: number // 应收金额
  receiveAmount: number // 收款金额
  lateFee: number // 滞纳金
  shouldRefundAmount: number // 应退金额
  refundAmount: number // 已退金额
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 押金信息 API
  // 查询押金信息分页
export const getDepositInfoPage = async (params: any) => {
    return await request.get({ url: `/lease/deposit-info/page`, params })
};

  // 查询押金信息详情
export const getDepositInfo = async (id: number) => {
    return await request.get({ url: `/lease/deposit-info/get?id=` + id })
};

  // 查询押金信息详情 - all
  export const getDepositAllInfo = async (id: number) => {
    return await request.get({ url: `/lease/deposit-info/get-all?id=` + id })
};

  // 查询押金行信息 - line
  export const getDepositLineByDepositId = async (id: number) => {
    return await request.get({ url: `/lease/deposit-info/get-line?id=` + id })
};

  // 新增押金信息
export const createDepositInfo = async (data: DepositInfoVO) => {
    return await request.post({ url: `/lease/deposit-info/create`, data })
};

  // 修改押金信息
export const updateDepositInfo = async (data: DepositInfoVO) => {
    return await request.put({ url: `/lease/deposit-info/update`, data })
};

  // 删除押金信息
export const deleteDepositInfo = async (id: number) => {
    return await request.delete({ url: `/lease/deposit-info/delete?id=` + id })
};

  // 导出押金信息 Excel
export const exportDepositInfo = async (params) => {
    return await request.download({ url: `/lease/deposit-info/export-excel`, params })
};

  // 新增押金行信息
  export const createDepositLine = async (data: DepositInfoVO) => {
    return await request.post({ url: `/lease/deposit-info/create-deposit-line`, data })
};

// 同步押金账单
  export const synDepositBill = async (data: DepositInfoVO) => {
    return await request.post({ url: `/lease/deposit-info/syn-interface`, data })
};

