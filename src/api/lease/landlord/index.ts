import request from '@/utils/request/server';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
import qs from 'qs';
//房东列表
export const getLandlordPage = async (params: PageParam) => {
  return await request.get({ url: '/lease/landlord', params });
};
//房东列表-新增
export const getLandlordAdd = async (params) => {
  return await request.post({ url: '/lease/landlord', data: params });
};
//房东列表-修改
export const getLandlordEdit = async (params) => {
  return await request.put({
    url: `/lease/landlord/${params.id}`,
    data: params
  });
};
//房东列表-删除
export const getLandlordDel = async (params) => {
  return await request.delete({ url: `/lease/landlord/${params.id}` });
};
//房东基本信息
export const getLandlordDetial = async (params) => {
  return await request.get({ url: `/lease/landlord/${params.id}` });
};
