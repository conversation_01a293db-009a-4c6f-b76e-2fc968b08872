import request from '@/utils/request/server';

// 长租合同 VO
export interface LongContractVO {
  id: number // id
  type: number // 合同类型，1-租入；2-租出
  code: string // 合同编码
  name: string // 合同名称
  status: number // 合同状态, 0-新建 1-待审核; 2-未开始; 3-执行中; 4-已到期; 5-已退租; 6-已作废
  approveStatus: number // 审批状态
  signingDate: Date // 合同签订日期
  signingChannel: number // 合同签订渠道，1-线上；2-线下
  handler: number // 经办人
  handlerName: string // 经办人名称
  partaId: number // 甲方id
  partaName: string // 甲方名称
  customerId: number // 客户ID
  customerType: number // 客户类型
  customerName: string // 客户名称
  customerIndustry: string // 客户行业
  customerIdcard: string // 客户身份证
  customerPhone: string // 客户电话
  customerEmail: string // 客户邮箱
  houseId: number // 房产信息
  rentalPlan: number // 租金方案, 1-5年,2-10年,3-15年,4-20年;
  downPaymentAmount: number // 首付金额
  penaltyAmtRatio: number // 滞纳金比例
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 长租合同 API
  // 查询长租合同分页
export const getLongContractPage = async (params: any) => {
    return await request.get({ url: `/lease/long-contract/page`, params })
};

  // 查询长租合同详情
export const getLongContract = async (id: number) => {
    return await request.get({ url: `/lease/long-contract/get?id=` + id })
};
  // 查询长租合同详情
export const getLongContractDetail = async (id: number) => {
    return await request.get({ url: `/lease/long-contract/get-detail?id=` + id })
};

  // 新增长租合同
export const createLongContract = async (data: LongContractVO) => {
    return await request.post({ url: `/lease/long-contract/create`, data })
};

  // 修改长租合同
export const updateLongContract = async (data: LongContractVO) => {
    return await request.put({ url: `/lease/long-contract/update`, data })
};

  // 删除长租合同
export const deleteLongContract = async (id: number) => {
    return await request.delete({ url: `/lease/long-contract/delete?id=` + id })
};

  // 导出长租合同 Excel
export const exportLongContract = async (params) => {
    return await request.download({ url: `/lease/long-contract/export-excel`, params })
};

  // 提交长租合同
export const submitLongContract = async (data: LongContractVO) => {
    return await request.post({ url: `/lease/long-contract/submit`, data })
};
//生成合同账单
export const generateBill = async (id) => {
  return await request.post({ url: `/lease/long-contract/generate-bill/${id}` });
};

//------------------------------------------合同退租--------------------------------------------------
//合同退租二次确认数据生成
export const getThrowLeaseAffirm = async (params) => {
  return await request.put({
    url: `/lease/long-contract/throw-contract`,
    data: params
  });
};
//------------------------------------------合同作废--------------------------------------------------
//合同作废
export const getCancelContract = async (id) => {
  return await request.put({
    url: `/lease/long-contract/cancelled/${id}`});
};

  // 导出长租合同 Excel
  export const exportLongTemplateContract = async () => {
    return await request.download({ url: `/lease/long-contract/export-template-excel` })
};