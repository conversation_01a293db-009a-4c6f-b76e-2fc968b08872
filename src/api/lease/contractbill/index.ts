import request from '@/utils/request/server';

// 合同账单 VO
export interface ContractBillVO {
  id: number // id
  contractId: number // 合同ID
  contractCode: string // 合同编号
  planId: number // 付款计划ID
  contractType: number // 合同类型，1-租赁合同，2-长租合同，3-工位合同
  billCode: string // 账单编号
  paymentType: number // 支付类型，1-计划；2-其他；
  costType: string // 费用类型
  startDate: Date // 开始时间
  endDate: Date // 结束时间
  actualPaymentDate: Date // 实收日期
  paymentDate: Date // 收款日期
  fixedRent: number // 固定租金
  floatingRent: number // 浮动租金
  isOverdue: number // 是否逾期
  status: number // 状态, 1-未收; 2-部分结清; 3-已结清; 4-已作废;
  synStatus: number // 同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败
  synMessage: string // 同步外部信息
  totalAmt: number // 应收总金额
  receivedTotalAmt: number // 已收总金额
  refundTotalAmt: number // 退款总金额
  refundableTotalAmt: number // 应退总金额
  extend: string // 扩展信息
  remark: string // 备注
}

// 合同账单 API
  // 查询合同账单分页
export const getContractBillPage = async (params: any) => {
    return await request.get({ url: `/lease/contract-bill/page`, params })
};

// 查询合同账单退款分页
export const getContractBillCanRefundPage = async (params: any) => {
    return await request.get({ url: `/lease/contract-bill/refund-page`, params })
};

  // 查询合同账单详情
export const getContractBill = async (id: number) => {
    return await request.get({ url: `/lease/contract-bill/get?id=` + id })
};

  // 新增合同账单
export const createContractBill = async (data: ContractBillVO) => {
    return await request.post({ url: `/lease/contract-bill/create`, data })
};

  // 修改合同账单
export const updateContractBill = async (data: ContractBillVO) => {
    return await request.put({ url: `/lease/contract-bill/update`, data })
};

  // 删除合同账单
export const deleteContractBill = async (id: number) => {
    return await request.delete({ url: `/lease/contract-bill/delete?id=` + id })
};

  // 导出合同账单 Excel
export const exportContractBill = async (params) => {
    return await request.download({ url: `/lease/contract-bill/export-excel`, params })
};

  // 同步合同账单
  export const synContractBill = async (data: ContractBillVO) => {
    return await request.post({ url: `/lease/contract-bill/syn-interface`, data })
};
