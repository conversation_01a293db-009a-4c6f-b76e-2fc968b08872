import request from '@/utils/request/server';

// 工位合同 VO
export interface StationContractVO {
  id: number // id
  type: number // 合同类型，1-租入；2-租出
  code: string // 合同编码
  name: string // 合同名称
  status: number // 合同状态, 0-新建 1-待审核; 2-未开始; 3-执行中; 4-已到期; 5-已退租; 6-已作废
  approveStatus: number // 审批状态
  signingDate: Date // 合同签订日期
  signingChannel: number // 合同签订渠道，1-线上；2-线下
  handler: number // 经办人
  handlerName: string // 经办人名称
  partaId: number // 甲方id
  partaName: string // 甲方名称
  customerId: number // 客户ID
  customerType: number // 客户类型
  customerName: string // 客户名称
  customerIndustry: string // 客户行业
  customerIdcard: string // 客户身份证
  customerPhone: string // 客户电话
  customerEmail: string // 客户邮箱
  startDate: Date // 租赁期限-开始时间
  endDate: Date // 租赁期限-结束时间
  rentalPlan: number // 租金方案, 1-固定租金; 2-按天计算;
  monthBillingWay: number // 月计费方式, 1-整月; 2-自然月;
  daysOfYear: number // 年计算方式, 1-365; 2-366;
  paymentFirstDate: Date // 首期收款日期
  paymentEndDateType: number // 收款截止日期-类型, 1-每期开始日前; 2-每期开始日后; 3-每期结束日前; 4-每期结束日后
  paymentEndDateDays: number // 收款截止日期-天数
  unitPrice: number // 租金单价
  rentalUnit: number // 租金单位, 1-元/平米/天; 2-元/平米/月; 3-元/平米/年
  isPaymentFull: number // 是否一次性付清
  paymentCycle: number // 付款周期
  ensureFee: number // 租赁保证金
  renegeFee: number // 违约金
  penaltyAmtRatio: number // 滞纳金比例
  penaltyAmtType: number // 滞纳金基数类型, 1-租金; 2-保证金;
  rentalArea: number // 租赁面积
  totalAmt: number // 总费用
  receivedTotalAmt: number // 已收总金额
  rentalHoliday: string // 免租期
  rentIncrease: string // 租金递增
  extend: string // 扩展信息
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 工位合同 API
  // 查询工位合同分页
export const getStationContractPage = async (params: any) => {
    return await request.get({ url: `/lease/station-contract/page`, params })
};

  // 查询工位合同详情
export const getStationContract = async (id: number) => {
    return await request.get({ url: `/lease/station-contract/get?id=` + id })
};

  // 查询工位合同详情
export const getStationContractDetail = async (id: number) => {
    return await request.get({ url: `/lease/station-contract/get-detail?id=` + id })
};

  // 新增工位合同
export const createStationContract = async (data: StationContractVO) => {
    return await request.post({ url: `/lease/station-contract/create`, data })
};

  // 修改工位合同
export const updateStationContract = async (data: StationContractVO) => {
    return await request.put({ url: `/lease/station-contract/update`, data })
};

  // 删除工位合同
export const deleteStationContract = async (id: number) => {
    return await request.delete({ url: `/lease/station-contract/delete?id=` + id })
};

  // 导出工位合同 Excel
export const exportStationContract = async (params) => {
    return await request.download({ url: `/lease/station-contract/export-excel`, params })
};

  // 提交工位合同
export const submitStationContract = async (data: StationContractVO) => {
    return await request.post({ url: `/lease/station-contract/submit`, data })
};
//生成合同账单
export const generateBill = async (id) => {
  return await request.post({ url: `/lease/station-contract/generate-bill/${id}` });
};

export const getToDeposit = async (id) => {
  return await request.post({ url: `/lease/station-contract/toDeposit/${id}` });
};

//------------------------------------------合同退租--------------------------------------------------
//合同退租二次确认数据生成
export const getThrowLeaseAffirm = async (params) => {
  return await request.put({
    url: `/lease/station-contract/throw-contract`,
    data: params
  });
};
//------------------------------------------合同作废--------------------------------------------------
//合同作废
export const getCancelContract = async (id) => {
  return await request.put({
    url: `/lease/station-contract/cancelled/${id}`});
};
// 导出工位合同 Excel
export const exportStationTemplateContract = async () => {
  return await request.download({ url: `/lease/station-contract/export-template-excel` })
};