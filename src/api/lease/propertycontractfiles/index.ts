import request from '@/utils/request'

// 物业合同附件 VO
export interface PropertyContractFilesVO {
  id: number // id
  propertyContractId: number // 合同id
  fileId: number // 文件id
  name: string // 文件名
  path: string // 文件路径
  url: string // 文件 URL
  type: string // 文件类型
  size: number // 文件大小
  uploadTime: Date // 上传时间
}

/**
 * 查询物业合同附件列表
 */
export function getPropertyContractFilesPage(params) {
  return request({
    url: '/lease/property-contract-files/page',
    method: 'get',
    params
  })
}

/**
 * 查询物业合同附件详情
 */
export function getPropertyContractFilesDetail(id) {
  return request({
    url: '/lease/property-contract-files/get?id=' + id,
    method: 'get'
  })
}

/**
 * 新增物业合同附件
 */
export function createPropertyContractFiles(data) {
  return request({
    url: '/lease/property-contract-files/create',
    method: 'post',
    data: data
  })
}

/**
 * 修改物业合同附件
 */
export function updatePropertyContractFiles(data) {
  return request({
    url: '/lease/property-contract-files/update',
    method: 'put',
    data: data
  })
}

/**
 * 删除物业合同附件
 */
export function deletePropertyContractFiles(id) {
  return request({
    url: '/lease/property-contract-files/delete?id=' + id,
    method: 'delete'
  })
}

/**
 * 导出物业合同附件
 */
export function exportPropertyContractFiles(params) {
  return request({
    url: '/lease/property-contract-files/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
