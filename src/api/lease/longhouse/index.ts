import request from '@/utils/request/server';

// 长租房管理 VO
export interface LongHouseVO {
  buildingInfo: string // 楼宇
  floorInfo: string // 楼层
  roomNo: string // 房间号
  roomNum: string // 房间编码
  area: number // 面积
  price: number // 价格
  houseStatus: number // 房屋状态 1-待出租 2-待入住 3-出租中 4-退租中 5-已完成
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 长租房管理 API
  // 查询长租房管理分页
export const getLongHousePage = async (params: any) => {
    return await request.get({ url: `/lease/long-house/page`, params })
};

  // 查询长租房管理分页-所有状态的数据
  export const getLongHouseByStatusPage = async (params: any) => {
    return await request.get({ url: `/lease/long-house/page-by-status`, params })
};

  // 查询长租房管理分页-所有状态的数据
  export const getLongHouseByAllPage = async (params: any) => {
    return await request.get({ url: `/lease/long-house/page-by-all`, params })
};

  // 查询长租房管理详情
export const getLongHouse = async (id: number) => {
    return await request.get({ url: `/lease/long-house/get?id=` + id })
};

  // 新增长租房管理
export const createLongHouse = async (data: LongHouseVO) => {
    return await request.post({ url: `/lease/long-house/create`, data })
};

  // 修改长租房管理
export const updateLongHouse = async (data: LongHouseVO) => {
    return await request.put({ url: `/lease/long-house/update`, data })
};

  // 删除长租房管理
export const deleteLongHouse = async (id: number) => {
    return await request.delete({ url: `/lease/long-house/delete?id=` + id })
};

  // 导出长租房管理 Excel
export const exportLongHouse = async (params) => {
    return await request.download({ url: `/lease/long-house/export-excel`, params })
};

  // 查询长租房管理lov数据
export const getLongHouseLov = async (params: any) => {
  return await request.get({ url: `/lease/long-house/building-list`, params })
};

  // 查询长租房管理lov数据
  export const getLongHouseLovPage = async (params: any) => {
    return await request.get({ url: `/lease/long-house/building-list-page`, params })
  };