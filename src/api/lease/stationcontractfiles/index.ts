import request from '@/utils/request/server';

// 工位合同附件 VO
export interface StationContractFilesVO {
  id: number // id
  stationContractId: number // 合同id
  fileId: number // 文件id
  name: string // 文件名
  path: string // 文件路径
  url: string // 文件 URL
  type: string // 文件类型
  size: number // 文件大小
  uploadTime: Date // 上传时间
}

// 工位合同附件 API
  // 查询工位合同附件分页
export const getStationContractFilesPage = async (params: any) => {
    return await request.get({ url: `/lease/station-contract-files/page`, params })
};

  // 查询工位合同附件详情
export const getStationContractFiles = async (id: number) => {
    return await request.get({ url: `/lease/station-contract-files/get?id=` + id })
};

  // 新增工位合同附件
export const createStationContractFiles = async (data: StationContractFilesVO) => {
    return await request.post({ url: `/lease/station-contract-files/create`, data })
};

  // 修改工位合同附件
export const updateStationContractFiles = async (data: StationContractFilesVO) => {
    return await request.put({ url: `/lease/station-contract-files/update`, data })
};

  // 删除工位合同附件
export const deleteStationContractFiles = async (id: number) => {
    return await request.delete({ url: `/lease/station-contract-files/delete?id=` + id })
};

  // 导出工位合同附件 Excel
export const exportStationContractFiles = async (params) => {
    return await request.download({ url: `/lease/station-contract-files/export-excel`, params })
};
