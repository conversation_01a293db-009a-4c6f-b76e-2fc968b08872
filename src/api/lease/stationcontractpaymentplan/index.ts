import request from '@/utils/request/server';

// 工位合同付款计划 VO
export interface StationContractPaymentPlanVO {
  id: number // id
  stationContractId: number // 合同ID
  code: string // 编号
  paymentType: number // 支付类型，1-计划；2-其他；
  costType: string // 费用类型
  startDate: Date // 开始时间
  endDate: Date // 结束时间
  actualPaymentDate: Date // 实收日期
  paymentDate: Date // 收款日期
  paymentAmt: number // 应收租金
  penaltyRatio: number // 滞纳金比例(其他费用使用)
  penaltyAmt: number // 滞纳金
  isOverdue: number // 是否逾期
  status: number // 状态, 1-未收; 2-部分结清; 3-已结清; 4-已作废;
  totalAmt: number // 应收总金额
  receivedTotalAmt: number // 已收总金额
  refundTotalAmt: number // 退款总金额
  refundableTotalAmt: number // 应退总金额
  extend: string // 扩展信息
  remark: string // 备注
}

// 工位合同付款计划 API
  // 查询工位合同付款计划分页
export const getStationContractPaymentPlanPage = async (params: any) => {
    return await request.get({ url: `/lease/station-contract-payment-plan/page`, params })
};

  // 查询工位合同付款计划详情
export const getStationContractPaymentPlan = async (id: number) => {
    return await request.get({ url: `/lease/station-contract-payment-plan/get?id=` + id })
};

  // 新增工位合同付款计划
export const createStationContractPaymentPlan = async (data: StationContractPaymentPlanVO) => {
    return await request.post({ url: `/lease/station-contract-payment-plan/create`, data })
};

  // 修改工位合同付款计划
export const updateStationContractPaymentPlan = async (data: StationContractPaymentPlanVO) => {
    return await request.put({ url: `/lease/station-contract-payment-plan/update`, data })
};

  // 删除工位合同付款计划
export const deleteStationContractPaymentPlan = async (id: number) => {
    return await request.delete({ url: `/lease/station-contract-payment-plan/delete?id=` + id })
};

  // 导出工位合同付款计划 Excel
export const exportStationContractPaymentPlan = async (params) => {
    return await request.download({ url: `/lease/station-contract-payment-plan/export-excel`, params })
};
