import request from '@/utils/request/server';

// 预存金发票 VO
export interface PrepaidInvoiceVO {
  id: number // id
  prepaidId: number // 预存金ID
  customerId: number // 客户ID
  customerCode: string // 客户编号
  customerName: string // 客户名称
  socialCreditCode: string // 统一社会信用代码
  invoiceNo: string // 发票流水号
  type: string // 发票类型, 1-电子发票; 2-纸质发票
  title: string // 发票抬头
  taxpayerNo: string // 纳税人识别号
  amount: number // 开票金额
  content: string // 开票内容
  contact: string // 联系方式
  email: string // 邮箱地址
  orderNum: string // 订单数量
  status: number // 开票状态, 1-开票中; 2-成功; 3-失败;
  thirdInvoiceNo: string // 三方开票编号
  isInvoice: number // 是否开票
  invoiceTime: Date // 三方开票时间
  callbackMsg: string // 回调信息
  extend: string // 扩展信息
  remark: string // 备注
}

// 预存金发票 API
  // 查询预存金发票分页
export const getPrepaidInvoicePage = async (params: any) => {
    return await request.get({ url: `/lease/prepaid-invoice-new/page`, params })
};

  // 查询预存金发票详情
export const getPrepaidInvoice = async (id: number) => {
    return await request.get({ url: `/lease/prepaid-invoice-new/get?id=` + id })
};

  // 新增预存金发票
export const createPrepaidInvoice = async (data: PrepaidInvoiceVO) => {
    return await request.post({ url: `/lease/prepaid-invoice-new/create`, data })
};

  // 修改预存金发票
export const updatePrepaidInvoice = async (data: PrepaidInvoiceVO) => {
    return await request.put({ url: `/lease/prepaid-invoice-new/update`, data })
};

  // 删除预存金发票
export const deletePrepaidInvoice = async (id: number) => {
    return await request.delete({ url: `/lease/prepaid-invoice-new/delete?id=` + id })
};

  // 导出预存金发票 Excel
export const exportPrepaidInvoice = async (params) => {
    return await request.download({ url: `/lease/prepaid-invoice-new/export-excel`, params })
};

  // 导出预存金发票 Excel
  export const getPrepaidInvoiceLine = async (id: number) => {
    return await request.get({ url: `/lease/prepaid-invoice-new/get-invoice-line?id=` + id })
};
