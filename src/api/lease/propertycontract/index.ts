import request from '@/utils/request/server';

// 物业合同 VO
export interface PropertyContractVO {
  id: number // id
  code: string // 合同编码
  name: string // 合同名称
  status: number // 合同状态, 0-新建 1-待审核; 2-未开始; 3-执行中; 4-已到期; 5-已退租; 6-已作废
  approveStatus: number // 审批状态
  signingDate: Date // 合同签订日期
  signingChannel: number // 合同签订渠道，1-线上；2-线下
  handler: number // 经办人
  handlerName: string // 经办人名称
  partaId: number // 甲方id
  partaName: string // 甲方名称
  customerId: number // 客户ID
  customerType: number // 客户类型
  customerName: string // 客户名称
  customerIndustry: string // 客户行业
  customerIdcard: string // 客户身份证
  customerPhone: string // 客户电话
  customerEmail: string // 客户邮箱
  startDate: Date // 租赁期限-开始时间
  endDate: Date // 租赁期限-结束时间
  unitPrice: number // 租金单价
  rentalUnit: number // 租金单位, 1-元/平米/天; 2-元/平米/月; 3-元/平米/年
  rentalArea: number // 租赁面积
  extend: string // 扩展信息
  sourceId: number // 来源合同id
  contractType: number // 来源合同类型，1-租赁合同，2-长租合同，3-工位合同
  sourceCode: string // 来源合同编码
  sourceName: string // 来源合同名称
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  companyKey: string // 公司key
  type: String //操作类型
}

// 物业合同 API
  // 查询物业合同分页
  export const getPropertyContractPage = async (params: any) => {
    return await request.get({ url: `/lease/property-contract/page`, params })
};

  // 查询物业合同详情
export const getPropertyContract = async (id: number) => {
    return await request.get({ url: `/lease/property-contract/get?id=` + id })
};

  // 查询物业合同详情
export const getPropertyContractDetail = async (id: number) => {
    return await request.get({ url: `/lease/property-contract/get-detail?id=` + id })
};

  // 新增物业合同
export const createPropertyContract = async (data: PropertyContractVO) => {
    return await request.post({ url: `/lease/property-contract/create`, data })
};

  // 修改物业合同
export const updatePropertyContract = async (data: PropertyContractVO) => {
    return await request.put({ url: `/lease/property-contract/update`, data })
};

  // 删除物业合同
export const deletePropertyContract = async (id: number) => {
    return await request.delete({ url: `/lease/property-contract/delete?id=` + id })
};

  // 导出物业合同 Excel
export const exportPropertyContract = async (params) => {
    return await request.download({ url: `/lease/property-contract/export-excel`, params })
};

  // 提交物业合同
export const submitPropertyContract = async (data: PropertyContractVO) => {
    return await request.post({ url: `/lease/property-contract/submit`, data })
};
//生成合同账单
export const generateBill = async (id) => {
  return await request.post({ url: `/lease/property-contract/generate-bill/${id}` });
};

export const getToDeposit = async (id) => {
  return await request.post({ url: `/lease/property-contract/toDeposit/${id}` });
};

//------------------------------------------合同退租--------------------------------------------------
//合同退租二次确认数据生成
export const getThrowLeaseAffirm = async (params) => {
  return await request.put({
    url: `/lease/property-contract/throw-contract`,
    data: params
  });
};
//------------------------------------------合同作废--------------------------------------------------
//合同作废
export const getCancelContract = async (id) => {
  return await request.put({
    url: `/lease/property-contract/cancelled/${id}`});
};

/**
 * 查询物业合同编辑表单列表
 */
//查询可创建物业合同的数据
export const getPropertyContractEditFormPage = async (params: any) => {
  return await request.get({ url: `/lease/contract/canCreateProperty`, params })
}

/**
 * 创建物业合同编辑表单
 */
export const createPropertyContractEditForm = async (params: any) => {
  return await request.post({ url: `/lease/property-contract/leaseToProperty`, data: params });
};

// 导出物业合同 Excel
export const exportPropertyTemplateContract = async () => {
  return await request.download({ url: `/lease/property-contract/export-template-excel` })
};
