import request from '@/utils/request/server';

// 长租房合同附件 VO
export interface LongContractFilesVO {
  id: number // id
  longContractId: number // 合同id
  name: string // 文件名
  path: string // 文件路径
  url: string // 文件 URL
  type: string // 文件类型
  size: number // 文件大小
}

// 长租房合同附件 API
  // 查询长租房合同附件分页
export const getLongContractFilesPage = async (params: any) => {
    return await request.get({ url: `/lease/long-contract-files/page`, params })
};

  // 查询长租房合同附件详情
export const getLongContractFiles = async (id: number) => {
    return await request.get({ url: `/lease/long-contract-files/get?id=` + id })
};

  // 新增长租房合同附件
export const createLongContractFiles = async (data: LongContractFilesVO) => {
    return await request.post({ url: `/lease/long-contract-files/create`, data })
};

  // 修改长租房合同附件
export const updateLongContractFiles = async (data: LongContractFilesVO) => {
    return await request.put({ url: `/lease/long-contract-files/update`, data })
};

  // 删除长租房合同附件
export const deleteLongContractFiles = async (id: number) => {
    return await request.delete({ url: `/lease/long-contract-files/delete?id=` + id })
};

  // 导出长租房合同附件 Excel
export const exportLongContractFiles = async (params) => {
    return await request.download({ url: `/lease/long-contract-files/export-excel`, params })
};
