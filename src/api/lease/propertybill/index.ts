import request from '@/utils/request/server';

// 物业账单 VO
export interface PropertyBillVO {
  id: number // id
  propertyBillCode: string // 账单编码
  roomId: string // 房间id
  roomCode: string // 房间编码
  roomName: string // 房间描述
  ownerInfo: string // 业主信息
  meterReadingType: number // 抄表类型 1-水表，2-电表，3-燃气
  magnification: number // 倍率
  lastMeterBottom: number // 上次表底
  lastAttributedMonth: string // 上次归属月份
  lastMeterReadingDate: Date // 上次抄表日期
  meterBottom: number // 本次表底
  attributedMonth: string // 本次归属月份
  meterReadingDate: Date // 本次抄表日期
  synStatus: number // 同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败
  synMessage: string // 同步外部信息
  externalKey: string // 外部系统主键
  synTime: Date // 同步外部系统时间
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 物业账单 API
  // 查询物业账单分页
export const getPropertyBillPage = async (params: any) => {
    return await request.get({ url: `/lease/property-bill/page`, params })
};

  // 查询物业账单详情
export const getPropertyBill = async (id: number) => {
    return await request.get({ url: `/lease/property-bill/get?id=` + id })
};

  // 新增物业账单
export const createPropertyBill = async (data: PropertyBillVO) => {
    return await request.post({ url: `/lease/property-bill/create`, data })
};

  // 修改物业账单
export const updatePropertyBill = async (data: PropertyBillVO) => {
    return await request.put({ url: `/lease/property-bill/update`, data })
};

  // 删除物业账单
export const deletePropertyBill = async (id: number) => {
    return await request.delete({ url: `/lease/property-bill/delete?id=` + id })
};

  // 导出物业账单 Excel
export const exportPropertyBill = async (params) => {
    return await request.download({ url: `/lease/property-bill/export-excel`, params })
};

// 同步账单
  export const synContractBill = async (data: PropertyBillVO) => {
    return await request.post({ url: `/lease/property-bill/syn-interface`, data })
};
