import request from '@/utils/request/server';

// 退款账单行信息 VO
export interface RefundBillLineVO {
  id: number // id
  headerId: number // 退款头id
  sourceId: number // 来源id
  contractType: number // 退款类型，1-合同账单，2-押金，3-其他
  billAmt: number // 账单金额
  refundTotalAmt: number // 退款总金额
  refundableTotalAmt: number // 应退总金额
  status: number // 退款状态, 1-新建; 2-退款中; 3-完成; 4-作废;
  billStatus: number // 账单状态, 1-需处理，2-无需处理;
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  externalCustomerId: string // 外部系统客户id
  synTime: Date // 同步外部系统时间
  synStatus: number // 同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败
  synMessage: string // 同步外部信息
}

// 退款账单行信息 API
  // 查询退款账单行信息分页
export const getRefundBillLinePage = async (params: any) => {
    return await request.get({ url: `/lease/refund-bill-line/page`, params })
};

  // 查询退款账单行信息详情
export const getRefundBillLine = async (id: number) => {
    return await request.get({ url: `/lease/refund-bill-line/get?id=` + id })
};

  // 新增退款账单行信息
export const createRefundBillLine = async (data: RefundBillLineVO) => {
    return await request.post({ url: `/lease/refund-bill-line/create`, data })
};

  // 修改退款账单行信息
export const updateRefundBillLine = async (data: RefundBillLineVO) => {
    return await request.put({ url: `/lease/refund-bill-line/update`, data })
};

  // 删除退款账单行信息
export const deleteRefundBillLine = async (id: number) => {
    return await request.delete({ url: `/lease/refund-bill-line/delete?id=` + id })
};

  // 导出退款账单行信息 Excel
export const exportRefundBillLine = async (params) => {
    return await request.download({ url: `/lease/refund-bill-line/export-excel`, params })
};
