import request from '@/utils/request/server';

// 工位合同房产信息 VO
export interface StationContractHouseVO {
  id: number // id
  longContractId: number // 合同ID
  spaceType: number // 空间类型
  spaceId: number // 空间ID
  houseId: number // 房产信息id
  area: number // 建筑面积
  rentalArea: number // 租赁面积
  price: number // 价格
}

// 工位合同房产信息 API
  // 查询工位合同房产信息分页
export const getStationContractHousePage = async (params: any) => {
    return await request.get({ url: `/lease/station-contract-house/page`, params })
};

  // 查询工位合同房产信息详情
export const getStationContractHouse = async (id: number) => {
    return await request.get({ url: `/lease/station-contract-house/get?id=` + id })
};

  // 新增工位合同房产信息
export const createStationContractHouse = async (data: StationContractHouseVO) => {
    return await request.post({ url: `/lease/station-contract-house/create`, data })
};

  // 修改工位合同房产信息
export const updateStationContractHouse = async (data: StationContractHouseVO) => {
    return await request.put({ url: `/lease/station-contract-house/update`, data })
};

  // 删除工位合同房产信息
export const deleteStationContractHouse = async (id: number) => {
    return await request.delete({ url: `/lease/station-contract-house/delete?id=` + id })
};

  // 导出工位合同房产信息 Excel
export const exportStationContractHouse = async (params) => {
    return await request.download({ url: `/lease/station-contract-house/export-excel`, params })
};
