import request from '@/utils/request/server';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
import qs from 'qs';
//招商列表列表
export const getOpportunityPage = async (params: PageParam) => {
  return await request.get({ url: '/lease/opportunity', params });
};
//我的招商列表列表
export const getOpportunityPageByMe = async (params: PageParam) => {
  return await request.get({ url: '/lease/opportunity/my', params });
};
//新增招商
export const getOpportunityAdd = async (params) => {
  return await request.post({ url: '/lease/opportunity', data: params });
};
//提交招商数据
export const getOpportunitySubmmit = async (params) => {
  return await request.post({ url: '/lease/opportunity/submmit', data: params });
};
//退回招商数据
export const getOpportunityReturnBill = async (params) => {
  return await request.post({ url: '/lease/opportunity/returnbill', data: params });
};
//修改招商
export const getOpportunityEdit = async (params) => {
  return await request.put({
    url: `/lease/opportunity/${params.id}`,
    data: params
  });
};
//删除招商信息
export const getOpportunityDel = async (params) => {
  return await request.delete({
    url: `/lease/opportunity/${params.id}`
  });
};
//单个招商信息
export const getOpportunityDetial = async (params) => {
  return await request.get({
    url: `/lease/opportunity/${params.id}`
  });
};
//招商跟进列表信息
export const getOpportunityFollowPage = async (params: PageParam) => {
  return await request.get({ url: '/lease/opportunity-follow', params });
};
//新增招商跟进记录
export const getOpportunityFollowAdd = async (params) => {
  return await request.post({ url: '/lease/opportunity-follow', data: params });
};

//调整招商记录的分配人信息
export const getOpportunityAssignMoidfy = async (params) => {
  return await request.post({ url: '/lease/opportunity/assign-user', data: params });
};
//新增招商转客户
export const getOpportunityToCustomer = async (params) => {
  return await request.post({ url: '/lease/opportunity/opportunityToCustomer', data: params });
};
//客户列表
export const getCustomerPage = async (params: PageParam) => {
  return await request.get({ url: '/lease/customer', params });
};
//客户列表
export const getCustomerSimpleList = async (customerType: number) => {
  return await request.get({ url: '/lease/customer/simple-list?customerType=' + customerType });
};
//我的客户列表
export const getCustomerPageByMe = async (params: PageParam) => {
  return await request.get({ url: '/lease/customer/my', params });
};
//新增客户列表
export const getCustomerAdd = async (params) => {
  return await request.post({ url: '/lease/customer', data: params });
};
//调整招商记录的分配人信息
export const getCustomerAssignMoidfy = async (params) => {
  return await request.post({ url: '/lease/customer/assign-user', data: params });
};
//提交招商数据
export const getCustomerSubmmit = async (params) => {
  return await request.post({ url: '/lease/customer/submmit', data: params });
};
//退回招商数据
export const getCustomerReturnBill = async (params) => {
  return await request.post({ url: '/lease/customer/returnbill', data: params });
};
//修改客户列表
export const getCustomerEdit = async (params) => {
  return await request.put({
    url: `/lease/customer/${params.id}`,
    data: params
  });
};
//删除客户列表
export const getCustomeDel = async (params) => {
  return await request.delete({ url: `/lease/customer/${params.id}` });
};

//删除客户列表
export const synCustomerInterface = async (params) => {
  return await request.post({ url: `/lease/customer/syn`, data: params });
};
//单个客户信息
export const getCustomeDetial = async (params) => {
  return await request.get({
    url: `/lease/customer/${params.id}`
  });
};
//客户跟进列表信息
export const getCustomerFollowPage = async (params: PageParam) => {
  return await request.get({ url: '/lease/customer-follow', params });
};
//新增客户跟进记录
export const getCustomerFollowAdd = async (params) => {
  return await request.post({ url: '/lease/customer-follow', data: params });
};
//公司信息列表
export const getPartnerPage = async (params: PageParam) => {
  return await request.get({ url: '/lease/partner', params });
};
//公司信息列表-新增
export const getPartnerAdd = async (params) => {
  return await request.post({ url: '/lease/partner', data: params });
};
//公司信息列表-修改
export const getPartnerEdit = async (params) => {
  return await request.put({
    url: `/lease/partner/${params.id}`,
    data: params
  });
};
//公司信息-删除
export const getPartnerDel = async (params) => {
  return await request.delete({ url: `/lease/partner/${params.id}` });
};
//公司基本信息
export const getPartnerDetial = async (params) => {
  return await request.get({ url: `/lease/partner/${params.id}` });
};
