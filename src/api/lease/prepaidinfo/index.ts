import request from '@/utils/request/server';

// 预存金信息 VO
export interface PrepaidInfoVO {
  id: number // id
  customerId: number // 客户ID
  accountCode: string // 账户编号
  socialCreditCode: string // 统一社会信用代码
  amount: number // 余额
  lockAmount: number // 余额
  rechargeAmount: number // 充值金额
  deductAmount: number // 扣款金额
  refundAmount: number // 退款金额
  extend: string // 扩展信息
  remark: string // 备注
  billStatus: string // 账户状态
}

// 预存金信息 API
  // 查询预存金信息分页
export const getPrepaidInfoPage = async (params: any) => {
    return await request.get({ url: `/lease/prepaid-info-new/page`, params })
};

  // 查询预存金信息详情
export const getPrepaidInfo = async (id: number) => {
    return await request.get({ url: `/lease/prepaid-info-new/get?id=` + id })
};

  // 查询预存金信息详情
  export const getPrepaidDetail = async (id: number) => {
    return await request.get({ url: `/lease/prepaid-info-new/get-detail?id=` + id })
};

  // 新增预存金信息
export const createPrepaidInfo = async (data: PrepaidInfoVO) => {
    return await request.post({ url: `/lease/prepaid-info-new/create`, data })
};

  // 修改预存金信息
export const updatePrepaidInfo = async (data: PrepaidInfoVO) => {
    return await request.put({ url: `/lease/prepaid-info-new/update`, data })
};

  // 新增预存金信息-有返回信息
  export const createPrepaidInfoResponse = async (data: PrepaidInfoVO) => {
    return await request.post({ url: `/lease/prepaid-info-new/create-response`, data })
};

  // 修改预存金信息-有返回信息
export const updatePrepaidInfoResponse = async (data: PrepaidInfoVO) => {
    return await request.put({ url: `/lease/prepaid-info-new/update-response`, data })
};

  // 删除预存金信息
export const deletePrepaidInfo = async (id: number) => {
    return await request.delete({ url: `/lease/prepaid-info-new/delete?id=` + id })
};

  // 导出预存金信息 Excel
export const exportPrepaidInfo = async (params) => {
    return await request.download({ url: `/lease/prepaid-info-new/export-excel`, params })
};

// 获取客户信息
export const getCustomerInfoSimple = async () => {
  return await request.get({ url: `/lease/prepaid-info-new/get-customer-simple` })
};

// 获取客户信息
export const prepaidOrderToInvoiceResponse = async (data) => {
  return await request.post({ url: `/lease/prepaid-invoice-new/order-to-invoice`, data })
};

// 获取预存金提醒信息
export const getPrepaidRemindDetail = async (id: number) => {
  return await request.get({ url: `/lease/prepaid-info-new/get-remind-detail?id=` + id })
};

// 修改预存金提醒信息
export const updatePrepaidRemindDetail = async (data: PrepaidInfoVO) => {
  return await request.put({ url: `/lease/prepaid-info-new/update-remind`, data })
};
