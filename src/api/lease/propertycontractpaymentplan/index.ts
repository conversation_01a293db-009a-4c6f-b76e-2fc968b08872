import request from '@/utils/request'

// 物业合同付款计划 VO
export interface PropertyContractPaymentPlanVO {
  id: number // id
  propertyContractId: number // 合同ID
  code: string // 编号
  paymentType: number // 支付类型，1-计划；2-其他；
  costType: string // 费用类型
  startDate: Date // 开始时间
  endDate: Date // 结束时间
  actualPaymentDate: Date // 实收日期
  paymentDate: Date // 收款日期
  paymentAmt: number // 应收租金
  penaltyRatio: number // 滞纳金比例(其他费用使用)
  penaltyAmt: number // 滞纳金
  isOverdue: number // 是否逾期
  status: number // 状态, 1-未收; 2-部分结清; 3-已结清; 4-已作废;
  totalAmt: number // 应收总金额
  receivedTotalAmt: number // 已收总金额
  refundTotalAmt: number // 退款总金额
  refundableTotalAmt: number // 应退总金额
  extend: string // 扩展信息
  remark: string // 备注
  synStatus: number // 同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败
  synMessage: string // 同步外部信息
}

/**
 * 查询物业合同付款计划列表
 */
export function getPropertyContractPaymentPlanPage(params) {
  return request({
    url: '/lease/property-contract-payment-plan/page',
    method: 'get',
    params
  })
}

/**
 * 查询物业合同付款计划详情
 */
export function getPropertyContractPaymentPlanDetail(id) {
  return request({
    url: '/lease/property-contract-payment-plan/get?id=' + id,
    method: 'get'
  })
}

/**
 * 新增物业合同付款计划
 */
export function createPropertyContractPaymentPlan(data) {
  return request({
    url: '/lease/property-contract-payment-plan/create',
    method: 'post',
    data: data
  })
}

/**
 * 修改物业合同付款计划
 */
export function updatePropertyContractPaymentPlan(data) {
  return request({
    url: '/lease/property-contract-payment-plan/update',
    method: 'put',
    data: data
  })
}

/**
 * 删除物业合同付款计划
 */
export function deletePropertyContractPaymentPlan(id) {
  return request({
    url: '/lease/property-contract-payment-plan/delete?id=' + id,
    method: 'delete'
  })
}

/**
 * 导出物业合同付款计划
 */
export function exportPropertyContractPaymentPlan(params) {
  return request({
    url: '/lease/property-contract-payment-plan/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
