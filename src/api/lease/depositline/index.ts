import request from '@/utils/request/server';

// 押金行信息 VO
export interface DepositLineVO {
  id: number // id
  depositId: number // 押金id
  operaType: number // 操作类型 1-收款 2-扣款 3-退款
  operaAmount: number // 操作金额
  startTime: Date // 开始时间
  endTime: Date // 结束时间
  receiveType: number // 收款方式 1-银行转账 2-现金 3-支票 4-汇票 5-其他
  receiveAccount: string // 收款账号
  receiveDate: Date // 收款时间
  refundType: number // 退款方式 1-银行转账 2-现金
  refundAccount: string // 退款账号
  refundDate: Date // 退款时间
  actFile: number // 附件信息
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 押金行信息 API
  // 查询押金行信息分页
export const getDepositLinePage = async (params: any) => {
    return await request.get({ url: `/lease/deposit-line/page`, params })
};

  // 查询押金行信息详情
export const getDepositLine = async (id: number) => {
    return await request.get({ url: `/lease/deposit-line/get?id=` + id })
};

  // 新增押金行信息
export const createDepositLine = async (data: DepositLineVO) => {
    return await request.post({ url: `/lease/deposit-line/create`, data })
};

  // 修改押金行信息
export const updateDepositLine = async (data: DepositLineVO) => {
    return await request.put({ url: `/lease/deposit-line/update`, data })
};

  // 删除押金行信息
export const deleteDepositLine = async (id: number) => {
    return await request.delete({ url: `/lease/deposit-line/delete?id=` + id })
};

  // 导出押金行信息 Excel
export const exportDepositLine = async (params) => {
    return await request.download({ url: `/lease/deposit-line/export-excel`, params })
};
