import request from '@/utils/request/server';

// 退款账单管理 VO
export interface RefundBillHeaderVO {
  id: number // id
  refundCode: string // 退款编号
  customerId: number // 客户id
  contractType: number // 退款类型，1-合同账单，2-押金，3-其他
  refundTotalAmt: number // 退款总金额
  refundableTotalAmt: number // 应退总金额
  status: number // 状态, 1-新建; 2-退款中; 3-完成; 4-作废;
  remark: string // 备注
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
  externalCustomerId: string // 外部系统客户id
  synTime: Date // 同步外部系统时间
  synStatus: number // 同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败
  synMessage: string // 同步外部信息
}

// 退款账单管理 API
  // 查询退款账单管理分页
export const getRefundBillHeaderPage = async (params: any) => {
    return await request.get({ url: `/lease/refund-bill-header/page`, params })
};

  // 查询退款账单管理详情
export const getRefundBillHeader = async (id: number) => {
    return await request.get({ url: `/lease/refund-bill-header/get?id=` + id })
};

  // 查询退款账单管理详情
  export const getRefundBillHeaderDetail = async (id: number) => {
    return await request.get({ url: `/lease/refund-bill-header/get-detail?id=` + id })
};

  // 新增退款账单管理
export const createRefundBillHeader = async (data: RefundBillHeaderVO) => {
    return await request.post({ url: `/lease/refund-bill-header/create`, data })
};

  // 修改退款账单管理
export const updateRefundBillHeader = async (data: RefundBillHeaderVO) => {
    return await request.put({ url: `/lease/refund-bill-header/update`, data })
};

  // 删除退款账单管理
export const deleteRefundBillHeader = async (id: number) => {
    return await request.delete({ url: `/lease/refund-bill-header/delete?id=` + id })
};

  // 导出退款账单管理 Excel
export const exportRefundBillHeader = async (params) => {
    return await request.download({ url: `/lease/refund-bill-header/export-excel`, params })
};

// 同步账单
  export const synContractBill = async (data: RefundBillHeaderVO) => {
    return await request.post({ url: `/lease/refund-bill-header/syn-interface`, data })
};

  // 提交账单
export const submitRefundBill = async (data: RefundBillHeaderVO) => {
    return await request.post({ url: `/lease/refund-bill-header/submit`, data })
};