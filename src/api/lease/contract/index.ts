import request from '@/utils/request/server';
//查询合同列表
export const getContractPage = async (params: PageParam) => {
  return await request.get({ url: '/lease/contract', params });
};
//新增合同-租入和租出
export const getContractAdd = async (data) => {
  return await request.post({ url: '/lease/contract', data });
};
//修改合同-租入和租出
export const getContractEdit = async (params) => {
  return await request.put({
    url: `/lease/contract/${params.id}`,
    data: params
  });
};

//新增合同-租入和租出
export const getContractSave = async (data) => {
  return await request.post({ url: '/lease/contract/save', data });
};
//合同详情信息
export const getContractDetial = async (params) => {
  return await request.get({
    url: `/lease/contract/${params.id}`,
    data: params
  });
};
//删除合同
export const getContractDel = async (params) => {
  return await request.delete({ url: `/lease/contract/${params.id}` });
};
// 上传文件
export const updateFile = (data: any) => {
  return request.upload({ url: '/infra/file/upload-file', data });
};

//生成付款计划
export const getGeneratePlan = async (params) => {
  return await request.post({
    url: '/lease/contract/generate-payment-plan',
    data: params
  });
};
//生成合同账单
export const getSynInterface = async (id) => {
  return await request.post({ url: `/lease/contract/syninterface/${id}` });
};
//生成押金账单
export const getToDeposit = async (id) => {
  return await request.post({ url: `/lease/contract/toDeposit/${id}` });
};

//------------------------------------------合同退租--------------------------------------------------
//合同退租二次确认数据生成
export const getThrowLeaseAffirm = async (params) => {
  return await request.put({
    url: `/lease/contract/throw-lease/new`,
    data: params
  });
};
//------------------------------------------合同作废--------------------------------------------------
//合同作废
export const getCancelContract = async (id) => {
  return await request.put({
    url: `/lease/contract/cancelled-new/${id}`});
};
//------------------------------------------合同提交--------------------------------------------------
//合同提交
export const getSubmitContract = async (id) => {
  return await request.post({
    url: `/lease/contract/submmit/${id}`
  });
};
//------------------------------------------物业合同--------------------------------------------------
//查询可创建物业合同的数据
export const getPropertyContractEditFormPage = async (params: any) => {
  return await request.get({ url: `/lease/contract/canCreateProperty`, params })
}
