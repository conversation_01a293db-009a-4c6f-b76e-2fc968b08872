import request from '@/utils/request';

// 物业合同房产信息 VO
export interface PropertyContractHouseVO {
  id: number // id
  propertyContractId: number // 合同ID
  spaceType: number // 空间类型
  spaceId: number // 空间ID
  houseId: number // 房产信息id
  area: number // 建筑面积
  rentalArea: number // 租赁面积
  price: number // 价格
}

// 物业合同房产信息 API
/**
 * 查询物业合同房产列表
 */
export function getPropertyContractHousePage(params) {
  return request({
    url: '/lease/property-contract-house/page',
    method: 'get',
    params
  });
}

/**
 * 查询物业合同房产详情
 */
export function getPropertyContractHouseDetail(id) {
  return request({
    url: '/lease/property-contract-house/get?id=' + id,
    method: 'get'
  });
}

/**
 * 新增物业合同房产
 */
export function createPropertyContractHouse(data) {
  return request({
    url: '/lease/property-contract-house/create',
    method: 'post',
    data: data
  });
}

/**
 * 修改物业合同房产
 */
export function updatePropertyContractHouse(data) {
  return request({
    url: '/lease/property-contract-house/update',
    method: 'put',
    data: data
  });
}

/**
 * 删除物业合同房产
 */
export function deletePropertyContractHouse(id) {
  return request({
    url: '/lease/property-contract-house/delete?id=' + id,
    method: 'delete'
  });
}

/**
 * 导出物业合同房产
 */
export function exportPropertyContractHouse(params) {
  return request({
    url: '/lease/property-contract-house/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  });
}
