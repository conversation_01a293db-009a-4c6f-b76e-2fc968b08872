import request from '@/utils/request';

// 物业合同退租记录 VO
export interface PropertyContractThrowLeaseVO {
  id: number // id
  contractId: number // 合同ID
  reason: number // 退租原因, 1-物业服务; 2-楼宇质量; 3-价格因素; 4-其他
  throwLeaseDate: Date // 退租时间
  explanation: string // 退租说明
  extend: string // 扩展信息
  remark: string // 备注
}

/**
 * 查询物业合同退租列表
 */
export function getPropertyContractThrowLeasePage(params) {
  return request({
    url: '/lease/property-contract-throw-lease/page',
    method: 'get',
    params
  })
}

/**
 * 查询物业合同退租详情
 */
export function getPropertyContractThrowLeaseDetail(id) {
  return request({
    url: '/lease/property-contract-throw-lease/get?id=' + id,
    method: 'get'
  })
}

/**
 * 新增物业合同退租
 */
export function createPropertyContractThrowLease(data) {
  return request({
    url: '/lease/property-contract-throw-lease/create',
    method: 'post',
    data: data
  })
}

/**
 * 修改物业合同退租
 */
export function updatePropertyContractThrowLease(data) {
  return request({
    url: '/lease/property-contract-throw-lease/update',
    method: 'put',
    data: data
  })
}

/**
 * 删除物业合同退租
 */
export function deletePropertyContractThrowLease(id) {
  return request({
    url: '/lease/property-contract-throw-lease/delete?id=' + id,
    method: 'delete'
  })
}

/**
 * 导出物业合同退租
 */
export function exportPropertyContractThrowLease(params) {
  return request({
    url: '/lease/property-contract-throw-lease/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
