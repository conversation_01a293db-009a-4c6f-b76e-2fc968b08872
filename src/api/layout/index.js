import request from '@/utils/request';
import { mapTree } from 'ele-admin-plus/es';
import { toFormData } from '@/utils/common';

/**
 * 获取当前登录用户的个人信息/权限/角色
 */
export async function getUserInfo() {
  const res = await request.get('/getInfo');
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 获取当前登录用户的菜单
 */
export async function getUserMenu(res) {
  const temp = res;
  // 一级菜单去掉父级
  temp.forEach((item, i) => {
    if (item.path === '/') {
      temp[i] = item.children[0];
    }
  });
  // 增加首页
  temp.unshift({
    path: '/index',
    component: 'index',
    meta: { title: '首页', icon: 'HomeOutlined' }
  });
  // 增加个人中心
  temp.push({
    path: '/profile',
    component: 'profile',
    meta: {
      title: '个人中心',
      icon: 'UserOutlined',
      active: '/index',
      hide: true
    }
  });
  temp.push({
    path: '/notify-message',
    component: 'system/notify/my',
    meta: {
      title: '我的站内信',
      icon: 'UserOutlined',
      active: '/index',
      hide: true
    }
  });
  // 修改图标
  return mapTree(temp, (item) => {
    return {
      ...item,
      meta: {
        ...item.meta,
        icon: ruoYiIcons[item.meta.icon] ?? item.meta.icon
      }
    };
  });
}

/**
 * 修改当前登录用户的密码
 */
export async function updatePassword(data) {
  const res = await request.put(
    '/system/user/profile/updatePwd',
    toFormData(data)
  );
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/** 若依默认菜单图标名称 */
export const ruoYiIcons = {
  system: 'SettingOutlined',
  user: 'UserOutlined',
  peoples: 'IdcardOutlined',
  'tree-table': 'AppstoreOutlined',
  tree: 'CityOutlined',
  post: 'SuitcaseOutlined',
  dict: 'BookOutlined',
  edit: 'ControlOutlined',
  message: 'MessageOutlined',
  log: 'LogOutlined',
  form: 'FileOutlined',
  logininfor: 'CalendarOutlined',
  monitor: 'DashboardOutlined',
  online: 'ConnectionOutlined',
  job: 'TimerOutlined',
  druid: 'FundOutlined',
  server: 'AnalysisOutlined',
  redis: 'ClusterOutlined',
  'redis-list': 'DatabaseOutlined',
  tool: 'AppstoreAddOutlined',
  build: 'FormOutlined',
  code: 'CodeOutlined',
  swagger: 'LinkOutlined',
  guide: 'LinkOutlined'
};
