import request from '@/utils/request/server';

/**
 * 获取当前登录用户信息
 */
export const getUserProfile = () => {
  return request.get({ url: '/system/user/profile/get' })
}

// 修改用户个人信息
export const updateUserProfile = (data) => {
  return request.put({ url: '/system/user/profile/update', data })
}
// 用户密码重置
export const updateUserPassword = (oldPassword, newPassword) => {
  return request.put({
    url: '/system/user/profile/update-password',
    data: {
      oldPassword: oldPassword,
      newPassword: newPassword
    }
  })
}
/**
 * 修改当前登录用户头像
 */
export const uploadAvatar = (data) => {
  return request.upload({ url: '/system/user/profile/update-avatar', data: data })
}
