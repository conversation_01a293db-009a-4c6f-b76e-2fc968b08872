import request from '@/utils/request/server';

// 活动参与人 VO
export interface ActivityJoinVO {
  id: number; // id
  activityId: number; // 活动id
  userId: number; // 参与用户的id
  nickName: string; // 参与用户的名称
  userProfile: string; // 参与用户的头像
  joinTime: Date; // 参与时间
  joinCode: string; // 报名编号
  qrCode: string; // 参与二维码
}

// 活动参与人 API
// 查询活动参与人分页
export const getActivityJoinPage = async (params: any) => {
  return await request.get({ url: `/cux/activity-join/page`, params });
};

// 查询活动参与人详情
export const getActivityJoin = async (id: number) => {
  return await request.get({ url: `/cux/activity-join/get?id=` + id });
};

// 新增活动参与人
export const createActivityJoin = async (data: ActivityJoinVO) => {
  return await request.post({ url: `/cux/activity-join/create`, data });
};

// 修改活动参与人
export const updateActivityJoin = async (data: ActivityJoinVO) => {
  return await request.put({ url: `/cux/activity-join/update`, data });
};

// 删除活动参与人
export const deleteActivityJoin = async (id: number) => {
  return await request.delete({ url: `/cux/activity-join/delete?id=` + id });
};

// 导出活动参与人 Excel
export const exportActivityJoin = async (params) => {
  return await request.download({
    url: `/cux/activity-join/export-excel`,
    params
  });
};
