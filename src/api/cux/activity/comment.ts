import request from '@/utils/request/server';

// 活动评论 VO
export interface ActivityCommentVO {
  id: number; // id
  activityId: number; // 活动id
  userId: number; // v用户的id
  nickName: string; // 评论用户的名称
  userProfile: string; // 评论用户的头像
  commentTime: Date; // 评论时间
  parentId: number; // 上级id
  commentDetail: string; // 评论详细
}

// 活动评论 API
// 查询活动评论分页
export const getActivityCommentPage = async (params: any) => {
  return await request.get({ url: `/cux/activity-comment/page`, params });
};

// 查询活动评论详情
export const getActivityComment = async (id: number) => {
  return await request.get({ url: `/cux/activity-comment/get?id=` + id });
};

// 新增活动评论
export const createActivityComment = async (data: ActivityCommentVO) => {
  return await request.post({ url: `/cux/activity-comment/create`, data });
};

// 修改活动评论
export const updateActivityComment = async (data: ActivityCommentVO) => {
  return await request.put({ url: `/cux/activity-comment/update`, data });
};

// 删除活动评论
export const deleteActivityComment = async (id: number) => {
  return await request.delete({ url: `/cux/activity-comment/delete?id=` + id });
};

// 导出活动评论 Excel
export const exportActivityComment = async (params) => {
  return await request.download({
    url: `/cux/activity-comment/export-excel`,
    params
  });
};
