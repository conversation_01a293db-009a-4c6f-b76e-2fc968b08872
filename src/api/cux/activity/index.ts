import request from '@/utils/request/server';

// 活动列 VO
export interface ActivityVO {
  id: number; // id
  title: string; // 活动标题
  startTime: Date; // 活动开始时间
  endTime: Date; // 活动结束时间
  joinEndTime: Date; // 报名截止时间
  address: string; // 活动地址
  warmReminder: string; // 温馨提示
  label: string; // 活动标签
  detail: string; // 详细描述
  imageUrl: string; // 活动海报
  status: number; // 状态（0正常 1停用）
  replayUrl: string; // 活动回放地址
  hotFlag: number; // 0正常 1热门
}

// 活动列 API
// 查询活动列分页
export const getActivityPage = async (params: any) => {
  return await request.get({ url: `/cux/activity/page`, params });
};

// 查询活动列详情
export const getActivity = async (id: number) => {
  return await request.get({ url: `/cux/activity/get?id=` + id });
};

// 新增活动列
export const createActivity = async (data: ActivityVO) => {
  return await request.post({ url: `/cux/activity/create`, data });
};

// 修改活动列
export const updateActivity = async (data: ActivityVO) => {
  return await request.put({ url: `/cux/activity/update`, data });
};

// 删除活动列
export const deleteActivity = async (id: number) => {
  return await request.delete({ url: `/cux/activity/delete?id=` + id });
};

// 获取活动二维码
export const getActivityCode = async (id: number) => {
  return await request.get({ url: `/cux/activity/code?id=` + id });
};
// 导出活动列 Excel
export const exportActivity = async (params) => {
  return await request.download({ url: `/cux/activity/export-excel`, params });
};
