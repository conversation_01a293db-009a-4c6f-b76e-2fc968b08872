import request from '@/utils/request/server';

// 首页轮播图 VO
export interface CarouselVO {
  id: number; // id
  imgaeUrl: string; // 轮播图地址
  status: number; // 专家状态（0正常 1停用）
  link: string; // 链接信息
}

// 首页轮播图 API
// 查询首页轮播图分页
export const getCarouselPage = async (params: any) => {
  return await request.get({ url: `/cux/carousel/page`, params });
};

// 查询首页轮播图详情
export const getCarousel = async (id: number) => {
  return await request.get({ url: `/cux/carousel/get?id=` + id });
};

// 新增首页轮播图
export const createCarousel = async (data: CarouselVO) => {
  return await request.post({ url: `/cux/carousel/create`, data });
};

// 修改首页轮播图
export const updateCarousel = async (data: CarouselVO) => {
  return await request.put({ url: `/cux/carousel/update`, data });
};

// 删除首页轮播图
export const deleteCarousel = async (id: number) => {
  return await request.delete({ url: `/cux/carousel/delete?id=` + id });
};

// 导出首页轮播图 Excel
export const exportCarousel = async (params) => {
  return await request.download({ url: `/cux/carousel/export-excel`, params });
};
