import request from '@/utils/request/server';

// 需求 VO
export interface DemandVO {
  id: number // id
  title: string // 需求标题
  demandDate: Date // 需求日期
  status: number // 需求状态（0正常 1停用）
  label: string // 标签
  detail: string // 详细描述
  remark: string // 备注
}

  // 查询需求分页
  export const getDemandPage = async (params: any) => {
    return await request.get({ url: `/cux/demand/page`, params });
  }

  // 查询需求详情
  export const  getDemand = async (id: number) => {
    return await request.get({ url: `/cux/demand/get?id=` + id });
  }

  // 新增需求
  export const createDemand = async (data: DemandVO) => {
    return await request.post({ url: `/cux/demand/create`, data });
  }

  // 修改需求
  export const updateDemand = async (data: DemandVO) => {
    return await request.put({ url: `/cux/demand/update`, data });
  }

  // 删除需求
  export const deleteDemand = async (id: number) => {
    return await request.delete({ url: `/cux/demand/delete?id=` + id });
  }

  // 导出需求 Excel
  export const exportDemand = async (params) => {
    return await request.download({ url: `/cux/demand/export-excel`, params });
  }
