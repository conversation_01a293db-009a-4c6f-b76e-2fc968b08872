import request from '@/utils/request/server';

// 查询食堂菜单详情
export const getRestaurant = async (id: number) => {
  return await request.get({ url: `/cux/restaurant/get?id=` + id });
};
export const getRestaurantPage = async (params) => {
  return await request.get({ url: `/cux/restaurant/page`, params });
};
// 新增食堂菜单
export const createRestaurant = async (data) => {
  return await request.post({ url: `/cux/restaurant/create`, data });
};
// 修改食堂菜单
export const updateRestaurant = async (data) => {
  return await request.put({ url: `/cux/restaurant/update`, data });
};
// 查询食堂菜单详情
export const checkRestaurantEditFlag = async (id: number) => {
  return await request.get({ url: `/cux/restaurant/canEdit` });
};
