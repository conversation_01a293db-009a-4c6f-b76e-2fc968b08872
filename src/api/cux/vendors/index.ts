import request from '@/utils/request/server';

// 服务商列 VO
export interface VendorsVO {
  id: number; // ID
  vendorName: string; // 服务商名称
  vendorDesc: byte[]; // 服务商详细
  status: number; // 状态（0正常 1停用）
}

// 服务商列 API
// 查询服务商列分页
export const getVendorsPage = async (params: any) => {
  return await request.get({ url: `/cux/vendors/page`, params });
};

// 查询服务商列详情
export const getVendors = async (id: number) => {
  return await request.get({ url: `/cux/vendors/get?id=` + id });
};

// 新增服务商列
export const createVendors = async (data: VendorsVO) => {
  return await request.post({ url: `/cux/vendors/create`, data });
};

// 修改服务商列
export const updateVendors = async (data: VendorsVO) => {
  return await request.put({ url: `/cux/vendors/update`, data });
};

// 删除服务商列
export const deleteVendors = async (id: number) => {
  return await request.delete({ url: `/cux/vendors/delete?id=` + id });
};

// 导出服务商列 Excel
export const exportVendors = async (params) => {
  return await request.download({ url: `/cux/vendors/export-excel`, params });
};

// 查询可分配服务商
export const getVendorsAssign = async (params: any) => {
  return await request.get({ url: `/cux/vendors/assign`, params });
};
export const assignVendors = async (data) => {
  return await request.post({ url: `/cux/server-vendors/save`, data });
};
