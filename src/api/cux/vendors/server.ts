import request from '@/utils/request/server';

// 服务商分配列 VO
export interface ServerVendorsVO {
  id: number; // ID
  serverId: number; // 服务ID
  vendorId: number; // 服务商ID
  vendorName: string; // 服务商名称
  status: number; // 状态（0正常 1停用）
}

// 服务商分配列 API
// 查询服务商分配列分页
export const getServerVendorsPage = async (params: any) => {
  return await request.get({ url: `/cux/server-vendors/page`, params });
};

// 查询服务商分配列详情
export const getServerVendors = async (id: number) => {
  return await request.get({ url: `/cux/server-vendors/get?id=` + id });
};

// 新增服务商分配列
export const createServerVendors = async (data: ServerVendorsVO) => {
  return await request.post({ url: `/cux/server-vendors/create`, data });
};

// 修改服务商分配列
export const updateServerVendors = async (data: ServerVendorsVO) => {
  return await request.put({ url: `/cux/server-vendors/update`, data });
};

// 删除服务商分配列
export const deleteServerVendors = async (id: number) => {
  return await request.delete({ url: `/cux/server-vendors/delete?id=` + id });
};

// 导出服务商分配列 Excel
export const exportServerVendors = async (params) => {
  return await request.download({
    url: `/cux/server-vendors/export-excel`,
    params
  });
};
