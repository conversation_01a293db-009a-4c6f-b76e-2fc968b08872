import request from '@/utils/request/server';

// 专家 VO
export interface ExpertVO {
  id: number; // id
  name: string; // 姓名
  email: string; // 用户邮箱
  mobile: string; // 手机号码
  avatar: string; // 头像地址
  status: number; // 专家状态（0正常 1停用）
  sex: number; // 性别
  label: string; // 标签
  detail: string; // 详细描述
  remark: string; // 备注
}

// 查询专家分页
export const getExpertPage = async (params: any) => {
  return await request.get({ url: `/cux/expert/page`, params });
};

// 查询专家详情
export const getExpert = async (id: number) => {
  return await request.get({ url: `/cux/expert/get?id=` + id });
};

// 新增专家
export const createExpert = async (data: ExpertVO) => {
  return await request.post({ url: `/cux/expert/create`, data });
};

// 修改专家
export const updateExpert = async (data: ExpertVO) => {
  return await request.put({ url: `/cux/expert/update`, data });
};

// 删除专家
export const deleteExpert = async (id: number) => {
  return await request.delete({ url: `/cux/expert/delete?id=` + id });
};

// 导出专家 Excel
export const exportExpert = async (params) => {
  return await request.download({ url: `/cux/expert/export-excel`, params });
};

export const test123 = async () => {
  return await request.get({
    url: '/system/canvans/test'
  });
};
