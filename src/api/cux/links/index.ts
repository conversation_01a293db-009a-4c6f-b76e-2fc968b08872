import request from '@/utils/request/server';

// 外部小程序链接 API
// 查询外部小程序链接分页
export const getServerLinksPage = async (params: any) => {
  return await request.get({ url: `/cux/server-links/list`, params });
};

// 查询外部小程序链接详情
export const getServerLink = async (id: number) => {
  return await request.get({ url: `/cux/server-links/get?id=` + id });
};

// 新增外部小程序链接
export const createServerLink = async (data) => {
  return await request.post({ url: `/cux/server-links/create`, data });
};

// 修改外部小程序链接
export const updateServerLink = async (data) => {
  return await request.put({ url: `/cux/server-links/update`, data });
};

// 删除外部小程序链接
export const deleteServerLink = async (id: number) => {
  return await request.delete({
    url: `/cux/server-links/delete?id=` + id
  });
};
