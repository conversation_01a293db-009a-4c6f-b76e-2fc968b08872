import request from '@/utils/request/server';

// 查询需求分页
export const getServerPage = async (params: any) => {
  return await request.get({ url: `/cux/server/page`, params });
};

// 查询需求详情
export const getServer = async (id: number) => {
  return await request.get({ url: `/cux/server/get?id=` + id });
};

// 新增需求
export const createServer = async (data) => {
  return await request.post({ url: `/cux/server/create`, data });
};

// 修改需求
export const updateServer = async (data) => {
  return await request.put({ url: `/cux/server/update`, data });
};

// 删除需求
export const deleteServer = async (id: number) => {
  return await request.delete({ url: `/cux/server/delete?id=` + id });
};

// 导出需求 Excel
export const exportServer = async (params) => {
  return await request.download({ url: `/cux/server/export-excel`, params });
};
