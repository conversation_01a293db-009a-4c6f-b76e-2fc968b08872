import request from '@/utils/request/server';

// 微信消息记录 VO
export interface WxMsgLogVO {
  id: number; // 编号
  accountId: number; // 微信渠道id
  toUser: string; // 接收人
  templateId: number; // 模板id
  templateCode: string; // 模板编码
  templateName: string; // 模版名称
  templateType: string; // 模版类型
  content: string; // 发送内容
  params: string; // 发送参数
  sendStatus: number; // 发送状态
  sendTime: Date; // 发送时间
  sendMessageId: string; // 发送返回的消息 ID
  sendException: string; // 发送异常
}

// 微信消息记录 API
// 查询微信消息记录分页
export const getWxMsgLogPage = async (params: any) => {
  return await request.get({ url: `/system/wx-msg-log/page`, params });
};

// 查询微信消息记录详情
export const getWxMsgLog = async (id: number) => {
  return await request.get({ url: `/system/wx-msg-log/get?id=` + id });
};

// 新增微信消息记录
export const createWxMsgLog = async (data: WxMsgLogVO) => {
  return await request.post({ url: `/system/wx-msg-log/create`, data });
};

// 修改微信消息记录
export const updateWxMsgLog = async (data: WxMsgLogVO) => {
  return await request.put({ url: `/system/wx-msg-log/update`, data });
};

// 删除微信消息记录
export const deleteWxMsgLog = async (id: number) => {
  return await request.delete({ url: `/system/wx-msg-log/delete?id=` + id });
};

// 导出微信消息记录 Excel
export const exportWxMsgLog = async (params) => {
  return await request.download({
    url: `/system/wx-msg-log/export-excel`,
    params
  });
};
