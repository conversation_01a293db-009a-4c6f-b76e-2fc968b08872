import request from '@/utils/request/server';

// 微信消息模版管理 VO
export interface WxMsgTemplateVO {
  id: number; // id
  accountId: number; // 微信渠道id
  templateType: number; // 模版类型
  status: number; // 开启状态（0正常 1停用）
  content: string; // 模版内容
  params: string; // 参数数组
}

// 微信消息模版管理 API
// 查询微信消息模版管理分页
export const getWxMsgTemplatePage = async (params: any) => {
  return await request.get({ url: `/system/wx-msg-template/page`, params });
};

// 查询微信消息模版管理详情
export const getWxMsgTemplate = async (id: number) => {
  return await request.get({ url: `/system/wx-msg-template/get?id=` + id });
};

// 新增微信消息模版管理
export const createWxMsgTemplate = async (data: WxMsgTemplateVO) => {
  return await request.post({ url: `/system/wx-msg-template/create`, data });
};

// 修改微信消息模版管理
export const updateWxMsgTemplate = async (data: WxMsgTemplateVO) => {
  return await request.put({ url: `/system/wx-msg-template/update`, data });
};

// 删除微信消息模版管理
export const deleteWxMsgTemplate = async (id: number) => {
  return await request.delete({
    url: `/system/wx-msg-template/delete?id=` + id
  });
};

// 基于模版发送消息测试
export const sendMsgTest = async (data) => {
  return await request.post({ url: `/system/wx-msg-template/send-test`, data });
};
