import request from '@/utils/request/server';

// 微信账户管理 VO
export interface WxAccountVO {
  id: number; // 编号
  code: string; // 微信渠道编码
  name: string; // 微信渠道名称
  type: number; // 类型（0微信 1企业微信）
  status: number; // 开启状态（0正常 1停用）
  appId: string; // 应用id
  appSecret: string; // 应用secret
  agentid: string; // 企微应用id
}

// 微信账户管理 API
// 查询微信账户管理分页
export const getWxAccountPage = async (params: any) => {
  return await request.get({ url: `/system/wx-account/page`, params });
};

// 查询微信账户精简列表
export const getWxAccountSimpleList = async (params: any) => {
  return await request.get({ url: `/system/wx-account/simple-list`, params });
};
// 查询微信账户管理详情
export const getWxAccount = async (id: number) => {
  return await request.get({ url: `/system/wx-account/get?id=` + id });
};

// 新增微信账户管理
export const createWxAccount = async (data: WxAccountVO) => {
  return await request.post({ url: `/system/wx-account/create`, data });
};

// 修改微信账户管理
export const updateWxAccount = async (data: WxAccountVO) => {
  return await request.put({ url: `/system/wx-account/update`, data });
};

// 删除微信账户管理
export const deleteWxAccount = async (id: number) => {
  return await request.delete({ url: `/system/wx-account/delete?id=` + id });
};

// 导出微信账户管理 Excel
export const exportWxAccount = async (params) => {
  return await request.download({
    url: `/system/wx-account/export-excel`,
    params
  });
};
