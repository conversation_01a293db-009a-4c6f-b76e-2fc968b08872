import request from '@/utils/request/server';

// 通知公告 VO
export interface NoticeVO {
  id: number // 公告ID
  title: string // 公告标题
  content: string // 公告内容
  type: number // 公告类型（1通知 2公告）
  approveMethod: number // 审批方式（0无需审批 1功能审批 2工作流审批 3外部系统审批）
  status: number // 公告状态（0新建 1已提交 2已发布 3审批拒绝）
  message: string // 消息
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 通知公告 API
export const NoticeApi = {
  // 查询通知公告分页
  getNoticePage: async (params: any) => {
    return await request.get({ url: `/system/notice/page`, params })
  },

  // 查询通知公告详情
  getNotice: async (id: number) => {
    return await request.get({ url: `/system/notice/get?id=` + id })
  },

  // 新增通知公告
  createNotice: async (data: NoticeVO) => {
    return await request.post({ url: `/system/notice/create`, data })
  },

  // 修改通知公告
  updateNotice: async (data: NoticeVO) => {
    return await request.put({ url: `/system/notice/update`, data })
  },

  // 删除通知公告
  deleteNotice: async (id: number) => {
    return await request.delete({ url: `/system/notice/delete?id=` + id })
  },

  // 导出通知公告 Excel
  exportNotice: async (params) => {
    return await request.download({ url: `/system/notice/export-excel`, params })
  },

  // 提交通知公告
  submitNotice: async (id: number) => {
    return await request.get({ url: `/system/notice/submit?ids=` + id })
  },

  // 撤回通知公告
  withdrawNotice: async (id: number) => {
    return await request.get({ url: `/system/notice/withdraw?ids=` + id })
  },

  // 失效通知公告
  loseNotice: async (id: number) => {
    return await request.get({ url: `/system/notice/lose?ids=` + id })
  },

  // 查询通知公告分页-创建
  getNoticePageCreate: async (params: any) => {
    return await request.get({ url: `/system/notice/page-create`, params })
  },

  // 导出通知公告-创建 Excel
  exportNoticeCreate: async (params) => {
    return await request.download({ url: `/system/notice/export-excel-create`, params })
  },

  // 查询通知公告分页-查询
  getNoticePageQuery: async (params: any) => {
    return await request.get({ url: `/system/notice/page-query`, params })
  },

  // 导出通知公告-查询 Excel
  exportNoticeQuery: async (params) => {
    return await request.download({ url: `/system/notice/export-excel-query`, params })
  },

  // 查询通知公告分页-审批
  getNoticePageApprove: async (params: any) => {
    return await request.get({ url: `/system/notice/page-approve`, params })
  },

  // 导出通知公告-审批 Excel
  exportNoticeApprove: async (params) => {
    return await request.download({ url: `/system/notice/export-excel-approve`, params })
  },


  // ==================== 子表（通知公告分配行表-角色） ====================
  
    // 获得通知公告分配行表-角色列表 - 不分页
    getNoticeAssignRoleListByNoticeId: async (noticeId) => {
      return await request.get({ url: `/system/notice/notice-assign-role/list-by-notice-id?noticeId=` + noticeId })
    },
  
    // 获得通知公告分配行表-角色列表 - 分页
    getNoticeAssignRolePageByNoticeId: async (params: any) => {
      return await request.get({ url: `/system/notice/notice-assign-role/page-by-notice-id`, params })
    },
  
     // 分配通知公告分配行表-角色列表 - 更新
     setNoticeAssignRoleListByNoticeId: async (data) => {
      return await request.post({ url: `/system/notice/notice-assign-role/assign-role-id`, data })
    },
  
    // 删除通知公告分配行表-角色
    deleteNoticeAssignRole: async (data) => {
      return await request.post({ url: `/system/notice/notice-assign-role/delete-assign-role`, data })
    },
  
    // 新增通知公告分配行表-角色
    addNoticeAssignRole: async (data) => {
      return await request.post({ url: `/system/notice/notice-assign-role/add-assign-role`, data })
    },
  
    // 新增通知公告分配行表-角色
    getSimpleRoleList: async (params: any) => {
      return await request.get({ url: `/system/notice/role/simple-page`, params })
    },
  
  // ==================== 子表（通知公告分配行表-公司） ====================
  
    // 获得通知公告分配行表-公司列表 - 不分页
    getNoticeAssignCompanyListByNoticeId: async (noticeId) => {
      return await request.get({ url: `/system/notice/notice-assign-company/list-by-notice-id?noticeId=` + noticeId })
    },
  
    // 获得通知公告分配行表-公司列表- 分页
    getNoticeAssignCompanyPageByNoticeId: async (params: any) => {
      return await request.get({ url: `/system/notice/notice-assign-company/page-by-notice-id` , params })
    },
  
    // 分配通知公告分配行表-公司列表 - 更新
    setNoticeAssignCompanyListByNoticeId: async (data) => {
      return await request.post({ url: `/system/notice/notice-assign-company/assign-company-id`, data })
    },
  
    // 删除通知公告分配行表-公司
    deleteNoticeAssignCompany: async (data) => {
      return await request.post({ url: `/system/notice/notice-assign-company/delete-assign-company`, data })
    },
  
    // 新增通知公告分配行表-公司
    addNoticeAssignCompany: async (data) => {
      return await request.post({ url: `/system/notice/notice-assign-company/add-assign-company`, data })
    },
  
    // 新增通知公告分配行表-公司
    getSimpleCompanyList: async (params: any) => {
      return await request.get({ url: `/system/notice/company/simple-page`, params })
    },

}
