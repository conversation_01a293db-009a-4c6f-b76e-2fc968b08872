import request from '@/utils/request/server';

// 通知公告分配头表-角色&人员 VO
export interface NoticeTypeAssignVO {
  id: number // 分配id
  typeName: string // 公告类型名称
  typeValue: string // 公告类型值
  status: number // 公告状态（0正常 1关闭）
  atributeVarchar1: string // 备用字段1
  atributeVarchar2: string // 备用字段2
  atributeVarchar3: string // 备用字段3
  atributeVarchar4: string // 备用字段4
  atributeVarchar5: string // 备用字段5
}

// 通知公告分配头表-角色&人员 API
export const NoticeTypeAssignApi = {
  // 查询通知公告分配头表-角色&人员分页
  getNoticeTypeAssignPage: async (params: any) => {
    return await request.get({ url: `/system/notice-type-assign/page`, params })
  },

  // 查询通知公告分配头表-角色&人员详情
  getNoticeTypeAssign: async (id: number) => {
    return await request.get({ url: `/system/notice-type-assign/get?id=` + id })
  },

  // 查询通知公告分配头表-简易信息
  getNoticeTypeAssignSimple: async (id: number) => {
    return await request.get({ url: `/system/notice-type-assign/get-simple`})
  },

  // 新增通知公告分配头表-角色&人员
  createNoticeTypeAssign: async (data: NoticeTypeAssignVO) => {
    return await request.post({ url: `/system/notice-type-assign/create`, data })
  },

  // 修改通知公告分配头表-角色&人员
  updateNoticeTypeAssign: async (data: NoticeTypeAssignVO) => {
    return await request.put({ url: `/system/notice-type-assign/update`, data })
  },

  // 删除通知公告分配头表-角色&人员
  deleteNoticeTypeAssign: async (id: number) => {
    return await request.delete({ url: `/system/notice-type-assign/delete?id=` + id })
  },

  // 导出通知公告分配头表-角色&人员 Excel
  exportNoticeTypeAssign: async (params) => {
    return await request.download({ url: `/system/notice-type-assign/export-excel`, params })
  },

// ==================== 子表（通知公告分配行表-角色） ====================

  // 获得通知公告分配行表-角色列表 - 不分页
  getNoticeTypeAssignRoleListByNoticeTypeAssignId: async (noticeTypeAssignId) => {
    return await request.get({ url: `/system/notice-type-assign/notice-type-assign-role/list-by-notice-type-assign-id?noticeTypeAssignId=` + noticeTypeAssignId })
  },

  // 获得通知公告分配行表-角色列表 - 分页
  getNoticeTypeAssignRolePageByNoticeTypeAssignId: async (params: any) => {
    return await request.get({ url: `/system/notice-type-assign/notice-type-assign-role/page-by-notice-type-assign-id`, params })
  },

   // 分配通知公告分配行表-角色列表 - 更新
   setNoticeTypeAssignRoleListByNoticeTypeAssignId: async (data) => {
    return await request.post({ url: `/system/notice-type-assign/notice-type-assign-role/assign-role-id`, data })
  },

  // 删除通知公告分配行表-角色
  deleteNoticeTypeAssignRole: async (data) => {
    return await request.post({ url: `/system/notice-type-assign/notice-type-assign-role/delete-assign-role`, data })
  },

  // 新增通知公告分配行表-角色
  addNoticeTypeAssignRole: async (data) => {
    return await request.post({ url: `/system/notice-type-assign/notice-type-assign-role/add-assign-role`, data })
  },

  // 新增通知公告分配行表-角色
  getSimpleRoleList: async (params: any) => {
    return await request.get({ url: `/system/notice-type-assign/role/simple-page`, params })
  },

// ==================== 子表（通知公告分配行表-用户） ====================

  // 获得通知公告分配行表-用户列表 - 不分页
  getNoticeTypeAssignUserListByNoticeTypeAssignId: async (noticeTypeAssignId) => {
    return await request.get({ url: `/system/notice-type-assign/notice-type-assign-user/list-by-notice-type-assign-id?noticeTypeAssignId=` + noticeTypeAssignId })
  },

  // 获得通知公告分配行表-用户列表- 分页
  getNoticeTypeAssignUserPageByNoticeTypeAssignId: async (params: any) => {
    return await request.get({ url: `/system/notice-type-assign/notice-type-assign-user/page-by-notice-type-assign-id` , params })
  },

  // 分配通知公告分配行表-用户列表 - 更新
  setNoticeTypeAssignUserListByNoticeTypeAssignId: async (data) => {
    return await request.post({ url: `/system/notice-type-assign/notice-type-assign-user/assign-user-id`, data })
  },

  // 删除通知公告分配行表-用户
  deleteNoticeTypeAssignUser: async (data) => {
    return await request.post({ url: `/system/notice-type-assign/notice-type-assign-user/delete-assign-user`, data })
  },

  // 新增通知公告分配行表-用户
  addNoticeTypeAssignUser: async (data) => {
    return await request.post({ url: `/system/notice-type-assign/notice-type-assign-user/add-assign-user`, data })
  },

  // 新增通知公告分配行表-用户
  getSimpleUserList: async (params: any) => {
    return await request.get({ url: `/system/notice-type-assign/user/simple-page`, params })
  },
 
  
}
