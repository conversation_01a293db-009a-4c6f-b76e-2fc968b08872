import request from '@/utils/request/server';

// 公司管理 VO
export interface CompanyVO {
  id: number // 公司id
  code: string // 公司编码
  name: string // 公司名称
  address: string // 公司地址
  phone: string // 联系电话
  email: string // 邮箱
  businessLicenseNumber: string // 营业执照编号
  contacts: string // 联系人
  contactsInformation: string // 联系方式
  parentId: number // 父公司id
  sort: number // 显示顺序
  leaderUserId: number // 负责人
  status: number // 公司状态（0正常 1停用）
}

// 公司管理 API
  // 查询公司管理分页
export const getCompanyPage = async (params: any) => {
    return await request.get({ url: `/system/company/page`, params })
};

  // 查询公司管理列表
export const getCompanyList = async (params: any) => {
    return await request.get({ url: '/system/company/list', params })
};

  // 查询公司管理详情
export const getCompany = async (id: number) => {
    return await request.get({ url: `/system/company/get?id=` + id })
};

  // 新增公司管理
export const createCompany = async (data: CompanyVO) => {
    return await request.post({ url: `/system/company/create`, data })
};

  // 修改公司管理
export const updateCompany = async (data: CompanyVO) => {
    return await request.put({ url: `/system/company/update`, data })
};

  // 删除公司管理
export const deleteCompany = async (id: number) => {
    return await request.delete({ url: `/system/company/delete?id=` + id })
};

  // 导出公司管理 Excel
export const exportCompany = async (params) => {
    return await request.download({ url: `/system/company/export-excel`, params })
};
