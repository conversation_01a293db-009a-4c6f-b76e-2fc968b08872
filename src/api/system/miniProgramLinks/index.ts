import request from '@/utils/request/server';

// 外部小程序链接 VO
export interface MiniProgramLinksVO {
  id: number // ID
  appId: string // 小程序id
  title: string // 小程序标题
  page: string // 小程序打开默认页面
  image: string // 小程序图片
  envVersion: string // 小程序版本
  sort: number // 显示顺序
  status: number // 状态（0正常 1停用）
}

// 外部小程序链接 API
  // 查询外部小程序链接分页
export const getMiniProgramLinksPage = async (params: any) => {
    return await request.get({ url: `/system/mini-program-links/page`, params })
};

  // 查询外部小程序链接详情
export const getMiniProgramLinks = async (id: number) => {
    return await request.get({ url: `/system/mini-program-links/get?id=` + id })
};

  // 新增外部小程序链接
export const createMiniProgramLinks = async (data: MiniProgramLinksVO) => {
    return await request.post({ url: `/system/mini-program-links/create`, data })
};

  // 修改外部小程序链接
export const updateMiniProgramLinks = async (data: MiniProgramLinksVO) => {
    return await request.put({ url: `/system/mini-program-links/update`, data })
};

  // 删除外部小程序链接
export const deleteMiniProgramLinks = async (id: number) => {
    return await request.delete({ url: `/system/mini-program-links/delete?id=` + id })
};

  // 导出外部小程序链接 Excel
export const exportMiniProgramLinks = async (params) => {
    return await request.download({ url: `/system/mini-program-links/export-excel`, params })
};