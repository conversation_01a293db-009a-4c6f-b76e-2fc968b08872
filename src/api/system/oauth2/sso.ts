import request from '@/utils/request/server';

// 获取sso地址
export const getSsoUrl = async () => {
  return request.get({
    url: '/system/sso/login_url?redirectUri=' + location.href
  });
};
// 获取ssoToken地址
export const getSsoToken = async (code) => {
  return request.get({
    url: '/system/sso/access-token?code=' + code
  });
};
export const getSsoRefreshToken = async (refreshToken) => {
  return request.get({
    url: '/system/sso/refresh-token?refreshToken=' + refreshToken
  });
};
// 获取sso地址
export const getSsoLogOutUrl = async () => {
  return request.get({
    url: '/system/sso/logout_url?redirectUri=' + location.href
  });
};
// 获取用户信息
export const getSsoUseInfo = async () => {
  return request.get({
    url: '/system/user/sso/get'
  });
};