import request from '@/utils/request/server';

// 外部系统 VO
export interface ExternalSystemVO {
  id: number // 编号
  code: string // 编码
  description: string // 描述信息
  information: string // 外部系统信息
  isEnable: boolean // 1=正常0=禁用
}

// 外部系统 API
  // 查询外部系统分页
export const getExternalSystemPage = async (params: any) => {
    return await request.get({ url: `/external/system/page`, params })
};

  // 查询外部系统详情
export const getExternalSystem = async (id: number) => {
    return await request.get({ url: `/external/system/get?id=` + id })
};

  // 新增外部系统
export const createExternalSystem = async (data: ExternalSystemVO) => {
    return await request.post({ url: `/external/system/create`, data })
};

  // 修改外部系统
export const updateExternalSystem = async (data: ExternalSystemVO) => {
    return await request.put({ url: `/external/system/update`, data })
};

  // 删除外部系统
export const deleteExternalSystem = async (id: number) => {
    return await request.delete({ url: `/external/system/delete?id=` + id })
};

  // 导出外部系统 Excel
export const exportExternalSystem = async (params) => {
    return await request.download({ url: `/external/system/export-excel`, params })
};
