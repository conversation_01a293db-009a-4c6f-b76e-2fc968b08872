import request from '@/utils/request/server';

// 查询租户列表
export const getTenantPage = (params) => {
  return request.get({ url: '/system/tenant/page', params })
}

// 查询租户详情
export const getTenant = (id) => {
  return request.get({ url: '/system/tenant/get?id=' + id })
}

// 新增租户
export const createTenant = (data) => {
  return request.post({ url: '/system/tenant/create', data })
}

// 修改租户
export const updateTenant = (data) => {
  return request.put({ url: '/system/tenant/update', data })
}

// 删除租户
export const deleteTenant = (id) => {
  return request.delete({ url: '/system/tenant/delete?id=' + id })
}

// 导出租户
export const exportTenant = (params) => {
  return request.download({ url: '/system/tenant/export-excel', params })
}
