import request from '@/utils/request/server';

export const CodeApi = {
  // 查询系统编码分页
  getCodePage: async (params) => {
    return await request.get({ url: `/system/code/page`, params });
  },

  // 查询系统编码详情
  getCode: async (id) => {
    return await request.get({ url: `/system/code/get?id=` + id });
  },
  // 测试编码
  testCode: async (code) => {
    return await request.get({ url: `/system/code/test?code=` + code });
  },
  // 新增系统编码
  createCode: async (data) => {
    return await request.post({ url: `/system/code/create`, data });
  },

  // 修改系统编码
  updateCode: async (data) => {
    return await request.put({ url: `/system/code/update`, data });
  },

  // 删除系统编码
  deleteCode: async (id) => {
    return await request.delete({ url: `/system/code/delete?id=` + id });
  },

  // 导出系统编码 Excel
  exportCode: async (params) => {
    return await request.download({ url: `/system/code/export-excel`, params });
  },

  // 获得系统编码明细列表
  getCodeRuleListByCodeId: async (codeId) => {
    return await request.get({
      url: `/system/code/code-rule/list-by-code-id?codeId=` + codeId
    });
  },

  // 删除系统编码明细列表
  deleteCodeRule: async (id) => {
    return await request.delete({ url: `/system/code-rule/delete?id=` + id });
  },
  // 查询系统编码规则列表
  getCodeRuleList: async (params) => {
    return await request.get({ url: `/system/code-rule/list`, params });
  },

  // 修改系统编码规则
  updateCodeRule: async (data) => {
    return await request.put({ url: `/system/code-rule/save`, data });
  },
  // 修改系统编码规则
  updateLineCodeRule: async (data) => {
    return await request.post({ url: `/system/code-rule/save-line`, data });
  }
};
