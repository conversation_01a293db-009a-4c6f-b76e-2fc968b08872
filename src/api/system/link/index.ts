import request from '@/utils/request/server';

// 查询岗位列表
export const getLinkPage = async (params: PageParam) => {
  return await request.get({ url: '/system/link/page', params });
};

// 获取岗位精简信息列表
export const getLinkList = async (params) => {
  return await request.get({ url: '/system/link/list', params });
};

// 查询岗位详情
export const getLink = async (id: number) => {
  return await request.get({ url: '/system/link/get?id=' + id });
};

// 新增岗位
export const createLink = async (data) => {
  return await request.post({ url: '/system/link/create', data });
};

// 修改岗位
export const updateLink = async (data) => {
  return await request.put({ url: '/system/link/update', data });
};

// 删除岗位
export const deleteLink = async (id: number) => {
  return await request.delete({ url: '/system/link/delete?id=' + id });
};
