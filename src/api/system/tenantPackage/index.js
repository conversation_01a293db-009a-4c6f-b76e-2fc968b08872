import request from '@/utils/request/server';

// 查询租户套餐列表
export const getTenantPackagePage = (params) => {
  return request.get({ url: '/system/tenant-package/page', params });
};

// 获得租户
export const getTenantPackage = (id) => {
  return request.get({ url: '/system/tenant-package/get?id=' + id });
};

// 新增租户套餐
export const createTenantPackage = (data) => {
  return request.post({ url: '/system/tenant-package/create', data });
};

// 修改租户套餐
export const updateTenantPackage = (data) => {
  return request.put({ url: '/system/tenant-package/update', data });
};

// 删除租户套餐
export const deleteTenantPackage = (id) => {
  return request.delete({ url: '/system/tenant-package/delete?id=' + id });
};
// 获取租户套餐精简信息列表
export const getTenantPackageList = () => {
  return request.get({ url: '/system/tenant-package/simple-list' });
};
