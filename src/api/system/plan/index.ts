import request from '@/utils/request/server';

// 创建需求
export const createUserPlan = async (data) => {
  return await request.post({
    url: '/system/user-plan/create',
    data: data
  });
};

// 更新需求
export const updateUserPlan = async (data) => {
  return await request.put({
    url: '/system/user-plan/update',
    data: data
  });
};

// 删除需求
export const deleteUserPlan = async (id: number) => {
  return await request.delete({
    url: '/system/user-plan/delete?id=' + id
  });
};

// 获得需求详细
export const getUserPlan = async (id: number) => {
  return await request.get({
    url: '/system/user-plan/get?id=' + id
  });
};

// 获得需求详细
export const getUserPlanPoint = async () => {
  return await request.get({
    url: '/system/user-plan/list'
  });
};

// 获得需求详细
export const getUserPlanList = (params) => {
  return request.get({ url: '/system/user-plan/list/all', params });
};
