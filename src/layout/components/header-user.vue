<!-- 用户信息 -->
<template>
  <ele-dropdown
    :items="[
      // {
      //   title: currentRole,
      //   command: 'profile',
      //   icon: UserOutlined
      // },
      {
        title: '个人信息',
        command: 'profile',
        icon: UserOutlined
      },
      // {
      //   title: '修改密码',
      //   command: 'password',
      //   icon: LockOutlined,
      //   iconStyle: { transform: 'translateY(-1px)' }
      // },
      {
        title: '退出登录',
        command: 'logout',
        icon: LogoutOutlined,
        divided: true
      }
    ]"
    :icon-props="{ size: 15 }"
    :popper-options="{
      modifiers: [{ name: 'offset', options: { offset: [0, 5] } }]
    }"
    @command="onUserDropClick"
  >
    <div class="header-avatar">
      <el-avatar
        :size="28"
        :src="loginUser.avatar"
        :icon="loginUser.avatar ? void 0 : UserOutlined"
        style="transform: translateY(-1px)"
      />
      <div
        class="hidden-sm-and-down"
        style="margin-left: 4px; line-height: 1.5"
      >
        {{ loginUser.nickname }}
      </div>
      <el-icon :size="13" style="margin: 0 -4px 0 2px">
        <ArrowDown />
      </el-icon>
    </div>
  </ele-dropdown>
  <!-- 修改密码弹窗 -->
  <password-modal v-model="passwordVisible" />
</template>

<script setup>
  import { useUserStore } from '@/store/modules/user';
  import { useRouter } from 'vue-router';
  import { ElMessageBox } from 'element-plus/es';
  import {
    ArrowDown,
    UserOutlined,
    LockOutlined,
    LogoutOutlined
  } from '@/components/icons';
  import PasswordModal from './password-modal.vue';

  const { push } = useRouter();
  const userStore = useUserStore();
  const currentRole = ref('abc');
  /** 是否显示修改密码弹窗 */
  const passwordVisible = ref(false);

  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});

  /** 用户信息下拉点击 */
  const onUserDropClick = (command) => {
    if (command === 'password') {
      passwordVisible.value = true;
    } else if (command === 'profile') {
      push('/profile');
    } else if (command === 'logout') {
      // 退出登录
      ElMessageBox.confirm('确定要退出登录吗?', '系统提示', {
        type: 'warning',
        draggable: true
      })
        .then(async () => {
          await userStore.loginOut();
        })
        .catch(() => {});
    }
  };
</script>

<style lang="scss" scoped>
  .header-avatar {
    display: flex;
    align-items: center;
    position: relative;
    height: 100%;
    outline: none;
  }
</style>
