<!-- 顶栏消息通知 -->
<template>
  <ele-popover
    :width="336"
    trigger="click"
    transition="el-zoom-in-top"
    :content-style="{ padding: 0 }"
    :body-style="{ overflow: 'hidden' }"
    :popper-style="{ maxWidth: 'calc(100% - 32px)' }"
    :popper-options="{
      modifiers: [{ name: 'offset', options: { offset: [0, 5] } }]
    }"
  >
    <template #reference>
      <div
        @click="getList"
        style="display: flex; align-items: center; height: 100%"
      >
        <el-badge
          :value="notifyStore.unreadCount"
          :hidden="!notifyStore.unreadCount"
          style="line-height: 1; padding: 4px 0"
        >
          <el-icon style="transform: scale(1.17) translateY(1px)">
            <BellOutlined />
          </el-icon>
        </el-badge>
      </div>
    </template>

    <el-scrollbar v-if="list.length" height="430px">
      <div class="list-wrapper">
        <div v-for="(item, index) in list" :key="index" class="list-item">
          <div class="list-item-icon" :style="{ background: '#60B2FC' }">
            <el-icon>
              <BellFilled />
            </el-icon>
          </div>
          <div class="list-item-body">
            <ele-ellipsis>{{ item.messageContent }}</ele-ellipsis>
            <ele-ellipsis type="placeholder" class="list-item-text">
              {{ formatDate(item.createTime) }}
            </ele-ellipsis>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <el-empty v-else description="已查看所有消息" :image-size="68" />
    <div class="bottom-tools">
      <div v-if="list.length" class="bottom-tool" @click="handleUpdateAll"
        >全部已读</div
      >
      <el-divider
        v-if="list.length"
        direction="vertical"
        style="margin: 0; width: 0"
      />
      <div class="bottom-tool" @click="getMore">查看更多</div>
    </div>
  </ele-popover>
</template>

<script setup>
  import { ref, nextTick } from 'vue';
  import { BellFilled } from '@element-plus/icons-vue';
  import { formatDate } from '@/utils/formatTime';
  import { useNotifyStore } from '@/store/modules/notify';
  import { storeToRefs } from 'pinia';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as NotifyMessageApi from '@/api/system/notify/message';
  import { BellOutlined } from '@/components/icons';
  const notifyStore = useNotifyStore();
  /** 标签页 */
  const tabRef = ref(null);
  const { push } = useRouter();

  /** 选项卡选中 */
  const active = ref('notice');

  /** 通知数据 */
  const notice = ref([]);

  const list = ref([]);
  /** 未读数量 */
  const { unreadCount } = storeToRefs(notifyStore);

  // 获得消息列表
  const getList = async () => {
    list.value = await NotifyMessageApi.getUnreadNotifyMessageList();
    notifyStore.setUnreadCount();
  };
  /** 清空通知 */
  const clearNotice = () => {
    notice.value = [];
    updateActiveBar();
  };

  /** 更新标签页指示线 */
  const updateActiveBar = () => {
    nextTick(() => {
      tabRef.value?.updateActiveBar?.();
    });
  };
  const getMore = () => {
    push('/notify-message');
  };
  /** 标记全部站内信已读 **/
  const handleUpdateAll = async () => {
    await NotifyMessageApi.updateAllNotifyMessageRead();
    list.value = [];
    notifyStore.setUnreadCount();
  };
  // ========== 初始化 =========
  onMounted(() => {
    // 首次加载小红点
    notifyStore.setUnreadCount();
    // 轮询刷新小红点
    setInterval(
      () => {
        notifyStore.setUnreadCount();
      },
      1000 * 60 * 2
    );
  });
</script>

<style lang="scss" scoped>
  .list-wrapper {
    padding-top: 8px;
    box-sizing: border-box;
  }

  .list-item {
    display: flex;
    padding: 14px 24px;
    box-sizing: border-box;
    transition: background-color 0.2s;
    cursor: pointer;

    .list-item-body {
      flex: 1;
      overflow: hidden;

      .list-item-text {
        margin-top: 6px;
      }
    }

    .list-item-icon {
      width: 32px;
      height: 32px;
      color: #fff;
      font-size: 16px;
      border-radius: 50%;
      text-align: center;
      background-color: #60b2fc;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    :deep(.el-avatar) {
      flex-shrink: 0;
    }

    .list-item-icon + .list-item-body,
    :deep(.el-avatar + .list-item-body) {
      padding-left: 12px;
    }

    &:hover {
      background-color: hsla(0, 0%, 60%, 0.08);
    }

    & + .list-item {
      border-top: 1px solid hsla(0, 0%, 60%, 0.2);
    }
  }

  /* 操作按钮 */
  .bottom-tools {
    display: flex;
    align-items: center;
    border-top: 1px solid hsla(0, 0%, 60%, 0.2);

    .bottom-tool {
      flex: 1;
      line-height: 46px;
      text-align: center;
      text-decoration: none;
      transition: background-color 0.3s;
      cursor: pointer;
      color: inherit;

      &:hover {
        background: hsla(0, 0%, 60%, 0.08);
      }
    }
  }

  /* 修改标签页样式 */
  .notice-tabs :deep(.el-tabs__header) {
    --ele-tab-height: 44px;
    --ele-tab-padding: 22px;
  }
</style>
