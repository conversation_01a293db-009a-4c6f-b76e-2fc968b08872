<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form @keyup.enter="reload" label-width="70px">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="用户名称" prop="username">
              <el-input
                v-model="queryParams.username"
                placeholder="请输入用户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="登录地址" prop="userIp">
              <el-input
                v-model="queryParams.userIp"
                placeholder="请输入登录地址"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="登录日期" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #logType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_LOGIN_TYPE"
            :model-value="row.logType"
          />
        </template>
        <template #result="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_LOGIN_RESULT"
            :model-value="row.result"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="openDetail(row)">
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单弹窗：详情 -->
    <LoginLogDetail ref="detailRef" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { Search, Refresh } from '@element-plus/icons-vue';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as LoginLogApi from '@/api/system/loginLog';
  import LoginLogDetail from './LoginLogDetail.vue';

  defineOptions({ name: 'SystemLoginLog' });
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    username: undefined,
    userIp: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'logType',
      label: '操作类型',
      align: 'center',
      minWidth: 110,
      slot: 'logType'
    },
    {
      prop: 'username',
      label: '用户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'userIp',
      label: '登录地址',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'userAgent',
      label: '浏览器',
      align: 'center',
      minWidth: 200
    },
    {
      prop: 'result',
      label: '登陆结果',
      align: 'center',
      minWidth: 140,
      slot: 'result'
    },
    {
      prop: 'createTime',
      label: '登录日期',
      align: 'center',
      minWidth: 150,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return LoginLogApi.getLoginLogPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 详情操作 */
  const detailRef = ref();
  const openDetail = (data: LoginLogApi.LoginLogVO) => {
    detailRef.value.open(data);
  };
</script>
