<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form @keyup.enter="reload" label-width="90px">
        <el-row :gutter="8">
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="操作人" prop="userId">
              <el-select
                v-model="queryParams.userId"
                clearable
                filterable
                placeholder="请输入操作人员"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="操作模块" prop="type">
              <el-input
                v-model="queryParams.type"
                placeholder="请输入操作模块"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="操作名称" prop="subType">
              <el-input
                v-model="queryParams.subType"
                placeholder="请输入操作名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="操作内容" prop="action">
              <el-input
                v-model="queryParams.action"
                placeholder="请输入操作内容"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label="操作时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="7" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_WX_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="openDetail(row)">
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单弹窗：详情 -->
    <OperateLogDetail ref="detailRef" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { DICT_TYPE } from '@/utils/dict';
  import { Search, Refresh } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import * as OperateLogApi from '@/api/system/operatelog';
  import OperateLogDetail from './OperateLogDetail.vue';
  import * as UserApi from '@/api/system/user';
  import { useFormData } from '@/utils/use-form-data';

  const userList = ref<UserApi.UserVO[]>([]); // 用户列表

  defineOptions({ name: 'SystemOperateLog' });
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    userId: undefined,
    type: undefined,
    subType: undefined,
    action: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'userName',
      label: '操作人',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'type',
      label: '操作模块',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'subType',
      label: '操作名',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'action',
      label: '操作内容',
      align: 'center',
      minWidth: 200
    },
    {
      prop: 'createTime',
      label: '操作时间',
      align: 'center',
      minWidth: 150,
      formatter: dateFormatter
    },
    {
      prop: 'userIp',
      label: '操作IP',
      align: 'center',
      minWidth: 140
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return OperateLogApi.getOperateLogPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  }; /** 详情操作 */
  const detailRef = ref();
  const openDetail = (data: OperateLogApi.OperateLogVO) => {
    detailRef.value.open(data);
  };

  /** 初始化 **/
  onMounted(async () => {
    userList.value = await UserApi.getSimpleUserList();
  });
</script>
