<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="渠道名称" prop="accountName">
              <el-input
                v-model.trim="queryParams.accountName"
                placeholder="请输入渠道名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模板编码" prop="templateCode">
              <el-input
                v-model.trim="queryParams.templateCode"
                placeholder="请输入模板编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模版名称" prop="templateName">
              <el-input
                v-model.trim="queryParams.templateName"
                placeholder="请输入模版名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模版类型" prop="templateType">
              <el-select
                v-model="queryParams.templateType"
                clearable
                placeholder="请选择模版类型"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.WX_MSG_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发送状态" prop="sendStatus">
              <el-select
                v-model="queryParams.sendStatus"
                placeholder="请选择发送状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_WX_SEND_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发送时间" prop="sendTime">
              <el-date-picker
                v-model="queryParams.sendTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['system:wx-msg-log:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['system:wx-msg-log:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #templateType="{ row }">
          <dict-data
            :code="DICT_TYPE.WX_MSG_TYPE"
            type="tag"
            :model-value="row.templateType"
          />
        </template>
        <template #sendStatus="{ row }">
          <dict-data
            :code="DICT_TYPE.SYSTEM_WX_SEND_STATUS"
            type="tag"
            :model-value="row.sendStatus"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="openEdit(row)">
            详细
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="removeBatch(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <WxMsgLogForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as WxMsgLogApi from '@/api/system/wechat/msglog';
  import WxMsgLogForm from './WxMsgLogForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';
  /** 微信消息记录 列表 */
  defineOptions({ name: 'WxMsgLogIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    accountName: undefined,
    toUser: undefined,
    templateId: undefined,
    templateCode: undefined,
    templateName: undefined,
    templateType: undefined,
    content: undefined,
    params: undefined,
    sendStatus: undefined,
    sendTime: [],
    sendMessageId: undefined,
    sendException: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'accountCode',
      label: '渠道编码',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'accountName',
      label: '渠道名称',
      align: 'center',
      minWidth: 120
    },
    {
      prop: 'toUser',
      label: '接收人',
      align: 'center',
      minWidth: 160
    },
    {
      prop: 'templateCode',
      label: '模板编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateName',
      label: '模版名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateType',
      label: '模版类型',
      align: 'center',
      minWidth: 110,
      slot: 'templateType'
    },
    {
      prop: 'sendStatus',
      label: '发送状态',
      align: 'center',
      minWidth: 110,
      slot: 'sendStatus'
    },
    {
      prop: 'sendTime',
      label: '发送时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'sendException',
      label: '发送异常',
      align: 'center',
      minWidth: 210
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return WxMsgLogApi.getWxMsgLogPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await WxMsgLogApi.deleteWxMsgLog(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await WxMsgLogApi.exportWxMsgLog(queryParams);
      download.excel(data, '微信消息记录.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
