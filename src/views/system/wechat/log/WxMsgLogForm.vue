<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="微信渠道id" prop="accountId">
        <el-input v-model="form.accountId" placeholder="请输入微信渠道id" />
      </el-form-item>
      <el-form-item label="接收人" prop="toUser">
        <el-input v-model="form.toUser" placeholder="请输入接收人" />
      </el-form-item>
      <el-form-item label="模板id" prop="templateId">
        <el-input v-model="form.templateId" placeholder="请输入模板id" />
      </el-form-item>
      <el-form-item label="模板编码" prop="templateCode">
        <el-input v-model="form.templateCode" placeholder="请输入模板编码" />
      </el-form-item>
      <el-form-item label="模版名称" prop="templateName">
        <el-input v-model="form.templateName" placeholder="请输入模版名称" />
      </el-form-item>
      <el-form-item label="模版类型" prop="templateType">
        <el-select v-model="form.templateType" placeholder="请选择模版类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="发送内容" prop="content">
        <tinymce-editor v-model="form.content" ref="editorRef" :init="config" />
      </el-form-item>
      <el-form-item label="发送参数" prop="params">
        <el-input v-model="form.params" placeholder="请输入发送参数" />
      </el-form-item>
      <el-form-item label="发送状态" prop="sendStatus">
        <el-radio-group v-model="form.sendStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="发送时间" prop="sendTime">
        <el-date-picker
          v-model="form.sendTime"
          type="date"
          value-format="x"
          placeholder="选择发送时间"
        />
      </el-form-item>
      <el-form-item label="发送返回的消息 ID" prop="sendMessageId">
        <el-input
          v-model="form.sendMessageId"
          placeholder="请输入发送返回的消息 ID"
        />
      </el-form-item>
      <el-form-item label="发送异常" prop="sendException">
        <el-input v-model="form.sendException" placeholder="请输入发送异常" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as WxMsgLogApi from '@/api/system/wechat/msglog';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 微信消息记录 表单 */
  defineOptions({ name: 'WxMsgLogForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    accountId: undefined,
    toUser: undefined,
    templateId: undefined,
    templateCode: undefined,
    templateName: undefined,
    templateType: undefined,
    content: undefined,
    params: undefined,
    sendStatus: undefined,
    sendTime: undefined,
    sendMessageId: undefined,
    sendException: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    accountId: [
      { required: true, message: '微信渠道id不能为空', trigger: 'blur' }
    ],
    toUser: [{ required: true, message: '接收人不能为空', trigger: 'blur' }],
    templateId: [
      { required: true, message: '模板id不能为空', trigger: 'blur' }
    ],
    templateCode: [
      { required: true, message: '模板编码不能为空', trigger: 'blur' }
    ],
    templateType: [
      { required: true, message: '模版类型不能为空', trigger: 'change' }
    ],
    content: [{ required: true, message: '发送内容不能为空', trigger: 'blur' }],
    params: [{ required: true, message: '发送参数不能为空', trigger: 'blur' }],
    sendStatus: [
      { required: true, message: '发送状态不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await WxMsgLogApi.createWxMsgLog(form);
        message.success(t('common.createSuccess'));
      } else {
        await WxMsgLogApi.updateWxMsgLog(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await WxMsgLogApi.getWxMsgLog(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
