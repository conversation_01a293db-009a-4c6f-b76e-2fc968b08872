<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模版类型" prop="templateType">
              <el-select
                v-model="queryParams.templateType"
                clearable
                placeholder="请选择模版类型"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.WX_MSG_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                clearable
                placeholder="请选择状态"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['system:wx-msg-template:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #templateType="{ row }">
          <dict-data
            :code="DICT_TYPE.WX_MSG_TYPE"
            type="tag"
            :model-value="row.templateType"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="warning" @click="openSendForm(row)">
            发送测试
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['system:wx-msg-template:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:wx-msg-template:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['system:wx-msg-template:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <WxMsgTemplateForm v-model="showEdit" :data="current" @done="reload" />
    <WxMsgTemplateSendForm v-model="showSendForm" :data="current" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as WxMsgTemplateApi from '@/api/system/wechat/template';
  import WxMsgTemplateForm from './WxMsgTemplateForm.vue';
  import WxMsgTemplateSendForm from './WxMsgTemplateSendForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';

  /** 微信消息模版管理 列表 */
  defineOptions({ name: 'WxMsgTemplateIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    templateType: undefined,
    status: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'templateCode',
      label: '模版编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateName',
      label: '模版名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateType',
      label: '模版类型',
      align: 'center',
      minWidth: 110,
      slot: 'templateType'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 80,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 220,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  const showSendForm = ref(false);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return WxMsgTemplateApi.getWxMsgTemplatePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  /** 打开编辑弹窗 */
  const openSendForm = (row) => {
    current.value = row ?? null;
    showSendForm.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await WxMsgTemplateApi.deleteWxMsgTemplate(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
