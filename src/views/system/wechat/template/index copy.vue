<template>
  <div class="message-preview-container">
    <!-- 左侧输入区域 -->
    <div class="input-area">
      <div class="input-group">
        <el-input
          v-model="title"
          placeholder="请输入图文消息标题"
          clearable
          maxlength="50"
        />
      </div>
      <div class="input-group">
        <el-input
          v-model="description"
          type="textarea"
          placeholder="请输入图文消息描述"
          clearable
          rows="4"
          maxlength="200"
        />
      </div>
      <div class="upload-group">
        <el-upload
          class="upload-image"
          action="#"
          list-type="picture-card"
          :show-file-list="false"
          :on-change="handleImageChange"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </div>
      <!-- 新增封面图描述输入框 -->
      <div class="input-group">
        <el-input
          v-model="coverImageDescription"
          placeholder="请输入封面图描述"
          clearable
          maxlength="100"
        />
      </div>
    </div>

    <!-- 右侧预览区域 -->
    <div class="preview-area">
      <div class="preview-card">
        <div class="image-container">
          <img v-if="image" :src="image" alt="封面图" class="image-preview" />
        </div>
        <div class="content">
          <h3 class="preview-title">{{ title || '图文消息标题' }}</h3>
          <p class="preview-description">{{
            description || '图文消息描述...'
          }}</p>
          <!-- 显示封面图描述 -->
          <p v-if="coverImageDescription" class="cover-description">
            {{ coverImageDescription }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';

  // 定义状态变量
  const title = ref('');
  const description = ref('');
  const image = ref(null);
  const coverImageDescription = ref(''); // 新增封面图描述

  // 图片上传处理
  const handleImageChange = (file) => {
    const reader = new FileReader();
    reader.onload = () => {
      image.value = reader.result;
    };
    reader.readAsDataURL(file.raw);
  };
</script>

<style scoped>
  .message-preview-container {
    display: flex;
    gap: 20px; /* 间距 */
    padding: 20px;
  }

  .input-area {
    flex: 1;
    max-width: 450px; /* 限制左侧区域的宽度 */
  }

  .input-group {
    margin-bottom: 20px;
  }

  .upload-group {
    margin-top: 20px;
  }

  .preview-area {
    flex: 1;
    max-width: 500px; /* 限制右侧区域的宽度 */
  }

  .preview-card {
    display: flex;
    flex-direction: column; /* 使内容垂直排列 */
    border: 1px solid #e4e7ed;
    border-radius: 10px; /* 更圆滑的边角 */
    background-color: #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    overflow: hidden;
    width: 100%;
  }

  .image-container {
    width: 100%;
    height: 200px; /* 增加图片显示区域的高度 */
    overflow: hidden;
  }

  .image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .content {
    padding: 15px;
  }

  .preview-title {
    font-size: 18px; /* 标题字体稍微增大 */
    font-weight: bold;
    color: #333; /* 统一字体颜色 */
    margin-top: 10px;
  }

  .preview-description {
    font-size: 14px;
    color: #666; /* 深色描述文字 */
    margin-top: 8px;
  }

  .cover-description {
    font-size: 12px;
    color: #999; /* 显示封面图描述 */
    margin-top: 10px;
  }
</style>
