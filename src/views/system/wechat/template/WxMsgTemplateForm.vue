<template>
  <ele-drawer
    destroy-on-close
    :title="title"
    :size="'50%'"
    v-model="visible"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="110px"
      v-loading="loading"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="模版编码" prop="templateCode">
            <el-input
              v-model="form.templateCode"
              placeholder="请输入模版编码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模版名称" prop="templateName">
            <el-input
              v-model="form.templateName"
              placeholder="请输入模版名称"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="微信渠道" prop="accountId">
            <el-select v-model="form.accountId" placeholder="请选择微信渠道">
              <el-option
                v-for="item in accountList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模版类型" prop="templateType">
            <el-select v-model="form.templateType" placeholder="请选择模版类型">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.WX_MSG_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="模版内容" prop="content">
        <el-input
          v-if="form.templateType === 'text'"
          v-model="form.content"
          type="textarea"
          :maxlength="2048"
          :rows="6"
        />
        <tinymce-editor
          v-else
          v-model="form.content"
          ref="editorRef"
          :init="config"
        />
      </el-form-item>
      <el-form-item>
        <!-- 模版内容支持的语法说明 -->
        <el-alert
          title="消息文本 模版内容支持的语法说明"
          type="warning"
          :closable="false"
          description="
          模版内容字段支持换行（<br/>）及链接标签（<a>)"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-drawer>
</template>
<script setup lang="ts">
  import * as WxMsgTemplateApi from '@/api/system/wechat/template';
  import { getWxAccountSimpleList } from '@/api/system/wechat/index';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';

  /** 微信消息模版管理 表单 */
  defineOptions({ name: 'WxMsgTemplateForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const accountList = ref([]);
  const config = ref({
    height: 380
  });
  const editorRef = ref(null);

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    accountId: undefined,
    templateCode: undefined,
    templateName: undefined,
    templateType: undefined,
    status: 0,
    content: undefined,
    params: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    accountId: [
      { required: true, message: '微信渠道不能为空', trigger: 'blur' }
    ],
    templateCode: [
      { required: true, message: '模版编码不能为空', trigger: 'change' }
    ],
    templateName: [
      { required: true, message: '模版名称不能为空', trigger: 'change' }
    ],
    templateType: [
      { required: true, message: '模版类型不能为空', trigger: 'change' }
    ],
    status: [
      {
        required: true,
        message: '状态不能为空',
        trigger: 'blur'
      }
    ],
    content: [{ required: true, message: '模版内容不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await WxMsgTemplateApi.createWxMsgTemplate(form);
        message.success(t('common.createSuccess'));
      } else {
        await WxMsgTemplateApi.updateWxMsgTemplate(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await WxMsgTemplateApi.getWxMsgTemplate(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
  onMounted(async () => {
    accountList.value = await getWxAccountSimpleList({});
  });
</script>
