<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="渠道编码" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入渠道编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="渠道名称" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入渠道名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['system:wx-account:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_WX_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['system:wx-account:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:wx-account:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['system:wx-account:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <WxAccountForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { DICT_TYPE } from '@/utils/dict';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as WxAccountApi from '@/api/system/wechat/index';
  import WxAccountForm from './WxAccountForm.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 微信账户管理 列表 */
  defineOptions({ name: 'WxAccountIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    code: undefined,
    name: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '渠道编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'name',
      label: '渠道名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'type',
      label: '类型',
      align: 'center',
      minWidth: 140,
      slot: 'type'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 80,
      slot: 'status'
    },
    {
      prop: 'appId',
      label: '应用ID',
      align: 'center',
      minWidth: 190
    },
    {
      prop: 'agentid',
      label: '企微应用ID',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 150,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return WxAccountApi.getWxAccountPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await WxAccountApi.deleteWxAccount(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
