<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="渠道编码" prop="code">
        <el-input v-model="form.code" placeholder="请输入微信渠道编码" />
      </el-form-item>
      <el-form-item label="渠道名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入微信渠道名称" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_WX_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="应用ID" prop="appId">
        <el-input v-model="form.appId" placeholder="请输入应用ID" />
      </el-form-item>
      <el-form-item label="应用secret" prop="appSecret">
        <el-input
          type="password"
          show-password
          v-model="form.appSecret"
          placeholder="请输入应用secret"
        />
      </el-form-item>
      <el-form-item v-if="form.type == 2" label="企微应用ID" prop="agentid">
        <el-input v-model="form.agentid" placeholder="请输入企微应用ID" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  import * as WxAccountApi from '@/api/system/wechat/index';
  import { useFormData } from '@/utils/use-form-data';

  /** 微信账户管理 表单 */
  defineOptions({ name: 'WxAccountForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    code: undefined,
    name: undefined,
    type: undefined,
    status: 0,
    appId: undefined,
    appSecret: undefined,
    agentid: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    code: [{ required: true, message: '渠道编码不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '渠道名称不能为空', trigger: 'blur' }],
    type: [
      {
        required: true,
        message: '类型不能为空',
        trigger: 'change'
      }
    ],
    status: [
      {
        required: true,
        message: '状态不能为空',
        trigger: 'blur'
      }
    ],
    appId: [{ required: true, message: '应用ID不能为空', trigger: 'blur' }],
    appSecret: [
      { required: true, message: '应用secret不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await WxAccountApi.createWxAccount(form);
        message.success(t('common.createSuccess'));
      } else {
        await WxAccountApi.updateWxAccount(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await WxAccountApi.getWxAccount(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
