<!-- 搜索表单 -->
<template>
  <el-form label-width="72px" @keyup.enter="search" @submit.prevent="">
    <el-row :gutter="8">
      <el-col :lg="7" :md="12" :sm="12" :xs="24">
        <el-form-item label="用户名称">
          <el-input
            clearable
            v-model.trim="form.username"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="7" :md="12" :sm="12" :xs="24">
        <el-form-item label="用户昵称">
          <el-input
            clearable
            v-model.trim="form.nickname"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="7" :md="12" :sm="12" :xs="24">
        <el-form-item label="手机号码">
          <el-input clearable v-model.trim="form.mobile" placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :lg="7" :md="12" :sm="12" :xs="24">
        <el-form-item label="用户状态">
          <el-select
            v-model="form.status"
            placeholder="用户状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :lg="7" :md="12" :sm="12" :xs="24">
        <el-form-item label="创建时间">
          <el-date-picker
            unlink-panels
            type="daterange"
            v-model="form.createTime"
            range-separator="-"
            value-format="YYYY-MM-DD HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="ele-fluid"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label-width="16px">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetForm] = useFormData({
    username: undefined,
    mobile: undefined,
    status: undefined,
    deptId: undefined,
    createTime: []
  });

  /** 重置表单数据 */
  const resetFields = () => {
    resetForm();
  };

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
