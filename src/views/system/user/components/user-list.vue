<template>
  <user-search ref="searchRef" @search="reload" />
  <!-- 表格 -->
  <ele-pro-table
    ref="tableRef"
    row-key="id"
    :columns="columns"
    :datasource="datasource"
    :show-overflow-tooltip="true"
    :footer-style="{ paddingBottom: '16px' }"
    cache-key="systemUserTable"
  >
    <template #toolbar>
      <el-button
        type="primary"
        class="ele-btn-icon"
        :icon="PlusOutlined"
        v-permission="'system:user:create'"
        @click="openEdit()"
      >
        新建
      </el-button>
    </template>
    <template #status="{ row }">
      <el-switch
        size="small"
        :model-value="row.status == 0"
        @change="(checked) => editStatus(checked, row)"
      />
    </template>
    <template #action="{ row }">
      <template v-if="row.id !== 1">
        <el-link
          type="primary"
          :underline="false"
          v-permission="'system:user:update'"
          @click="openEdit(row)"
        >
          修改
        </el-link>
        <el-divider v-if="moreItems.length" direction="vertical" />
        <ele-dropdown
          v-if="moreItems.length"
          :items="moreItems"
          style="display: inline"
          @command="(key) => dropClick(key, row)"
        >
          <el-link type="primary" :underline="false">
            <span>更多</span>
            <el-icon :size="12" style="vertical-align: -1px; margin-left: 2px">
              <ArrowDown />
            </el-icon>
          </el-link>
        </ele-dropdown>
      </template>
    </template>
  </ele-pro-table>
  <!-- 编辑弹窗 -->
  <user-edit
    :data="current"
    v-model="showEdit"
    :dept-id="deptId"
    @done="reload"
  />
  <!-- 导入弹窗 -->
  <user-import v-model="showImport" @done="reload" />
  <!-- 分配角色弹窗 -->
  <user-role v-model="showRole" :data="current" />
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    PlusOutlined,
    UploadOutlined,
    DownloadOutlined,
    ArrowDown
  } from '@/components/icons';
  import { usePermission } from '@/utils/use-permission';
  import UserSearch from './user-search.vue';
  import UserEdit from './user-edit.vue';
  import UserImport from './user-import.vue';
  import UserRole from './user-role.vue';
  import { dateFormatter } from '@/utils/formatTime';
  import download from '@/utils/download';
  import {
    getUserPage,
    deleteUser,
    updateUserStatus,
    resetUserPwd,
    exportUser
  } from '@/api/system/user';

  const props = defineProps({
    /** 部门id */
    deptId: Number
  });

  const { hasPermission } = usePermission();

  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'username',
        label: '用户名称',
        align: 'center',
        minWidth: 140
      },
      {
        prop: 'nickname',
        label: '用户昵称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'companyName',
        label: '公司',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'deptName',
        label: '部门',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'mobile',
        label: '手机号码',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'status',
        label: '状态',
        width: 90,
        align: 'center',
        slot: 'status'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 160,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        fixed: 'right'
      }
    ];
  });

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);

  /** 是否显示分配角色弹窗 */
  const showRole = ref(false);

  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    if (hasPermission('system:user:delete')) {
      items.push({ title: '删除用户', command: 'delete' });
    }
    // if (hasPermission('system:user:update-password')) {
    //   items.push({ title: '重置密码', command: 'password' });
    // }
    if (hasPermission('system:permission:assign-user-role')) {
      items.push({ title: '分配角色', command: 'role' });
    }
    return items;
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getUserPage({
      ...where,
      ...filters,
      ...pages,
      deptId: props.deptId
    });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开编辑弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 批量删除 */
  const removeBatch = (row) => {
    ElMessageBox.confirm(`是否确认删除当前用户？`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        deleteUser(row.id)
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 修改用户状态 */
  const editStatus = (checked, row) => {
    const status = checked ? '0' : '1';
    updateUserStatus(row.id, status).then(() => {
      row.status = status;
      EleMessage.success('修改成功');
    });
  };

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'password') {
      ElMessageBox.prompt(`请输入"${row.username}"的新密码:`, '重置密码', {
        inputPlaceholder: '请输入5-18位非空白字符',
        inputPattern: /^[\S]{5,18}$/,
        inputErrorMessage: '密码必须为5-18位非空白字符',
        draggable: true
      }).then(async ({ value }) => {
        await resetUserPwd(row.id, value);
        EleMessage.success('修改成功');
      });
    } else if (key === 'role') {
      current.value = row ?? null;
      showRole.value = true;
    } else if (key === 'delete') {
      removeBatch(row);
    }
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading('请求中..');
    tableRef.value?.fetch?.(({ where, orders, filters }) => {
      exportUser({ ...where, ...orders, ...filters })
        .then(() => {
          loading.close();
          download.excel(data, '用户列表.xls');
        })
        .catch((e) => {
          loading.close();
        });
    });
  };

  // 监听机构 id 变化
  watch(
    () => props.deptId,
    () => {
      searchRef.value?.resetFields?.();
      reload({});
    }
  );
</script>
