<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="640"
    :model-value="modelValue"
    :title="isUpdate ? '修改用户' : '新建用户'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="用户昵称" prop="nickname">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.nickname"
              placeholder="请输入用户名"
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input
              clearable
              :maxlength="11"
              v-model="form.mobile"
              placeholder="请输入手机号码"
            />
          </el-form-item>
          <el-form-item v-if="!isUpdate" label="用户名称" prop="username">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.username"
              placeholder="请输入用户名称"
            />
          </el-form-item>
          <el-form-item label="用户性别" prop="sex">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="归属部门" prop="deptId">
            <el-tree-select
              v-model="form.deptId"
              :data="deptList"
              :props="defaultProps"
              default-expand-all
              check-strictly
              clearable
              node-key="id"
              class="ele-fluid"
              placeholder="请选择归属部门"
            />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input
              clearable
              :maxlength="100"
              v-model="form.email"
              placeholder="请输入邮箱"
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item v-if="!isUpdate" label="用户密码" prop="password">
            <el-input
              show-password
              type="password"
              :maxlength="20"
              v-model="form.password"
              placeholder="请输入用户密码"
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item label="岗位">
            <el-select v-model="form.postIds" multiple placeholder="请选择">
              <el-option
                v-for="item in postList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注">
        <el-input
          type="textarea"
          :rows="3"
          :maxlength="200"
          v-model="form.remark"
          placeholder="请输入内容"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { createUser, updateUser, getUser } from '@/api/system/user';
  import { defaultProps, handleTree } from '@/utils/tree';
  import * as PostApi from '@/api/system/post';
  import * as DeptApi from '@/api/system/dept';
  const emit = defineEmits(['done', 'update:modelValue']);
  const deptList = ref([]); // 树形结构
  const postList = ref([]); // 岗位列表

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    nickname: '',
    deptId: '',
    mobile: '',
    email: '',
    id: undefined,
    username: '',
    password: '',
    sex: undefined,
    postIds: [],
    remark: '',
    status: 0,
    roleIds: []
  });

  /** 表单验证规则 */
  const rules = reactive({
    username: [
      { required: true, message: '用户名称不能为空', trigger: 'blur' }
    ],
    nickname: [
      { required: true, message: '用户昵称不能为空', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '用户密码不能为空', trigger: 'blur' }
    ],
    email: [
      {
        type: 'email',
        message: '请输入正确的邮箱地址',
        trigger: ['blur', 'change']
      }
    ],
    mobile: [
      {
        pattern:
          /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? updateUser : createUser;
      if (isUpdate.value) {
        delete form.password;
      }
      saveOrUpdate(form)
        .then(() => {
          loading.value = false;
          EleMessage.success('保存成功');
          updateModelValue(false);
          emit('done');
        })
        .catch(() => {
          loading.value = false;
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    async (modelValue) => {
      if (modelValue) {
        if (props.data) {
          const resp = await getUser(props.data.id);
          assignFields({
            ...resp
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
        // 加载部门树
        deptList.value = handleTree(await DeptApi.getSimpleDeptList());
        // 加载岗位列表
        postList.value = await PostApi.getSimplePostList();
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
