<!-- 用户编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="640"
    :model-value="modelValue"
    :title="isUpdate ? '修改' : '新建'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="租户名" prop="name">
            <el-input
              clearable
              v-model="form.name"
              placeholder="请输入租户名"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="租户权限" prop="packageId">
            <el-select
              v-model="form.packageId"
              clearable
              placeholder="请选择租户权限"
            >
              <el-option
                v-for="item in packageList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系人" prop="contactName">
            <el-input
              clearable
              v-model="form.contactName"
              placeholder="请输入联系人"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系手机" prop="contactMobile">
            <el-input
              clearable
              v-model="form.contactMobile"
              placeholder="请输入联系人"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item
            v-if="form.id === undefined"
            label="用户名称"
            prop="username"
          >
            <el-input
              clearable
              v-model="form.username"
              placeholder="请输入用户名称"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item
            v-if="form.id === undefined"
            label="用户密码"
            prop="password"
          >
            <el-input
              show-password
              type="password"
              v-model="form.password"
              placeholder="请输入用户密码"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="账号额度" prop="accountCount">
            <el-input-number
              :min="0"
              controls-position="right"
              v-model="form.accountCount"
              placeholder="请输入账号额度"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="过期时间" prop="expireTime">
            <el-date-picker
              v-model="form.expireTime"
              clearable
              placeholder="请选择过期时间"
              type="date"
              value-format="x"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="绑定域名" prop="website">
            <el-input
              clearable
              v-model="form.website"
              placeholder="请输入绑定域名"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="租户状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage, phoneReg } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { getTenantPackageList } from '@/api/system/tenantPackage';
  import { getTenant, updateTenant, createTenant } from '@/api/system/tenant';
  const emit = defineEmits(['done', 'update:modelValue']);
  const packageList = ref([]); // 租户权限

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    name: undefined,
    packageId: undefined,
    contactName: undefined,
    contactMobile: undefined,
    accountCount: undefined,
    expireTime: undefined,
    website: undefined,
    status: 0,
    // 新增专属
    username: undefined,
    password: undefined
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [{ required: true, message: '租户名不能为空', trigger: 'blur' }],
    packageId: [
      { required: true, message: '租户权限不能为空', trigger: 'blur' }
    ],
    contactName: [
      { required: true, message: '联系人不能为空', trigger: 'blur' }
    ],
    status: [{ required: true, message: '租户状态不能为空', trigger: 'blur' }],
    accountCount: [
      { required: true, message: '账号额度不能为空', trigger: 'blur' }
    ],
    expireTime: [
      { required: true, message: '过期时间不能为空', trigger: 'blur' }
    ],
    website: [{ required: true, message: '绑定域名不能为空', trigger: 'blur' }],
    username: [
      { required: true, message: '用户名称不能为空', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '用户密码不能为空', trigger: 'blur' }
    ],
    contactMobile: [
      {
        pattern: phoneReg,
        message: '手机号格式不正确',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? updateTenant : createTenant;
      saveOrUpdate(form)
        .then(() => {
          loading.value = false;
          EleMessage.success('保存成功');
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    async (modelValue) => {
      if (modelValue) {
        if (props.data) {
          const data = await getTenant(props.data.id);
          assignFields({
            ...data
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
        packageList.value = await getTenantPackageList();
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
