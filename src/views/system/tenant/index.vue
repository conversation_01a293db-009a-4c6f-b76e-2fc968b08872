<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <tenant-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="systemRoleTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #packageId="{ row }">
          <el-tag v-if="row.packageId === 0" type="danger">系统租户</el-tag>
          <template v-else v-for="item in packageList">
            <el-tag
              type="success"
              :key="item.id"
              v-if="item.id === row.packageId"
            >
              {{ item.name }}
            </el-tag>
          </template>
        </template>
        <template #accountCount="{ row }">
          <el-tag>{{ row.accountCount }}</el-tag>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            v-permission="'system:tenant:update'"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="'system:tenant:delete'"
            direction="vertical"
          />
          <el-link
            type="danger"
            v-permission="'system:tenant:delete'"
            :underline="false"
            @click="removeBatch(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <tenant-edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import TenantSearch from './components/tenant-search.vue';
  import TenantEdit from './components/tenant-edit.vue';
  import download from '@/utils/download';
  import { dateFormatter2 } from '@/utils/formatTime';
  import {
    getTenantPage,
    deleteTenant,
    exportTenant
  } from '@/api/system/tenant';
  import { getTenantPackageList } from '@/api/system/tenantPackage';

  /** 表格实例 */
  const tableRef = ref(null);
  //租户权限列表
  const packageList = ref([]);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '租户名',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'packageId',
        label: '租户权限',
        align: 'center',
        minWidth: 110,
        slot: 'packageId'
      },
      {
        prop: 'contactName',
        label: '联系人',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'contactMobile',
        label: '联系手机',
        width: 140,
        align: 'center'
      },
      {
        prop: 'accountCount',
        label: '账号额度',
        width: 90,
        align: 'center',
        slot: 'accountCount'
      },
      {
        prop: 'expireTime',
        label: '过期时间',
        align: 'center',
        minWidth: 140,
        formatter: dateFormatter2
      },
      {
        prop: 'website',
        label: '绑定域名',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'status',
        label: '租户状态',
        align: 'center',
        minWidth: 110,
        slot: 'status'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 110,
        formatter: dateFormatter2
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        fixed: 'right'
      }
    ];
  });

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getTenantPage({ ...where, ...filters, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 批量删除 */
  const removeBatch = (row) => {
    ElMessageBox.confirm(`是否确认删除当前的数据?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        deleteTenant(row.id)
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading('请求中..');
    tableRef.value?.fetch?.(({ where }) => {
      exportTenant(where)
        .then((data) => {
          loading.close();
          download.excel(data, '租户列表.xls');
        })
        .catch((e) => {
          loading.close();
        });
    });
  };

  /** 初始化 **/
  onMounted(async () => {
    packageList.value = await getTenantPackageList();
  });
</script>

<script>
  export default {
    name: 'SystemTenant'
  };
</script>
