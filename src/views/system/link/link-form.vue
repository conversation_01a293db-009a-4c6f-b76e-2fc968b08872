<template>
  <ele-modal
    form
    :width="680"
    :close-on-click-modal="false"
    :title="dialogTitle"
    :body-style="{ paddingLeft: '0px' }"
    v-model="dialogVisible"
  >
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="外部系统" prop="name">
        <el-input v-model="formData.name" placeholder="请输入外部系统名称" />
      </el-form-item>
      <el-row :gutter="8">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="图标">
            <icon-select v-model="formData.icon" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="背景色" prop="color">
            <el-color-picker v-model="formData.color" show-alpha />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="URL地址" prop="url">
        <el-input v-model="formData.url" placeholder="请输入外部系统地址" />
      </el-form-item>
      <el-row :gutter="8">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="formData.sort" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </ele-modal>
</template>
<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { CommonStatusEnum } from '@/utils/constants';
  import * as LinkApi from '@/api/system/link';
  import { useMessage } from '@/hooks/web/useMessage';
  import IconSelect from './icon-select.vue';

  defineOptions({ name: 'UserGroupForm' });

  const message = useMessage(); // 消息弹窗

  const dialogVisible = ref(false); // 弹窗的是否展示
  const dialogTitle = ref(''); // 弹窗的标题
  const formLoading = ref(false); // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
  const formType = ref(''); // 表单的类型：create - 新增；update - 修改
  const formData = ref({
    id: undefined,
    name: undefined,
    url: undefined,
    color: undefined,
    sort: 1,
    icon: undefined,
    status: CommonStatusEnum.ENABLE
  });
  const formRules = reactive({
    name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
    url: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
    icon: [{ required: true, message: '图标不能为空', trigger: 'blur' }],
    color: [{ required: true, message: '背景色不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
  });
  const formRef = ref(); // 表单 Ref
  const userList = ref<any[]>([]); // 用户列表

  /** 打开弹窗 */
  const open = async (type: string, id?: number) => {
    dialogVisible.value = true;
    dialogTitle.value = type == 'create' ? '新增' : '更新';
    formType.value = type;
    resetForm();
    // 修改时，设置数据
    if (id) {
      formLoading.value = true;
      try {
        formData.value = await LinkApi.getLink(id);
      } finally {
        formLoading.value = false;
      }
    }
  };
  defineExpose({ open }); // 提供 open 方法，用于打开弹窗

  /** 提交表单 */
  const emit = defineEmits(['success']); // 定义 success 事件，用于操作成功后的回调
  const submitForm = async () => {
    // 校验表单
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    formLoading.value = true;
    try {
      const data = formData.value;
      if (formType.value === 'create') {
        await LinkApi.createLink(data);
        message.success('保存成功');
      } else {
        await LinkApi.updateLink(data);
        message.success('保存成功');
      }
      dialogVisible.value = false;
      // 发送操作成功的事件
      emit('success');
    } finally {
      formLoading.value = false;
    }
  };

  /** 重置表单 */
  const resetForm = () => {
    formData.value = {
      id: undefined,
      name: undefined,
      url: undefined,
      color: undefined,
      icon: undefined,
      sort: 1,
      status: CommonStatusEnum.ENABLE
    };
    formRef.value?.resetFields();
  };
</script>
