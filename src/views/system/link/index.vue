<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="URL地址">
              <el-input
                clearable
                v-model.trim="queryParams.url"
                placeholder="请输入外部系统地址"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="外部系统">
              <el-input
                clearable
                v-model="queryParams.name"
                placeholder="请输入外部系统名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            :icon="Plus"
            v-permission="'bpm:user-group:create'"
            @click="openForm('create')"
          >
            新增外部系统
          </el-button>
        </template>
        <template #icon="{ row }">
          <el-icon :size="18" :style="{ color: row.color }">
            <component :is="row.icon" />
          </el-icon>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm('update', row.id)"
          >
            编辑
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <link-form ref="formRef" @success="reload" />
  </ele-page>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import { Plus, Search, Refresh } from '@element-plus/icons-vue';
  import * as LinkApi from '@/api/system/link';
  import LinkForm from './link-form.vue';
  defineOptions({ name: 'SystemLink' });

  const message = useMessage(); // 消息弹窗

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: '',
    url: ''
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '外部系统名称',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'icon',
        label: '图标',
        align: 'center',
        minWidth: 100,
        slot: 'icon'
      },
      {
        prop: 'url',
        label: '外部系统地址',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'sort',
        label: '排序',
        align: 'center',
        minWidth: 60
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        minWidth: 110,
        slot: 'status'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return LinkApi.getLinkPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type, id) => {
    formRef.value.open(type, id);
  };
  /** 删除按钮操作 */
  const handleDelete = async (id) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await LinkApi.deleteLink(id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };
  /** 详情操作 */
  const detailVisible = ref(false);
  const detailData = ref({
    rule: [],
    option: {}
  });
  const openDetail = async (rowId) => {
    // 设置表单
    const data = await FormApi.getForm(rowId);
    setConfAndFields2(detailData, data.conf, data.fields);
    // 弹窗打开
    detailVisible.value = true;
  };
</script>
<script>
  import * as MenuIcons from '@/layout/menu-icons';
  export default {
    components: MenuIcons
  };
</script>
