<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="编码" prop="code">
        <el-input v-model="form.code" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="外部系统信息" prop="information">
        <tinymce-editor
          v-model="form.information"
          ref="editorRef"
          :init="config"
        />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="状态" prop="isEnable">
            <el-radio-group v-model="form.isEnable">
              <el-radio
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.SYSTEM_ENABLE_STATUS
                )"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as ExternalSystemApi from '@/api/system/external/externalsystem';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 外部系统 表单 */
  defineOptions({ name: 'ExternalSystemForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    code: undefined,
    description: undefined,
    information: undefined,
    isEnable: 1
  });
  /** 表单验证规则 */
  const rules = reactive({
    code: [{ required: true, message: '编码不能为空', trigger: 'blur' }],
    isEnable: [{ required: true, message: '是否启用不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      const data = form;
      if (!isUpdate.value) {
        await ExternalSystemApi.createExternalSystem(data);
        message.success(t('common.createSuccess'));
      } else {
        await ExternalSystemApi.updateExternalSystem(data);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await ExternalSystemApi.getExternalSystem(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
