<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="4" :md="12" :sm="12" :xs="24">
            <el-form-item label="编码" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="4" :md="12" :sm="12" :xs="24">
            <el-form-item label="描述" prop="description">
              <el-input
                v-model.trim="queryParams.description"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="4" :md="12" :sm="12" :xs="24">
            <el-form-item label="是否启用" prop="isEnable">
              <el-select
                v-model="queryParams.isEnable"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_ENABLE_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="4" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['system:external-system:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['system:external-system:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.SYSTEM_ENABLE_STATUS"
            type="tag"
            :model-value="row.isEnable"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['system:external-system:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:external-system:delete']"
          />
          <ele-dropdown
            v-if="moreItems.length"
            :items="moreItems"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
          <!-- <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['system:external-system:delete']"
          >
            删除
          </el-link> -->
        </template>
      </ele-pro-table>
    </ele-card>
    <ExternalSystemForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as ExternalSystemApi from '@/api/system/external/externalsystem';
  import ExternalSystemForm from './ExternalSystemForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 外部系统 列表 */
  defineOptions({ name: 'ExternalSystemIndex' });

  const message = useMessage(); // 消息弹窗
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    code: undefined,
    description: undefined,
    information: undefined,
    isEnable: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'description',
      label: '描述信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'information',
      label: '外部系统信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'isEnable',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ExternalSystemApi.getExternalSystemPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await ExternalSystemApi.deleteExternalSystem(row.id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch (e) {
      message.error(e.message);
    }
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await ExternalSystemApi.exportExternalSystem(queryParams);
      download.excel(data, '外部系统.xls');
    } catch (e) {
      exportLoading.value = false;
      message.error(e.message);
    } finally {
      exportLoading.value = false;
    }
  };
  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    items.push({ title: '短信消息模板', command: 'smstemp' });
    items.push({ title: '删除', command: 'delete' });
    return items;
  });

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'smstemp') {
      //该处后续可以集成显示对应短信模板分配情况
      message.success('待后续实现...');
    } else if (key === 'delete') {
      removeBatch(row);
    }
  };
</script>
