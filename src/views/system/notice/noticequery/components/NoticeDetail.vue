<template>
  <ele-modal
    :width="1000"
    v-model="visible"
    position="center"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ padding: '0 8px 8px 8px' }"
    @open="handleOpen"
    destroy-on-close
    :show-close="false"
    :close-on-click-modal="false"
  >
    <div class="modal-header">
      <h3 class="title">{{ form.title }}</h3>
      <p class="date"
        >创建日期: {{ formatDate(form.createTime, 'YYYY-MM-DD') }}</p
      >
    </div>
    <el-divider></el-divider>
    <el-scrollbar height="420">
      <div v-html="form.content"></div>
    </el-scrollbar>
    <template #footer>
      <el-button @click="handleCancel">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, nextTick } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate } from '@/utils/formatTime';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    createTime: undefined,
    status: '',
    content: ''
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      assignFields(props.data);
    } else {
      resetFields();
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>

<style scoped>
  .modal-header {
    text-align: center; /* 使标题和时间标签居中 */
  }
  .title {
    margin: 0;
    font-size: 24px; /* 增加标题的字体大小 */
  }
  .date {
    margin: 0;
    color: #909399; /* 修改时间标签的字体颜色为更浅的颜色 */
  }
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* 设置标签之间的间距 */
  }
  .tag-item {
    margin-bottom: 4px; /* 可选：设置标签底部间距 */
  }
</style>
