<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <NoticeSearch @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        :highlight-current-row="true"
        :export-config="{ fileName: '通知公告管理' }"
        cache-key="cuxExpertTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="submitBatch()"
          >
            提交
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="withdrawBatch()"
          >
            撤回
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="loseBatch()"
          >
            作废
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #avatar="{ row }">
          <el-image :src="row.avatar" class="h-38px w-38px mr-10px rounded" />
        </template>
        <template #approveMethod="{ row }">
          <dict-data
            :code="DICT_TYPE.APPROVE_METHODS"
            type="tag"
            :model-value="row.approveMethod"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_APPROVE_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            v-if="row.status === 0 || row.status === 4"
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-if="row.status === 0 || row.status === 4"
            direction="vertical"
          />
          <ele-dropdown
            v-if="moreItems.length"
            :items="moreItems"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <NoticeEdit v-model="showEdit" :data="current" @done="reload" />
    <!-- 角色弹窗 -->
    <NoticeAssignRole v-model="showRole" :data="current" @done="reload" />
    <!-- 公司弹窗 -->
    <NoticeAssignCompany v-model="showCompany" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';

  import { EleMessage } from 'ele-admin-plus/es';
  import NoticeSearch from './components/NoticeSearch.vue';
  import NoticeEdit from './components/NoticeEdit.vue';
  import NoticeAssignRole from './components/NoticeAssignRole.vue';
  import NoticeAssignCompany from './components/NoticeAssignCompany.vue';

  import { NoticeApi } from '@/api/system/notice/noticemanager';
  import { dateFormatter, dateFormatter2 } from '@/utils/formatTime';
  import { ElMessageBox } from 'element-plus/es';

  defineOptions({ name: 'CuxExpert' });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'title',
      label: '标题',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'content',
      label: '内容',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'typeDesc',
      label: '类型',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'approveMethod',
      label: '审批方式',
      align: 'center',
      minWidth: 110,
      slot: 'approveMethod'
    },
    {
      prop: 'orderNo',
      label: '排序',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'message',
      label: '消息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'effectiveTimeFrom',
      label: '有效期从',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'effectiveTimeTo',
      label: '有效期至',
      align: 'center',
      minWidth: 100,
      formatter: dateFormatter2
    },
    {
      prop: 'createTime',
      label: '创建日期',
      align: 'center',
      minWidth: 100,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 是否显示角色弹窗 */
  const showRole = ref(false);
  /** 是否显示用户弹窗 */
  const showCompany = ref(false);
  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return NoticeApi.getNoticePageCreate({ ...where, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    tableRef.value?.fetch?.(({ where }) => {
      NoticeApi.exportNoticeCreate(where)
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
  /** 批量提交 */
  const submitBatch = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查公告状态是否为新建或者审批拒绝或者撤回
    const allowedStatuses = [0, 3, 4];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有新建或者审批拒绝或者撤回的数据项才能提交');
      return;
    }
    ElMessageBox.confirm(
      `是否确认提交公告标题为"${rows.map((d) => d.title).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        NoticeApi.submitNotice(rows.map((d) => d.id))
          .then(() => {
            loading.close();
            EleMessage.success('提交成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 批量撤回 */
  const withdrawBatch = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查公告状态是否为已发布
    const allowedStatuses = [2];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有已发布的数据项才能撤回');
      return;
    }
    ElMessageBox.confirm(
      `是否确认撤回公告标题为"${rows.map((d) => d.title).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        NoticeApi.withdrawNotice(rows.map((d) => d.id))
          .then(() => {
            loading.close();
            EleMessage.success('撤回成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 批量失效 */
  const loseBatch = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }

    ElMessageBox.confirm(
      `是否确认作废公告标题为"${rows.map((d) => d.title).join()}"的数据项?作废后，该公告将不能再次修改及发布`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        NoticeApi.loseNotice(rows.map((d) => d.id))
          .then(() => {
            loading.close();
            EleMessage.success('作废成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  /** 批量删除 */
  const removeBatch = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      `是否确认删除公告标题为"${rows.map((d) => d.title).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        NoticeApi.deleteNotice(rows.map((d) => d.id))
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    items.push({ title: '分配角色', command: 'role' });
    items.push({ title: '分配部门', command: 'company' });
    items.push({ title: '删除', command: 'delete' });
    return items;
  });
  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'role') {
      current.value = row ?? null;
      showRole.value = true;
    } else if (key === 'company') {
      current.value = row ?? null;
      showCompany.value = true;
    } else if (key === 'delete') {
      removeBatch(row);
    }
  };
</script>
