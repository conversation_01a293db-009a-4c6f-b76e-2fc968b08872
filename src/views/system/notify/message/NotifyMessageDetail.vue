<template>
  <ele-modal
    v-model="dialogVisible"
    :max-height="500"
    :scroll="true"
    title="详情"
    @open="open"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item label="用户Id">
        {{ detailData.userId }}
      </el-descriptions-item>
      <el-descriptions-item label="模版编号">
        {{ detailData.templateId }}
      </el-descriptions-item>
      <el-descriptions-item label="模板编码">
        {{ detailData.templateCode }}
      </el-descriptions-item>
      <el-descriptions-item label="模版类型">
        <dict-data
          type="tag"
          :code="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE"
          :model-value="detailData.templateType"
        />
      </el-descriptions-item>
      <el-descriptions-item label="模版参数">
        {{ detailData.templateParams }}
      </el-descriptions-item>
      <el-descriptions-item label="消息内容">
        {{ detailData.messageContent }}
      </el-descriptions-item>
      <el-descriptions-item label="是否已读">
        <dict-data
          type="tag"
          :code="DICT_TYPE.INFRA_BOOLEAN_STRING"
          :model-value="detailData.readStatus"
        />
      </el-descriptions-item>
      <el-descriptions-item label="阅读时间">
        {{ formatDate(detailData.readTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
    </el-descriptions>
  </ele-modal>
</template>
<script setup>
  import { DICT_TYPE } from '@/utils/dict';
  import { formatDate } from '@/utils/formatTime';
  defineOptions({ name: 'SystemNotifyMessageDetail' });

  const dialogVisible = defineModel({ type: Boolean }); // 弹窗的是否展示
  const detailLoading = ref(false); // 表单的加载中
  const detailData = ref({}); // 详情数据
  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  /** 打开弹窗 */
  const open = async () => {
    dialogVisible.value = true;
    // 设置数据
    detailLoading.value = true;
    try {
      detailData.value = props.data;
    } finally {
      detailLoading.value = false;
    }
  };
</script>
