<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="消息内容" prop="messageContent">
              <el-input
                v-model.trim="queryParams.templateParams"
                placeholder="请输入消息内容"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="是否已读" prop="readStatus">
              <el-select
                v-model="queryParams.readStatus"
                placeholder="请选择是否已读"
                clearable
              >
                <el-option
                  v-for="dict in getStrDictOptions(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #templateParams="{ row }">
          {{ row.templateParams }}
        </template>
        <template #readStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.INFRA_BOOLEAN_STRING"
            :model-value="row.readStatus"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openDetail(row)"
            v-permission="['system:notify-message:query']"
          >
            详细
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <NotifyMessageDetail
      v-model="showDetail"
      :data="current"
      @success="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { DICT_TYPE, getStrDictOptions } from '@/utils/dict';
  import { Search, Refresh } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as NotifyMessageApi from '@/api/system/notify/message';
  import NotifyMessageDetail from './NotifyMessageDetail.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 站内信消息 列表 */
  defineOptions({ name: 'NotifyMessageIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    messageContent: undefined,
    readStatus: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'userId',
      label: '用户Id',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateCode',
      label: '模板编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'messageContent',
      label: '模版内容',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateParams',
      label: '模版参数',
      align: 'center',
      minWidth: 110,
      slot: 'templateParams'
    },
    {
      prop: 'readStatus',
      label: '是否已读',
      align: 'center',
      minWidth: 110,
      slot: 'readStatus'
    },
    {
      prop: 'readTime',
      label: '阅读时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showDetail = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return NotifyMessageApi.getNotifyMessagePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openDetail = (row) => {
    current.value = row ?? null;
    showDetail.value = true;
  };
</script>
