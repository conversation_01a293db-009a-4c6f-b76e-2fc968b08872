<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模板名称" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入模板名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模版编码" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入模版编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择开启状态"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload">{{
                t('common.query')
              }}</el-button>
              <el-button :icon="Refresh" @click="reset">{{
                t('common.reset')
              }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['system:notify-template:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          ></dict-data>
        </template>
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE"
            :model-value="row.type"
          ></dict-data>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['system:notify-template:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:notify-template:send-notify']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="openSendForm(row)"
            v-permission="['system:notify-template:send-notify']"
          >
            测试
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:notify-template:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['system:notify-template:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <NotifyTemplateForm v-model="showEdit" :data="current" @success="reload" />
    <NotifyTemplateSendForm
      v-model="showSend"
      :data="current"
      @success="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as NotifyTemplateApi from '@/api/system/notify/template';
  import NotifyTemplateForm from './NotifyTemplateForm.vue';
  import NotifyTemplateSendForm from './NotifyTemplateSendForm.vue';

  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  /** 站内信模板 列表 */
  defineOptions({ name: 'NotifyTemplateIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: undefined,
    code: undefined,
    status: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '模板名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'code',
      label: '模版编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'type',
      label: '类型',
      align: 'center',
      minWidth: 110,
      slot: 'type'
    },
    {
      prop: 'content',
      label: '模版内容',
      align: 'center',
      minWidth: 170
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 220,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 是否显示测试发送弹窗 */
  const showSend = ref(false);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return NotifyTemplateApi.getNotifyTemplatePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  /** 发送站内信按钮 */
  const openSendForm = (row) => {
    current.value = row ?? null;
    showSend.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await NotifyTemplateApi.deleteNotifyTemplate(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
