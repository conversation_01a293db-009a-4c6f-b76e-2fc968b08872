<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="是否已读" prop="readStatus">
              <el-select
                v-model="queryParams.readStatus"
                placeholder="请选择状态"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="dict in getBoolDictOptions(
                    DICT_TYPE.INFRA_BOOLEAN_STRING
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发送时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload">{{
                t('common.query')
              }}</el-button>
              <el-button :icon="Refresh" @click="reset">{{
                t('common.reset')
              }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Reading"
            @click="handleUpdateList()"
          >
            标记已读
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Reading"
            @click="handleUpdateAll()"
          >
            全部已读
          </el-button>
        </template>
        <template #templateType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE"
            :model-value="row.templateType"
          ></dict-data>
        </template>
        <template #readStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.INFRA_BOOLEAN_STRING"
            :model-value="row.readStatus"
          ></dict-data>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            :type="row.readStatus ? 'primary' : 'error'"
            @click="openDetail(row)"
            v-permission="['system:notify-template:update']"
          >
            {{ row.readStatus ? '详情' : '标记已读' }}
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <MyNotifyMessageDetail
      v-model="showDetail"
      :data="current"
      @success="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Reading } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as NotifyMessageApi from '@/api/system/notify/message';
  import MyNotifyMessageDetail from './MyNotifyMessageDetail.vue';
  import { useNotifyStore } from '@/store/modules/notify';

  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getBoolDictOptions } from '@/utils/dict';
  /** 站内信模板 列表 */
  defineOptions({ name: 'NotifyTemplateIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  const notifyStore = useNotifyStore();

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    readStatus: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left',
      selectable: (row) => !row.readStatus
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'messageContent',
      label: '消息内容',
      align: 'center',
      minWidth: 310
    },

    {
      prop: 'createTime',
      label: '发送时间',
      align: 'center',
      minWidth: 220,
      formatter: dateFormatter
    },
    {
      prop: 'readStatus',
      label: '是否已读',
      align: 'center',
      minWidth: 110,
      slot: 'readStatus'
    },
    {
      prop: 'readTime',
      label: '阅读时间',
      align: 'center',
      minWidth: 220,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  /** 是否显示详细 */
  const showDetail = ref(false);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return NotifyMessageApi.getMyNotifyMessagePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openDetail = (row) => {
    if (!row.readStatus) {
      handleReadOne(row.id);
    }
    current.value = row ?? null;
    showDetail.value = true;
  };
  /** 标记一条站内信已读 */
  const handleReadOne = async (id) => {
    await NotifyMessageApi.updateNotifyMessageRead(id);
    reload();
    notifyStore.setUnreadCount();
  };

  /** 标记全部站内信已读 **/
  const handleUpdateAll = async () => {
    await NotifyMessageApi.updateAllNotifyMessageRead();
    message.success('全部已读成功！');
    tableRef.value.clearSelection();
    reload();
    notifyStore.setUnreadCount();
  };
  /** 表格选中数据 */
  const selections = ref([]);

  /** 标记一些站内信已读 **/
  const handleUpdateList = async () => {
    if (selections.value.length === 0) {
      message.error('请至少选择一条数据！');
      return;
    }
    //selections转数组
    const ids = selections.value.map((item) => item.id);
    await NotifyMessageApi.updateNotifyMessageRead(ids);
    tableRef.value.clearSelection();
    reload();
    notifyStore.setUnreadCount();
  };
</script>
