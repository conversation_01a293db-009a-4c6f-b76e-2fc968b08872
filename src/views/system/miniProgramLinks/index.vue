<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="120px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="链接标题" prop="title">
              <el-input
                v-model.trim="queryParams.title"
                placeholder="请输入小程序标题"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="类别" prop="title">
              <el-select v-model="queryParams.type" clearable class="!w-240px">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.MINI_LINK_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="状态"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #linkType="{ row }">
          <span v-if="row.linkType == 0">小程序</span>
          <span v-if="row.linkType == 1">H5</span>
        </template>
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.MINI_LINK_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #image="{ row }">
          <el-image :src="row.image" class="h-45px w-45px mr-10px rounded" />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="openEdit(row)">
            编辑
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="danger" @click="removeBatch(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <MiniProgramLinksForm
      v-model="showEdit"
      :data="current"
      @success="reload"
    />
  </ele-page>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as MiniProgramLinksApi from '@/api/system/miniProgramLinks';
  import MiniProgramLinksForm from './MiniProgramLinksForm.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 外部小程序链接 列表 */
  defineOptions({ name: 'MiniProgramLinksIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    title: undefined,
    status: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'linkType',
      label: '链接类别',
      minWidth: 100,
      align: 'center',
      slot: 'linkType'
    },
    {
      prop: 'title',
      label: '链接标题',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'appId',
      label: '小程序APPID',
      align: 'center',
      minWidth: 170
    },
    {
      prop: 'page',
      label: '链接页面',
      align: 'center',
      minWidth: 140
    },
    {
      prop: 'image',
      label: '链接图标',
      align: 'center',
      minWidth: 110,
      slot: 'image'
    },
    {
      prop: 'type',
      label: '类别',
      align: 'center',
      minWidth: 110,
      slot: 'type'
    },
    {
      prop: 'sort',
      label: '显示顺序',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return MiniProgramLinksApi.getMiniProgramLinksPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await MiniProgramLinksApi.deleteMiniProgramLinks(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
