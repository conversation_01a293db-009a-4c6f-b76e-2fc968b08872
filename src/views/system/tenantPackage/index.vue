<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <tenant-package-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="systemRoleTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
        </template>
        <template #accountCount="{ row }">
          <el-tag>{{ row.accountCount }}</el-tag>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            v-permission="'system:tenant-package:update'"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="'system:tenant-package:delete'"
            direction="vertical"
          />
          <el-link
            type="danger"
            v-permission="'system:tenant-package:delete'"
            :underline="false"
            @click="removeBatch(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <tenant-package-edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import TenantPackageSearch from './components/tenant-package-search.vue';
  import TenantPackageEdit from './components/tenant-package-edit.vue';
  import download from '@/utils/download';
  import { dateFormatter2 } from '@/utils/formatTime';
  import {
    getTenantPackagePage,
    deleteTenantPackage
  } from '@/api/system/tenantPackage';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '权限名',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        minWidth: 70,
        slot: 'status'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 160,
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 110,
        formatter: dateFormatter2
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        fixed: 'right'
      }
    ];
  });

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getTenantPackagePage({ ...where, ...filters, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 批量删除 */
  const removeBatch = (row) => {
    ElMessageBox.confirm(`是否确认删除当前的数据?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        deleteTenantPackage(row.id)
          .then(() => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading('请求中..');
    tableRef.value?.fetch?.(({ where }) => {
      exportTenant(where)
        .then((data) => {
          loading.close();
          download.excel(data, '租户列表.xls');
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

</script>

<script>
  export default {
    name: 'SystemTenantPackage'
  };
</script>
