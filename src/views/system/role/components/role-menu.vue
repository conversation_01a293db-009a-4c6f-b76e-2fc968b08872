<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="680"
    position="center"
    :model-value="modelValue"
    :body-style="{ paddingLeft: '0px' }"
    title="分配菜单"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="108px"
      @submit.prevent=""
    >
      <el-form-item label="角色名称" prop="name">
        <el-tag>{{ form.name }}</el-tag>
      </el-form-item>
      <el-form-item label="角色标识" prop="code">
        <el-tag>{{ form.code }}</el-tag>
      </el-form-item>
      <el-form-item label="菜单权限">
        <ele-loading class="role-menu-tree">
          <div style="line-height: 1; padding: 0 6px 0 6px">
            全选/全不选:
            <el-switch
              v-model="treeNodeAll"
              active-text="是"
              inactive-text="否"
              inline-prompt
              @change="handleCheckedTreeNodeAll"
            />
            全部展开/折叠:
            <el-switch
              v-model="menuExpand"
              active-text="展开"
              inactive-text="折叠"
              inline-prompt
              @change="handleCheckedTreeExpand"
            />
          </div>
          <div style="height: 260px; overflow: auto; padding: 0 6px">
            <el-tree
              ref="treeRef"
              :data="menuOptions"
              :props="defaultProps"
              empty-text="加载中，请稍候"
              node-key="id"
              show-checkbox
            />
          </div>
        </ele-loading>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as MenuApi from '@/api/system/menu';
  import * as PermissionApi from '@/api/system/permission';
  import { defaultProps, handleTree } from '@/utils/tree';
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  const menuOptions = ref([]); // 菜单树形结构
  const menuExpand = ref(false); // 展开/折叠
  const treeRef = ref(); // 菜单树组件 Ref
  const treeNodeAll = ref(false); // 全选/全不选
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    name: '',
    code: '',
    menuIds: []
  });

  /** 表单验证规则 */
  const rules = reactive({});

  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    try {
      const data = {
        roleId: form.id,
        menuIds: [
          ...treeRef.value.getCheckedKeys(false), // 获得当前选中节点
          ...treeRef.value.getHalfCheckedKeys() // 获得半选中的父节点
        ]
      };
      await PermissionApi.assignRoleMenu(data);
      loading.value = false;
      EleMessage.success('保存成功');
      updateModelValue(false);
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    async (modelValue) => {
      resetFields();
      menuExpand.value = false; // 展开/折叠
      treeNodeAll.value = false; // 展开/折叠
      if (modelValue) {
        menuOptions.value = handleTree(await MenuApi.getSimpleMenusList());
        assignFields(props.data);
        loading.value = true;
        try {
          form.menuIds = await PermissionApi.getRoleMenuList(
            props.data.id
          );
          // 设置选中
          form.menuIds.forEach((menuId) => {
            treeRef.value.setChecked(menuId, true, false);
          });
        } finally {
          loading.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
  /** 全选/全不选 */
  const handleCheckedTreeNodeAll = () => {
    treeRef.value.setCheckedNodes(treeNodeAll.value ? menuOptions.value : []);
  };

  /** 展开/折叠全部 */
  const handleCheckedTreeExpand = () => {
    const nodes = treeRef.value?.store.nodesMap;
    for (let node in nodes) {
      if (nodes[node].expanded === menuExpand.value) {
        continue;
      }
      nodes[node].expanded = menuExpand.value;
    }
  };
</script>

<style lang="scss" scoped>
  .role-menu-tree {
    width: 100%;
    padding: 6px 0;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    box-sizing: border-box;
    overflow: hidden;
  }
</style>
