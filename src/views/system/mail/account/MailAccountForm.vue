<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="160px"
      v-loading="loading"
    >
      <el-form-item label="邮箱" prop="mail">
        <el-input v-model="form.mail" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="form.password" placeholder="请输入密码" />
      </el-form-item>
      <el-form-item label="SMTP 服务器域名" prop="host">
        <el-input v-model="form.host" placeholder="请输入SMTP 服务器域名" />
      </el-form-item>
      <el-form-item label="SMTP 服务器端口" prop="port">
        <el-input-number
          v-model="form.port"
          :min="0"
          placeholder="请输入端口"
        />
      </el-form-item>
      <el-form-item label="是否开启 SSL" prop="sslEnable">
        <el-radio-group v-model="form.sslEnable">
          <el-radio
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否开启 STARTTLS" prop="starttlsEnable">
        <el-radio-group v-model="form.starttlsEnable">
          <el-radio
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as MailAccountApi from '@/api/system/mail/account';
  import { useFormData } from '@/utils/use-form-data';
  import { getBoolDictOptions, DICT_TYPE } from '@/utils/dict';

  /** 邮箱账号 表单 */
  defineOptions({ name: 'MailAccountForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    mail: undefined,
    username: undefined,
    password: undefined,
    host: undefined,
    port: undefined,
    sslEnable: undefined,
    starttlsEnable: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    mail: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }],
    username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
    password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
    host: [
      { required: true, message: 'SMTP 服务器域名不能为空', trigger: 'blur' }
    ],
    port: [
      { required: true, message: 'SMTP 服务器端口不能为空', trigger: 'blur' }
    ],
    sslEnable: [
      { required: true, message: '是否开启 SSL不能为空', trigger: 'blur' }
    ],
    starttlsEnable: [
      { required: true, message: '是否开启 STARTTLS不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      const data = form;
      if (!isUpdate.value) {
        await MailAccountApi.createMailAccount(data);
        message.success(t('common.createSuccess'));
      } else {
        await MailAccountApi.updateMailAccount(data);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await MailAccountApi.getMailAccount(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
