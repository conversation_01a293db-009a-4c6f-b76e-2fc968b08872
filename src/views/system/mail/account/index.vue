<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="邮箱" prop="mail">
              <el-input
                v-model.trim="queryParams.mail"
                placeholder="请输入邮箱"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model.trim="queryParams.username"
                placeholder="请输入用户名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['system:mail-account:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #sslEnable="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.INFRA_BOOLEAN_STRING"
            :model-value="row.sslEnable"
          />
        </template>
        <template #starttlsEnable="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.INFRA_BOOLEAN_STRING"
            :model-value="row.starttlsEnable"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['system:mail-account:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:mail-account:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['system:mail-account:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <MailAccountForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as MailAccountApi from '@/api/system/mail/account';
  import MailAccountForm from './MailAccountForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  /** 邮箱账号 列表 */
  defineOptions({ name: 'MailAccountIndex' });
  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    mail: undefined,
    username: undefined,
    password: undefined,
    host: undefined,
    port: undefined,
    sslEnable: undefined,
    starttlsEnable: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'mail',
      label: '邮箱',
      align: 'center',
      minWidth: 200
    },
    {
      prop: 'username',
      label: '用户名',
      align: 'center',
      minWidth: 200
    },
    {
      prop: 'host',
      label: 'SMTP 服务器域名',
      align: 'center',
      minWidth: 180
    },
    {
      prop: 'port',
      label: 'SMTP 服务器端口',
      align: 'center',
      minWidth: 180
    },
    {
      prop: 'sslEnable',
      label: '是否开启 SSL',
      align: 'center',
      minWidth: 180,
      slot: 'sslEnable'
    },
    {
      prop: 'starttlsEnable',
      label: '是否开启 STARTTLS',
      align: 'center',
      minWidth: 180,
      slot: 'starttlsEnable'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 170,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return MailAccountApi.getMailAccountPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await MailAccountApi.deleteMailAccount(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
