<template>
  <ele-modal 
  :width="880"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    destroy-on-close
    title="详细日志"
    @open="open">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="用户类型" >
        <dict-data type="tag" :code="DICT_TYPE.USER_TYPE" :model-value="detailData.userType" />
      </el-descriptions-item>
      <el-descriptions-item label="发送邮箱" >
        {{ detailData.fromMail }}
      </el-descriptions-item>
      <el-descriptions-item label="接收邮箱" >
        {{ detailData.toMail }}
      </el-descriptions-item>
      <el-descriptions-item label="邮件标题" >
        {{ detailData.templateTitle }}
      </el-descriptions-item>
      <el-descriptions-item label="发送内容">
        <div v-dompurify-html="detailData.templateContent"></div>
      </el-descriptions-item>
      <el-descriptions-item label="发送状态">
        <dict-data type="tag" :code="DICT_TYPE.SYSTEM_SMS_SEND_STATUS" :model-value="detailData.sendStatus" />
      </el-descriptions-item>
      <el-descriptions-item label="失败原因">
        {{ detailData.sendException }}
      </el-descriptions-item>
      <el-descriptions-item label="发送时间">
        {{ formatDate(detailData.sendTime) }}
      </el-descriptions-item>
    </el-descriptions>
  </ele-modal>
</template>
<script setup>
import * as MailLogApi from '@/api/system/mail/log'
import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'SystemMailLogDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
const detailData = ref({}) // 详情数据
const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = await MailLogApi.getMailLog(props.data.id)
  } finally {
    detailLoading.value = false
  }
}
</script>
