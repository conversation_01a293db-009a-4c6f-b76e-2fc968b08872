<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="接收邮箱" prop="toMail">
              <el-input
                v-model.trim="queryParams.toMail"
                placeholder="请输入接收邮箱地址"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="邮箱账号" prop="accountId">
              <el-input
                v-model.trim="queryParams.accountId"
                placeholder="请输入邮箱账号"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模板编码" prop="templateCode">
              <el-input
                v-model.trim="queryParams.templateCode"
                placeholder="请输入模板编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发送状态" prop="sendStatus">
              <el-select
                v-model="queryParams.sendStatus"
                placeholder="请选择发送状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_MAIL_SEND_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #userType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.USER_TYPE"
            :model-value="row.userType"
          />
        </template>
        <template #sendStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_MAIL_SEND_STATUS"
            :model-value="row.sendStatus"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openDetail(row)"
            v-permission="['system:mail-log:query']"
          >
            查看
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <MailLogForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as MailLogApi from '@/api/system/mail/log';
  import MailLogForm from './MailLogDetail.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 邮件日志 列表 */
  defineOptions({ name: 'MailLogIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    userId: undefined,
    userType: undefined,
    toMail: undefined,
    accountId: undefined,
    fromMail: undefined,
    templateId: undefined,
    templateCode: undefined,
    templateNickname: undefined,
    templateTitle: undefined,
    templateContent: undefined,
    templateParams: undefined,
    sendStatus: undefined,
    sendTime: [],
    sendMessageId: undefined,
    sendException: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'userType',
      label: '用户类型',
      align: 'center',
      minWidth: 110,
      slot: 'userType'
    },
    {
      prop: 'fromMail',
      label: '发送邮箱',
      align: 'center',
      minWidth: 190
    },
    {
      prop: 'templateNickname',
      label: '发送人',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateCode',
      label: '模板编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'templateTitle',
      label: '邮件标题',
      align: 'center',
      minWidth: 110
    },

    {
      prop: 'toMail',
      label: '接收邮箱地址',
      align: 'center',
      minWidth: 190
    },
    {
      prop: 'sendStatus',
      label: '发送状态',
      align: 'center',
      minWidth: 110,
      slot: 'sendStatus'
    },
    {
      prop: 'sendTime',
      label: '发送时间',
      align: 'center',
      minWidth: 170,
      formatter: dateFormatter
    },
    {
      prop: 'sendException',
      label: '发送异常',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 170,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return MailLogApi.getMailLogPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openDetail = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
</script>
