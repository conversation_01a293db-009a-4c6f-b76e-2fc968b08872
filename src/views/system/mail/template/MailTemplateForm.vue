<template>
  <ele-modal
    form
    :width="880"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="8">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入模板名称" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="模板编码" prop="code">
            <el-input v-model="form.code" placeholder="请输入模板编码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="8">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="模板标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入模板标题" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="发送人名称" prop="nickname">
            <el-input v-model="form.nickname" placeholder="请输入发送人名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="模板内容" prop="content">
        <tinymce-editor v-model="form.content" :init="config" />
      </el-form-item>
      <el-form-item label="邮箱账号" prop="accountId">
        <el-select v-model="form.accountId" placeholder="请选择短信类型">
          <el-option
            v-for="item in accountList"
            :key="item.id"
            :label="item.mail"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开启状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as MailTemplateApi from '@/api/system/mail/template';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';
  import * as MailAccountApi from '@/api/system/mail/account';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 邮件模版 表单 */
  defineOptions({ name: 'MailTemplateForm' });
  const accountList = ref([]);

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const config = ref({
    height: 380
  });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    name: undefined,
    code: undefined,
    accountId: undefined,
    nickname: undefined,
    title: undefined,
    content: undefined,
    params: undefined,
    status: undefined,
    remark: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
    code: [{ required: true, message: '模板编码不能为空', trigger: 'blur' }],
    accountId: [
      { required: true, message: '发送的邮箱账号编号不能为空', trigger: 'blur' }
    ],
    title: [{ required: true, message: '模板标题不能为空', trigger: 'blur' }],
    content: [{ required: true, message: '模板内容不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await MailTemplateApi.createMailTemplate(form);
        message.success(t('common.createSuccess'));
      } else {
        console.log(form);
        await MailTemplateApi.updateMailTemplate(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await MailTemplateApi.getMailTemplate(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      accountList.value = await MailAccountApi.getSimpleMailAccountList();
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
