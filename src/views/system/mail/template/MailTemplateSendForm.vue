<template>
  <ele-modal
    form
    :width="880"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    destroy-on-close
    title="发送测试"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="模板内容" prop="content">
        <tinymce-editor v-model="formData.content" :init="config" />
      </el-form-item>
      <el-form-item label="收件邮箱" prop="mail">
        <el-input v-model="formData.mail" placeholder="请输入收件邮箱" />
      </el-form-item>
      <el-form-item
        v-for="param in formData.params"
        :key="param"
        :label="'参数 {' + param + '}'"
        :prop="'templateParams.' + param"
      >
        <el-input
          v-model="formData.templateParams[param]"
          :placeholder="'请输入 ' + param + ' 参数'"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </ele-modal>
</template>
<script lang="ts" setup>
  import * as MailTemplateApi from '@/api/system/mail/template';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';

  defineOptions({ name: 'SystemMailTemplateSendForm' });

  const message = useMessage(); // 消息弹窗
  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const dialogVisible = defineModel({ type: Boolean });
  const formLoading = ref(false); // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
  const formData = ref({
    content: '',
    params: {},
    mail: '',
    templateCode: '',
    templateParams: new Map()
  });
  const formRules = reactive({
    mail: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }],
    templateCode: [
      { required: true, message: '模版编号不能为空', trigger: 'blur' }
    ],
    templateParams: {}
  });
  const formRef = ref(); // 表单 Ref
  const config = ref({
    height: 380
  });
  /** 打开弹窗 */
  const handleOpen = async () => {
    dialogVisible.value = true;
    resetForm();
    // 设置数据
    formLoading.value = true;
    try {
      const data = await MailTemplateApi.getMailTemplate(props.data.id);
      // 设置动态表单
      formData.value.content = data.content;
      formData.value.params = data.params;
      formData.value.templateCode = data.code;
      formData.value.templateParams = data.params.reduce((obj, item) => {
        obj[item] = ''; // 给每个动态属性赋值，避免无法读取
        return obj;
      }, {});
      formRules.templateParams = data.params.reduce((obj, item) => {
        obj[item] = {
          required: true,
          message: '参数 ' + item + ' 不能为空',
          trigger: 'blur'
        };
        return obj;
      }, {});
    } finally {
      formLoading.value = false;
    }
  };
  /** 提交表单 */
  const submitForm = async () => {
    // 校验表单
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    formLoading.value = true;
    try {
      const data = formData.value as MailTemplateApi.MailSendReqVO;
      const logId = await MailTemplateApi.sendMail(data);
      if (logId) {
        message.success('提交发送成功！发送结果，见发送日志编号：' + logId);
      }
      dialogVisible.value = false;
    } finally {
      formLoading.value = false;
    }
  };

  /** 重置表单 */
  const resetForm = () => {
    formData.value = {
      content: '',
      params: {},
      mail: '',
      templateCode: '',
      templateParams: new Map()
    };
    formRules.templateParams = {};
    formRef.value?.resetFields();
  };
</script>
