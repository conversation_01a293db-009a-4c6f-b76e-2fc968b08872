<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模板名称" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入模板名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模板编码" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入模板编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['system:mail-template:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openSendForm(row)"
            v-permission="['system:mail-template:send-mail']"
          >
            测试发送
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:mail-template:send-mail']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['system:mail-template:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:mail-template:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['system:mail-template:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <MailTemplateForm v-model="showEdit" :data="current" @done="reload" />
    <!-- 表单弹窗：发送测试 -->
    <MailTemplateSendForm v-model="showSendForm" :data="current"/>
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as MailTemplateApi from '@/api/system/mail/template';
  import MailTemplateForm from './MailTemplateForm.vue';
  import MailTemplateSendForm from './MailTemplateSendForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import * as MailAccountApi from '@/api/system/mail/account';
  import { DICT_TYPE } from '@/utils/dict';

  /** 邮件模版 列表 */
  defineOptions({ name: 'MailTemplateIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: undefined,
    code: undefined,
    accountId: undefined,
    nickname: undefined,
    title: undefined,
    content: undefined,
    params: undefined,
    status: undefined,
    remark: undefined,
    createTime: []
  });
  const accountList = ref([]);
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  //发送测试操作
  const showSendForm = ref(false);

  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '模板名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'code',
      label: '模板编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'accountId',
      label: '邮箱账号',
      align: 'center',
      minWidth: 220,
      formatter: (record) => {
        return accountList.value.find(
          (account) => account.id === record.accountId
        )?.mail;
      }
    },
    {
      prop: 'nickname',
      label: '发送人名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'title',
      label: '模板标题',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'content',
      label: '模板内容',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'params',
      label: '参数数组',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '开启状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 190,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 240,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return MailTemplateApi.getMailTemplatePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const openSendForm = (row) => {
    current.value = row ?? null;
    showSendForm.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await MailTemplateApi.deleteMailTemplate(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  onMounted(async () => {
    accountList.value = await MailAccountApi.getSimpleMailAccountList();
  });
</script>
