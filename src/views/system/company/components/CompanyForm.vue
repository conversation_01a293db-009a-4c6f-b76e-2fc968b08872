<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="700"
    :model-value="modelValue"
    :title="isUpdate ? '修改公司' : '添加公司'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent=""
    >
      <el-form-item label="上级公司" prop="parentId">
        <company-select v-model="form.parentId" placeholder="请选择上级公司" />
      </el-form-item>
      <el-row>
        <el-form-item label="公司编码" prop="code">
          <el-input
            clearable
            :maxlength="20"
            v-model="form.code"
            placeholder="请输入公司编码"
          />
        </el-form-item>
        <el-form-item label="公司名称" prop="name">
          <el-input
            clearable
            :maxlength="20"
            v-model="form.name"
            placeholder="请输入公司名称"
          />
        </el-form-item>
      </el-row>
      <el-form-item label="显示排序" prop="sort">
        <el-input-number
          :min="0"
          :max="99999"
          v-model="form.sort"
          placeholder="请输入显示排序"
          controls-position="right"
          class="ele-fluid"
        />
      </el-form-item>
      <el-form-item label="公司状态" prop="status">
        <el-select v-model="form.status" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-row>
        <el-form-item label="联系电话" prop="phone">
          <el-input
            clearable
            v-model="form.phone"
            placeholder="请输入联系电话"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input clearable v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="公司地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入公司地址" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="营业执照编号" prop="businessLicenseNumber">
          <el-input
            v-model="form.businessLicenseNumber"
            placeholder="请输入营业执照编号"
          />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="联系人" prop="contacts">
          <el-input v-model="form.contacts" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactsInformation">
          <el-input
            v-model="form.contactsInformation"
            placeholder="请输入联系方式"
          />
        </el-form-item>
      </el-row>
      <el-form-item label="负责人" prop="leaderUserId">
        <el-input v-model="form.leaderUserId" placeholder="请输入负责人" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as CompanyApi from '@/api/system/company';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import CompanySelect from './company-select.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 上级部门id */
    parentId: Number
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    code: undefined,
    name: undefined,
    address: undefined,
    phone: undefined,
    email: undefined,
    businessLicenseNumber: undefined,
    contacts: undefined,
    contactsInformation: undefined,
    parentId: undefined,
    sort: undefined,
    leaderUserId: undefined,
    status: undefined
  });

  /** 表单验证规则 */
  const rules = reactive({
    code: [{ required: true, message: '公司编码不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],
    sort: [{ required: true, message: '显示顺序不能为空', trigger: 'blur' }],
    status: [
      {
        required: true,
        message: '公司状态（0正常 1停用）不能为空',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? CompanyApi.updateCompany
        : CompanyApi.createCompany;
      saveOrUpdate({ ...form, parentId: form.parentId || 0 })
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data,
            parentId: props.data.parentId || void 0
          });
          isUpdate.value = true;
        } else {
          form.parentId = props.parentId;
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
