<!-- 公司选择下拉框 -->
<template>
  <el-tree-select
    clearable
    check-strictly
    default-expand-all
    :data="data"
    node-key="id"
    :props="{ label: 'name' }"
    :model-value="modelValue"
    :placeholder="placeholder"
    class="ele-fluid"
    @update:modelValue="updateValue"
  />
</template>

<script setup>
  import { ref } from 'vue';
  import { EleMessage, toTree } from 'ele-admin-plus/es';
  import * as CompanyApi from '@/api/system/company';

  const emit = defineEmits(['update:modelValue']);

  defineProps({
    /** 选中的公司 */
    modelValue: [Number, String],
    /** 提示信息 */
    placeholder: {
      type: String,
      default: '请选择归属公司'
    }
  });

  /** 公司数据 */
  const data = ref([]);

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 获取公司数据 */
  CompanyApi.getCompanyList()
    .then((list) => {
      data.value = toTree({
        data: list,
        idField: 'id',
        parentIdField: 'parentId'
      });
    })
    .catch((e) => {
      EleMessage.error(e.message);
    });
</script>
