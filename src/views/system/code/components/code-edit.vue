<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="460"
    :model-value="modelValue"
    :title="isUpdate ? '修改编码' : '添加编码'"
    @update:modelValue="updateModelValue"
    @open="open"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="编码" prop="code">
        <el-input clearable v-model="form.code" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item label="描述信息" prop="description">
        <el-input
          clearable
          v-model="form.description"
          placeholder="请输入描述信息"
        />
      </el-form-item>
      <el-form-item label="编码状态" prop="status">
        <el-select v-model="form.isEnable" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { CodeApi } from '@/api/system/code';
  import { CommonStatusEnum } from '@/utils/constants';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    description: '',
    code: '',
    isEnable: CommonStatusEnum.ENABLE
  });

  /** 表单验证规则 */
  const rules = reactive({
    description: [
      {
        required: true,
        message: '请输入编码描述',
        type: 'string',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入编码',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? CodeApi.updateCode
        : CodeApi.createCode;
      try {
        await saveOrUpdate(form);
        EleMessage.success('保存成功！');
        updateModelValue(false);
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const open = async () => {
    if (props.data) {
      const res = await CodeApi.getCode(props.data.id);
      assignFields(res);
      isUpdate.value = true;
    } else {
      isUpdate.value = false;
      resetFields();
    }
  };
</script>
