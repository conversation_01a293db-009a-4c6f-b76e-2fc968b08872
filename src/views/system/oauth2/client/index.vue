<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="应用名">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入应用名"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            :icon="Plus"
            v-permission="'system:oauth2-client:create'"
            @click="openForm('create')"
          >
            新增
          </el-button>
        </template>
        <template #logo="{ row }">
          <img width="40px" height="40px" :src="row.logo" />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #accessTokenValiditySeconds="{ row }">
          {{ row.accessTokenValiditySeconds }} 秒
        </template>
        <template #refreshTokenValiditySeconds="{ row }">
          {{ row.accessTokenValiditySeconds }} 秒
        </template>
        <template #authorizedGrantTypes="{ row }">
          <el-tag
            :disable-transitions="true"
            :key="index"
            v-for="(authorizedGrantType, index) in row.authorizedGrantTypes"
            :index="index"
            class="mr-5px"
          >
            {{ authorizedGrantType }}
          </el-tag>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm('update', row.id)"
            v-permission="['system:oauth2-client:update']"
          >
            编辑
          </el-link>
          <el-divider
            v-permission="['system:oauth2-client:delete']"
            direction="vertical"
          />
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
            v-permission="['system:oauth2-client:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单弹窗：添加/修改 -->
    <ClientForm ref="formRef" @success="reload" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as ClientApi from '@/api/system/oauth2/client';
  import ClientForm from './ClientForm.vue';
  import { Plus, Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'SystemOAuth2Client' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    userId: null,
    userType: undefined,
    clientId: null
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'clientId',
        label: '客户端',
        align: 'center',
        minWidth: 230
      },
      {
        prop: 'name',
        label: '应用名',
        align: 'center',
        minWidth: 140
      },
      {
        prop: 'logo',
        label: '应用图标',
        align: 'center',
        minWidth: 100,
        slot: 'logo'
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        minWidth: 100,
        slot: 'status'
      },
      {
        prop: 'accessTokenValiditySeconds',
        label: '访问令牌的有效期',
        align: 'center',
        minWidth: 200,
        slot: 'accessTokenValiditySeconds'
      },
      {
        prop: 'refreshTokenValiditySeconds',
        label: '刷新令牌的有效期',
        align: 'center',
        minWidth: 200,
        slot: 'refreshTokenValiditySeconds'
      },
      {
        prop: 'authorizedGrantTypes',
        label: '授权类型',
        align: 'center',
        minWidth: 350,
        slot: 'authorizedGrantTypes'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ClientApi.getOAuth2ClientPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type: string, id?: number) => {
    formRef.value.open(type, id);
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await ClientApi.deleteOAuth2Client(id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
