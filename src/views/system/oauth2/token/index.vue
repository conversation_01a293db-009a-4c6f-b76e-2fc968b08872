<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="用户编号">
              <el-input
                clearable
                v-model.trim="queryParams.userId"
                placeholder="请输入用户编号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="用户类型">
              <el-select
                v-model="queryParams.userType"
                placeholder="请选择用户类型"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.USER_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户端">
              <el-input
                clearable
                v-model.trim="queryParams.clientId"
                placeholder="请输入客户端编号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #userType="{ row }">
          <dict-data
            :code="DICT_TYPE.USER_TYPE"
            type="tag"
            :model-value="row.userType"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="danger"
            @click="handleForceLogout(row.accessToken)"
            v-permission="['system:oauth2-token:delete']"
          >
            强退
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as OAuth2AccessTokenApi from '@/api/system/oauth2/token';
  import { Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'SystemTokenClient' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    userId: null,
    userType: undefined,
    clientId: null
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'clientId',
        label: '客户端',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'accessToken',
        label: '访问令牌',
        align: 'center',
        minWidth: 300
      },
      {
        prop: 'refreshToken',
        label: '刷新令牌',
        align: 'center',
        minWidth: 300
      },
      {
        prop: 'userId',
        label: '用户编号',
        align: 'center',
        minWidth: 110,
      },
      {
        prop: 'userType',
        label: '用户类型',
        align: 'center',
        minWidth: 110,
        slot: 'userType'
      },
      {
        prop: 'expiresTime',
        label: '过期时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return OAuth2AccessTokenApi.getAccessTokenPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 强制退出操作 */
  const handleForceLogout = async (accessToken: string) => {
    try {
      // 删除的二次确认
      await message.confirm('是否要强制退出用户');
      // 发起删除
      await OAuth2AccessTokenApi.deleteAccessToken(accessToken);
      message.success(t('common.success'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
