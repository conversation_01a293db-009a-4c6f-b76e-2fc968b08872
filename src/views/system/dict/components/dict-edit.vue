<!-- 字典编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="460"
    :title="title"
    destroy-on-close
    v-model="visible"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="字典名称" prop="name">
        <el-input clearable v-model="form.name" placeholder="请输入字典名称" />
      </el-form-item>
      <el-form-item label="字典类型" prop="type">
        <el-input clearable v-model="form.type" placeholder="请输入字典类型" />
      </el-form-item>
      <!-- <el-form-item label="状态" prop="status">
        <dict-data
          code="sys_normal_disable"
          type="radio"
          v-model="form.status"
        />
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          :rows="4"
          type="textarea"
          :maxlength="200"
          v-model="form.remark"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import * as DictTypeApi from '@/api/system/dict/dict.type';

  const emit = defineEmits(['done', 'update:modelValue']);
  const title = ref(''); // 弹窗的标题

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    name: '',
    type: '',
    status: 0,
    remark: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入字典名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请输入字典类型',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = async () => {
    // 校验表单
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await DictTypeApi.createDictType(form);
        message.success(t('common.createSuccess'));
      } else {
        await DictTypeApi.updateDictType(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    resetFields();
    try {
      if (props.data) {
        const result = await DictTypeApi.getDictType(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
