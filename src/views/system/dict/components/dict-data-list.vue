<template>
  <ele-card flex-table style="height: 100%">
    <dict-data-search
      ref="searchRef"
      style="margin-bottom: -14px"
      @search="reload"
    />
    <!-- 表格 -->
    <ele-pro-table
      ref="tableRef"
      row-key="dictCode"
      :columns="columns"
      :datasource="datasource"
      :load-on-created="false"
      :show-overflow-tooltip="true"
      highlight-current-row
      :footer-style="{ paddingBottom: '16px' }"
      cache-key="systemDictDataTable"
    >
      <template #toolbar>
        <el-space :size="12" wrap>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
        </el-space>
      </template>
      <template #status="{ row }">
        <dict-data
          :code="DICT_TYPE.COMMON_STATUS"
          type="tag"
          :model-value="row.status"
        />
      </template>
      <template #action="{ row }">
        <el-link type="primary" :underline="false" @click="openEdit(row)">
          修改
        </el-link>
        <el-divider direction="vertical" />
        <el-link type="danger" :underline="false" @click="removeBatch(row)">
          删除
        </el-link>
      </template>
    </ele-pro-table>
    <!-- 编辑弹窗 -->
    <dict-data-edit
      v-model="showEdit"
      :data="current"
      :dict-type="dictType"
      @done="reload"
    />
  </ele-card>
</template>

<script setup>
  import { DICT_TYPE } from '@/utils/dict';
  import { ref, watch } from 'vue';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import DictDataSearch from './dict-data-search.vue';
  import DictDataEdit from './dict-data-edit.vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useUserStore } from '@/store/modules/user';
  import * as DictDataApi from '@/api/system/dict/dict.data';
  const props = defineProps({
    /** 字典类型 */
    dictType: String
  });
  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  const userStore = useUserStore();

  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'label',
      label: '数据标签',
      align: 'center',
      minWidth: 140
    },
    {
      prop: 'value',
      label: '数据键值',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'sort',
      label: '显示排序',
      width: 110,
      align: 'center'
    },
    {
      prop: 'status',
      label: '状态',
      width: 90,
      align: 'center',
      slot: 'status'
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 180,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 130,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true
    }
  ]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return DictDataApi.getDictDataPage({
      ...where,
      ...orders,
      ...pages,
      dictType: props.dictType
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 批量删除 */
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await DictDataApi.deleteDictData(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };

  // 监听字典id变化
  watch(
    () => props.dictType,
    () => {
      searchRef.value?.resetFields?.();
      reload({});
    }
  );
</script>
