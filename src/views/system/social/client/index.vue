<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="应用名">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入应用名"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="社交平台">
              <el-select
                v-model="queryParams.socialType"
                placeholder="请选择社交平台"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_SOCIAL_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="用户类型">
              <el-select
                v-model="queryParams.userType"
                placeholder="请选择用户类型"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.USER_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户端">
              <el-input
                clearable
                v-model.trim="queryParams.clientId"
                placeholder="请输入客户端编号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态">
              <el-select
                v-model="queryParams.status"
                clearable
                placeholder="请选择状态"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            :icon="Plus"
            v-permission="'system:social-client:create'"
            @click="openForm('create')"
          >
            新增
          </el-button>
        </template>
        <template #socialType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SOCIAL_TYPE"
            :model-value="row.socialType"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm('update', row.id)"
            v-permission="['system:social-client:update']"
          >
            编辑
          </el-link>
          <el-divider
            v-permission="['system:social-client:delete']"
            direction="vertical"
          />
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
            v-permission="['system:social-client:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单弹窗：添加/修改 -->
    <SocialClientForm ref="formRef" @success="reload" />
  </ele-page>
</template>

<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as SocialClientApi from '@/api/system/social/client';
  import SocialClientForm from './SocialClientForm.vue';
  import { Plus, Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'SocialClient' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    userId: null,
    userType: undefined,
    clientId: null
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '应用名',
        align: 'center',
        minWidth: 170
      },
      {
        prop: 'application',
        label: '应用编码',
        align: 'center',
        minWidth: 170
      },
      {
        prop: 'socialType',
        label: '社交平台',
        align: 'center',
        minWidth: 140,
        slot: 'socialType'
      },
      {
        prop: 'clientId',
        label: '客户端编号',
        align: 'center',
        minWidth: 180,
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        minWidth: 100,
        slot: 'status'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return SocialClientApi.getSocialClientPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type: string, id?: number) => {
    formRef.value.open(type, id);
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await SocialClientApi.deleteSocialClient(id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };

</script>
