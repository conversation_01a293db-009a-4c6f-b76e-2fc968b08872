<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="社交平台">
              <el-select
                v-model="queryParams.type"
                placeholder="请选择社交平台"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_SOCIAL_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="用户昵称">
              <el-input
                v-model="queryParams.nickname"
                clearable
                placeholder="请输入用户昵称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="OpenId">
              <el-input
                v-model="queryParams.openid"
                clearable
                placeholder="请输社交平台OpenId"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                end-placeholder="结束日期"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SOCIAL_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #avatar="{ row }">
          <el-image :src="row.avatar" class="h-30px w-30px" @click="imagePreview(row.avatar)" />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openDetail(row.id)"
            v-permission="['system:social-user:query']"
          >
            详细
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单弹窗：添加/修改 -->
    <SocialUserDetail ref="detailRef" />
  </ele-page>
</template>

<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as SocialUserApi from '@/api/system/social/user';
  import SocialUserDetail from './SocialUserDetail.vue';
  import { createImageViewer } from '@/components/ImageViewer';
  import { Plus, Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'SocialUser' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    userId: null,
    userType: undefined,
    clientId: null
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '应用名',
        align: 'center',
        minWidth: 140
      },
      {
        prop: 'type',
        label: '社交平台',
        align: 'center',
        minWidth: 140,
        slot: 'type'
      },
      {
        prop: 'openid',
        label: '平台OpenId',
        align: 'center',
        minWidth: 120,
      },
      {
        prop: 'nickname',
        label: '用户昵称',
        align: 'center',
        minWidth: 120,
      },
      {
        prop: 'avatar',
        label: '用户头像',
        align: 'center',
        minWidth: 120,
        slot:'avatar'
      },
      {
        prop: 'clientId',
        label: '客户端编号',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'updateTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 90,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return SocialUserApi.getSocialUserPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  const imagePreview = (imgUrl: string) => {
    createImageViewer({
      urlList: [imgUrl]
    });
  };

  /** 详情操作 */
  const detailRef = ref();
  const openDetail = (id: number) => {
    detailRef.value.open(id);
  };
</script>
