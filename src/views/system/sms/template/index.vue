<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="短信类型">
              <el-select
                v-model="queryParams.type"
                placeholder="请选择短信类型"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="开启状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择开启状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_SMS_SEND_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="模板编码">
              <el-input
                v-model="queryParams.code"
                placeholder="请输入模板编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="openForm('create')"
            v-permission="['system:sms-template:create']"
          >
            新增
          </el-button>
          <el-button
            type="success"
            class="ele-btn-icon"
            :icon="Download"
            :loading="exportLoading"
            @click="handleExport"
            v-permission="['system:sms-template:export']"
          >
            导出
          </el-button>
        </template>
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #channelCode="{ row }">
          <div>
            {{
              channelList.find((channel) => channel.id === row.channelId)
                ?.signature
            }}
          </div>
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE"
            :model-value="row.channelCode"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm('update', row.id)"
            v-permission="['system:sms-template:update']"
          >
            修改
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:sms-template:send-sms']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="openSendForm(row.id)"
            v-permission="['system:sms-template:send-sms']"
          >
            测试
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['system:sms-template:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="handleDelete(row.id)"
            v-permission="['system:sms-template:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 表单弹窗：添加/修改 -->
    <SmsTemplateForm ref="formRef" @success="reload" />
    <!-- 表单弹窗：测试发送 -->
    <SmsTemplateSendForm ref="sendFormRef" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as SmsTemplateApi from '@/api/system/sms/smsTemplate';
  import * as SmsChannelApi from '@/api/system/sms/smsChannel';
  import download from '@/utils/download';
  import SmsTemplateForm from './SmsTemplateForm.vue';
  import SmsTemplateSendForm from './SmsTemplateSendForm.vue';
  import { Plus, Download, Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'SystemSmsTemplate' });
  const channelList = ref<SmsChannelApi.SmsChannelVO[]>([]); // 短信渠道列表
  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  const exportLoading = ref(false); // 导出的加载中

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    type: undefined,
    status: undefined,
    code: '',
    content: '',
    apiTemplateId: '',
    channelId: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'code',
        label: '模板编码',
        align: 'center',
        minWidth: 170
      },
      {
        prop: 'name',
        label: '模板名称',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'content',
        label: '模板内容',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'type',
        label: '短信类型',
        align: 'center',
        minWidth: 100,
        slot: 'type'
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        minWidth: 100,
        slot: 'status'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'channelCode',
        label: '短信渠道',
        align: 'center',
        minWidth: 180,
        slot: 'channelCode'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return SmsTemplateApi.getSmsTemplatePage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type: string, id?: number) => {
    formRef.value.open(type, id);
  };

  /** 发送短信按钮 */
  const sendFormRef = ref();
  const openSendForm = (id: number) => {
    sendFormRef.value.open(id);
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await SmsTemplateApi.deleteSmsTemplate(id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };

  /** 导出按钮操作 */
  const handleExport = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await SmsTemplateApi.exportSmsTemplate(queryParams);
      download.excel(data, '短信模板.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  /** 初始化 **/
  onMounted(async () => {
    // 加载渠道列表
    channelList.value = await SmsChannelApi.getSimpleSmsChannelList();
  });
</script>
