<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="手机号">
              <el-input
                clearable
                v-model.trim="queryParams.mobile"
                placeholder="请输入手机号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="短信渠道">
              <el-select
                v-model="queryParams.channelId"
                placeholder="请选择短信渠道"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="channel in channelList"
                  :key="channel.id"
                  :value="channel.id"
                  :label="
                    channel.signature +
                    `【 ${getDictLabel(DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE, channel.code)}】`
                  "
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发送状态">
              <el-select
                v-model="queryParams.sendStatus"
                placeholder="请选择发送状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.SYSTEM_SMS_SEND_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发送时间">
              <el-date-picker
                v-model="queryParams.sendTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="success"
            class="ele-btn-icon"
            :icon="Download"
            :loading="exportLoading"
            @click="handleExport"
            v-permission="['system:sms-log:export']"
          >
            导出
          </el-button>
        </template>
        <template #mobile="{ row }">
          <div>{{ row.mobile }}</div>
        </template>
        <template #sendStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SMS_SEND_STATUS"
            :model-value="row.sendStatus"
          />
          <div>{{ formatDate(row.sendTime) }}</div>
        </template>
        <template #receiveStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SMS_RECEIVE_STATUS"
            :model-value="row.receiveStatus"
          />
        </template>
        <template #channelCode="{ row }">
          <div>
            {{
              channelList.find((channel) => channel.id === row.channelId)
                ?.signature
            }}
          </div>
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE"
            :model-value="row.channelCode"
          />
        </template>
        <template #templateType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.SYSTEM_SMS_TEMPLATE_TYPE"
            :model-value="row.templateType"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openDetail(row)"
            v-permission="['system:sms-log:query']"
          >
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 表单弹窗：详情 -->
    <SmsLogDetail ref="detailRef" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict';
  import { dateFormatter, formatDate } from '@/utils/formatTime';
  import download from '@/utils/download';
  import * as SmsChannelApi from '@/api/system/sms/smsChannel';
  import * as SmsLogApi from '@/api/system/sms/smsLog';
  import SmsLogDetail from './SmsLogDetail.vue';
  import { Download, Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'SystemSmsLog' });
  const exportLoading = ref(false) // 导出的加载中

  const message = useMessage(); // 消息弹窗
  const channelList = ref([]); // 短信渠道列表
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    channelId: null,
    templateId: null,
    mobile: '',
    sendStatus: null,
    receiveStatus: null,
    sendTime: [],
    receiveTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'mobile',
        label: '收件人',
        align: 'center',
        minWidth: 130,
        slot: 'mobile'
      },

      {
        prop: 'sendStatus',
        label: '发送状态',
        align: 'center',
        minWidth: 170,
        slot: 'sendStatus'
      },
      {
        prop: 'templateContent',
        label: '短信内容',
        align: 'center',
        minWidth: 300
      },
      {
        prop: 'receiveStatus',
        label: '接收状态',
        align: 'center',
        minWidth: 100,
        slot: 'receiveStatus'
      },
      {
        prop: 'channelCode',
        label: '短信渠道',
        align: 'center',
        minWidth: 100,
        slot: 'channelCode'
      },
      {
        prop: 'templateType',
        label: '短信类型',
        align: 'center',
        minWidth: 100,
        slot: 'templateType'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 80,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return SmsLogApi.getSmsLogPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 导出按钮操作 */
  const handleExport = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await SmsLogApi.exportSmsLog(queryParams);
      download.excel(data, '短信日志.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  /** 详情操作 */
  const detailRef = ref();
  const openDetail = (data: SmsLogApi.SmsLogVO) => {
    detailRef.value.open(data);
  };

  /** 初始化 **/
  onMounted(async () => {
    // 加载渠道列表
    channelList.value = await SmsChannelApi.getSimpleSmsChannelList();
  });
</script>
