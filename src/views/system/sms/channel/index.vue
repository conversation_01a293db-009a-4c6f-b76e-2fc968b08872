<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="短信签名">
              <el-input
                clearable
                v-model.trim="queryParams.signature"
                placeholder="请输入短信签名"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="启用状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择启用状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="openForm('create')"
            v-permission="['system:sms-channel:create']"
          >
            新增
          </el-button>
        </template>
        <template #code="{ row }">
          <dict-data type="tag" :code="DICT_TYPE.SYSTEM_SMS_CHANNEL_CODE" :model-value="row.code" />
        </template>
        <template #status="{ row }">
          <dict-data type="tag"  :code="DICT_TYPE.COMMON_STATUS" :model-value="row.status" />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm('update', row.id)"
            v-permission="['system:sms-channel:update']"
          >
            修改
          </el-link>
          <el-divider direction="vertical" v-permission="['system:sms-channel:delet']"/>
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
            v-permission="['system:sms-channel:delet']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 表单弹窗：添加/修改 -->
    <SmsChannelForm ref="formRef" @success="reload" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as SmsChannelApi from '@/api/system/sms/smsChannel';
  import SmsChannelForm from './SmsChannelForm.vue';
  import { Plus,Search,Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'SystemSmsChannel' });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    signature: undefined,
    status: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'signature',
        label: '短信签名',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'code',
        label: '渠道编码',
        align: 'center',
        minWidth: 130,
        slot: 'code'
      },
      {
        prop: 'status',
        label: '启用状态',
        align: 'center',
        minWidth: 100,
        slot: 'status'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'apiKey',
        label: '短信 API 的账号',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'apiSecret',
        label: '短信 API 的密钥',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'callbackUrl',
        label: '短信发送回调 URL',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return SmsChannelApi.getSmsChannelPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type: string, id?: number) => {
    formRef.value.open(type, id);
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await SmsChannelApi.deleteSmsChannel(id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>