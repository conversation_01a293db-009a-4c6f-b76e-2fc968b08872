<template>
  <ele-modal
    form
    :width="880"
    destroy-on-close
    :close-on-click-modal="false"
    v-model="visible"
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="95px"
      v-loading="loading"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="服务商名称" prop="vendorName">
            <el-input
              v-model="form.vendorName"
              placeholder="请输入服务商名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="服务商详细" prop="vendorDesc">
        <tinymce-editor
          ref="editorRef"
          :init="config"
          v-model="form.vendorDesc"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as VendorsApi from '@/api/cux/vendors';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 服务商列 表单 */
  defineOptions({ name: 'VendorsForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗
  const editorRef = ref(null);
  /** 编辑器配置 */
  const config = ref({
    height: 380
  });
  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    vendorName: undefined,
    vendorDesc: undefined,
    status: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    vendorName: [
      { required: true, message: '服务商名称不能为空', trigger: 'blur' }
    ],
    status: [
      {
        required: true,
        message: '状态（0正常 1停用）不能为空',
        trigger: 'blur'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await VendorsApi.createVendors(form);
        message.success(t('common.createSuccess'));
      } else {
        await VendorsApi.updateVendors(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await VendorsApi.getVendors(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
