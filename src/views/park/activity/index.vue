<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="活动标题" prop="title">
              <el-input
                v-model.trim="queryParams.title"
                placeholder="请输入活动标题"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="queryParams.startTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="queryParams.endTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="报名时间" prop="joinEndTime">
              <el-date-picker
                v-model="queryParams.joinEndTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['cux:activity:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #hotFlag="{ row }">
          <dict-data
            :code="DICT_TYPE.PARK_HOT_FLAG"
            type="tag"
            :model-value="row.hotFlag"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['cux:activity:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['cux:activity:delete']"
          />
          <el-link
            :underline="false"
            type="danger"
            @click="removeBatch(row)"
            v-permission="['cux:activity:delete']"
          >
            删除
          </el-link>
          <el-divider direction="vertical" />
          <ele-dropdown
            :items="[
              { title: '参与人员', command: 'join' },
              { title: '活动评论', command: 'comment' },
              { title: '活动二维码', command: 'code' }
            ]"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <ActivityForm v-model="showEdit" :data="current" @done="reload" />
    <JoinUserForm v-model="showJoin" :data="current" @done="reload" />
    <CommentForm v-model="showComment" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter2 } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as ActivityApi from '@/api/cux/activity/index';
  import ActivityForm from './ActivityForm.vue';
  import JoinUserForm from './JoinUserForm.vue';
  import CommentForm from './CommentForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ArrowDown } from '@/components/icons';
  /** 活动列 列表 */
  defineOptions({ name: 'ActivityIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    title: undefined,
    startTime: [],
    endTime: [],
    joinEndTime: [],
    address: undefined,
    warmReminder: undefined,
    label: undefined,
    detail: undefined,
    imageUrl: undefined,
    status: undefined,
    replayUrl: undefined,
    hotFlag: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'title',
      label: '活动标题',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'startTime',
      label: '活动开始时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'endTime',
      label: '活动结束时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'joinEndTime',
      label: '报名截止时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'address',
      label: '活动地址',
      align: 'center',
      minWidth: 170
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'hotFlag',
      label: '是否热门',
      align: 'center',
      minWidth: 110,
      slot: 'hotFlag'
    },
    {
      prop: 'replayUrl',
      label: '活动回放地址',
      align: 'center',
      minWidth: 110
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 170,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  const showJoin = ref(false);
  const showComment = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ActivityApi.getActivityPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await ActivityApi.deleteActivity(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  // 生成小程序二维码的函数
  const generateQrcode = async (row) => {
    const result = await ActivityApi.getActivityCode(row.id);
    console.log(result);
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  /** 打开编辑弹窗 */
  const openJoin = (row) => {
    current.value = row ?? null;
    showJoin.value = true;
  };
  /** 打开编辑弹窗 */
  const openComment = (row) => {
    current.value = row ?? null;
    showComment.value = true;
  };
  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'join') {
      openJoin(row);
    } else if (key === 'comment') {
      openComment(row);
    } else if (key === 'code') {
      generateQrcode(row);
    }
  };
</script>
