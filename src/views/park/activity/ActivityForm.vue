<template>
  <ele-drawer
    form
    :size="'70%'"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="130px"
      v-loading="loading"
    >
      <el-form-item label="活动标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入活动标题" />
      </el-form-item>
      <el-row>
        <el-col :lg="8" :md="12" :sm="24" :xs="24">
          <el-form-item label="活动开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="date"
              value-format="x"
              placeholder="选择活动开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="24" :xs="24">
          <el-form-item label="活动结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="date"
              value-format="x"
              placeholder="选择活动结束时间"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="24" :xs="24">
          <el-form-item label="报名截止时间" prop="joinEndTime">
            <el-date-picker
              v-model="form.joinEndTime"
              type="date"
              value-format="x"
              placeholder="选择报名截止时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="活动地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入活动地址" />
      </el-form-item>
      <el-form-item label="温馨提示" prop="warmReminder">
        <el-input v-model="form.warmReminder" placeholder="请输入温馨提示" />
      </el-form-item>
      <el-form-item label="活动标签" prop="label">
        <ele-edit-tag
          v-model="form.label"
          size="default"
          type="primary"
          effect="light"
          :tooltip-props="{ effect: 'danger' }"
          :readonly="false"
          :disabled="false"
        />
      </el-form-item>
      <el-form-item label="详细描述" prop="detail">
        <tinymce-editor :init="config" v-model="form.detail" />
      </el-form-item>
      <el-form-item label="活动海报" prop="imageUrl">
        <image-upload
          :limit="1"
          v-model="form.imageUrl"
          :fileLimit="2"
          :item-style="{ width: '160px', height: '160px', margin: 0 }"
          :button-style="{ width: '160px', height: '160px', margin: 0 }"
        />
      </el-form-item>
      <el-row>
        <el-col :lg="8" :md="12" :sm="24" :xs="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="24" :xs="24">
          <el-form-item label="是否热门" prop="hotFlag">
            <el-radio-group v-model="form.hotFlag">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.PARK_HOT_FLAG)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="活动回放地址" prop="replayUrl">
        <el-input v-model="form.replayUrl" placeholder="请输入活动回放地址" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-drawer>
</template>
<script setup lang="ts">
  import * as ActivityApi from '@/api/cux/activity/index';
  import { useFormData } from '@/utils/use-form-data';
  import ImageUpload from '@/components/ImageUpload/index.vue';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';

  /** 活动列 表单 */
  defineOptions({ name: 'ActivityForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  /** 编辑器配置 */
  const config = ref({
    height: 380
  });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    title: undefined,
    startTime: undefined,
    endTime: undefined,
    joinEndTime: undefined,
    address: undefined,
    warmReminder: undefined,
    label: '',
    detail: undefined,
    imageUrl: undefined,
    status: 0,
    replayUrl: undefined,
    hotFlag: 0
  });
  /** 表单验证规则 */
  const rules = reactive({
    title: [{ required: true, message: '活动标题不能为空', trigger: 'blur' }],
    startTime: [
      { required: true, message: '活动开始时间不能为空', trigger: 'blur' }
    ],
    endTime: [
      { required: true, message: '活动结束时间不能为空', trigger: 'blur' }
    ],
    joinEndTime: [
      { required: true, message: '报名截止时间不能为空', trigger: 'blur' }
    ],
    address: [{ required: true, message: '活动地址不能为空', trigger: 'blur' }],
    detail: [{ required: true, message: '详细描述不能为空', trigger: 'blur' }],
    status: [
      {
        required: true,
        message: '状态不能为空',
        trigger: 'blur'
      }
    ],
    imageUrl: [
      { required: true, message: '活动海报不能为空', trigger: 'blur' }
    ],
    hotFlag: [{ required: true, message: '是否热门不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    let requst = { ...form };
    //判断是否有标签，如果有，执行转换，否则不执行
    if (requst.label) {
      requst.label = requst.label.join(', ');
    }
    try {
      if (!isUpdate.value) {
        await ActivityApi.createActivity(requst);
        message.success(t('common.createSuccess'));
      } else {
        await ActivityApi.updateActivity(requst);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await ActivityApi.getActivity(props.data.id);
        result.label = result.label.split(',');
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
