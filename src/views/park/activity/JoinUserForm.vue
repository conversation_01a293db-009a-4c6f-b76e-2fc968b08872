<template>
  <ele-drawer
    form
    :size="'70%'"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <ele-card flex-table :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="用户名" prop="nickName">
              <el-input
                v-model.trim="queryParams.nickName"
                placeholder="请输入参与的用户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="参与时间" prop="joinTime">
              <el-date-picker
                v-model="queryParams.joinTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #userProfile="{ row }">
          <el-image
            :src="row.userProfile"
            class="h-38px w-38px mr-10px rounded"
          />
        </template>
      </ele-pro-table>
    </ele-card>
    <template #footer>
      <el-button @click="cancle">关 闭</el-button>
    </template>
  </ele-drawer>
</template>
<script setup lang="ts">
  import { useFormData } from '@/utils/use-form-data';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import * as ActivityJoinApi from '@/api/cux/activity/join';
  import { dateFormatter } from '@/utils/formatTime';
  import download from '@/utils/download';

  /** 活动列 表单 */
  defineOptions({ name: 'ActivityForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const activityId = ref(-1);

  /** 提交状态 */
  const loading = ref(false);

  const [queryParams, resetFields] = useFormData({
    activityId: undefined,
    nickName: undefined,
    joinTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'nickName',
      label: '昵称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'fullName',
      label: '姓名',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'userProfile',
      label: '用户头像',
      align: 'center',
      minWidth: 110,
      slot: 'userProfile'
    },
    {
      prop: 'joinTime',
      label: '参与时间',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter
    },
    {
      prop: 'joinCode',
      label: '报名编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'mobile',
      label: '手机号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'companyName',
      label: '公司',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'position',
      label: '岗位',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'internalUsers',
      label: '内部员工',
      align: 'center',
      minWidth: 110
    }
  ];
  /** 当前编辑数据 */
  const exportLoading = ref(false); // 导出的加载中

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ActivityJoinApi.getActivityJoinPage({
      ...where,
      ...filters,
      ...pages,
      activityId: props.data.id
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      queryParams.activityId = props.data.id;
    } finally {
      loading.value = false;
    }
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await ActivityJoinApi.exportActivityJoin(queryParams);
      download.excel(data, '活动参与人.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
