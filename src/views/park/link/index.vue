<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="120px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="标题" prop="title">
              <el-input
                v-model.trim="queryParams.title"
                placeholder="请输入标题"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="状态"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :default-expand-all="true"
        :datasource="datasource"
        :pagination="false"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增大类
          </el-button>
        </template>
        <template #title="{ row }">
          {{ row.title }}
          <dict-data
            v-if="row.level == 0"
            type="tag"
            :code="DICT_TYPE.PARK_SERVER_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #image="{ row }">
          <el-image :src="row.image" class="h-45px w-45px mr-10px rounded" />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEditLevel2(row, 'add')"
            v-show="row.level == 0"
          >
            新增小类
          </el-link>
          <el-divider direction="vertical" />

          <el-link
            :underline="false"
            v-show="row.level == 0"
            type="primary"
            @click="openEdit(row)"
          >
            编辑
          </el-link>
          <el-link
            :underline="false"
            v-show="row.level == 1"
            type="primary"
            @click="openEditLevel2(row, 'update')"
          >
            编辑
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            :underline="false"
            v-show="row.level == 1"
            type="primary"
            @click="distribute(row)"
          >
            分配服务商
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="danger" @click="removeBatch(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <ServerLinksForm v-model="showEdit" :data="current" @done="reload" />
    <ServerDetailEdit v-model="showAdd" :data="current" @done="reload" />
    <ServerVendor v-model="showDist" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as ServerLinksApi from '@/api/cux/links';
  import ServerLinksForm from './ServerLinksForm.vue';
  import ServerDetailEdit from './ServerDetailEdit.vue';
  import ServerVendor from './ServerVendor.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { toTree } from 'ele-admin-plus/es';

  /** 外部小程序链接 列表 */
  defineOptions({ name: 'MiniProgramLinksIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    title: undefined,
    status: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'title',
      label: '标题',
      align: 'left',
      minWidth: 110,
      slot: 'title'
    },
    {
      prop: 'sort',
      label: '显示顺序',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 230,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  const showAdd = ref(false);
  const showDist = ref(false);
  /** 表格数据源 */
  const datasource = async ({ where, filters }) => {
    const data = await ServerLinksApi.getServerLinksPage({
      ...where,
      ...filters
    });
    return toTree({
      data,
      idField: 'id',
      parentIdField: 'parentId'
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const openEditLevel2 = (row, type) => {
    current.value = row ?? null;
    current.value.editType = type;
    showAdd.value = true;
  };
  const distribute = (row) => {
    current.value = row ?? null;
    showDist.value = true;
  };
  /** 删除 */
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm(
        row.level == 0
          ? '此操作会删除下方所有子节点，确认删除吗'
          : '确认删除当前数据吗'
      );
      // 发起删除
      await ServerLinksApi.deleteServerLink(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
