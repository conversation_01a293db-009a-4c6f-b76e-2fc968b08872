<template>
  <ele-modal
    form
    :width="680"
    destroy-on-close
    :close-on-click-modal="false"
    v-model="visible"
    :title="isUpdate ? '修改' : '新增'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="价格标题" prop="priceTitle">
            <el-input v-model="form.priceTitle" placeholder="请输入价格标题" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="图标" prop="linkImage">
            <el-input v-model="form.linkImage" placeholder="请输入图标" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="颜色" prop="linkColor">
            <el-color-picker v-model="form.linkColor" show-alpha />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="显示顺序" prop="sort">
            <el-input-number
              class="ele-fluid"
              :controls="false"
              v-model="form.sort"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类别" prop="type">
            <el-select v-model="form.type" clearable placeholder="请选择分类">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PARK_SERVER_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import * as ServerLinksApi from '@/api/cux/links';
  import { useFormData } from '@/utils/use-form-data';

  /** 外部小程序链接 表单 */
  defineOptions({ name: 'ServerLinksForm' });

  const serverOption = ref([]);
  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const editorRef = ref(null);
  /** 编辑器配置 */
  const config = ref({
    height: 380
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    level: undefined,
    type: undefined,
    parentId: undefined,
    title: undefined,
    priceTitle: undefined,
    linkImage: undefined,
    linkColor: undefined,
    imageUrl: undefined,
    serverDesc: undefined,
    concatDesc: undefined,
    sort: undefined,
    type: undefined,
    status: 0
  });
  /** 表单验证规则 */
  const rules = reactive({
    priceTitle: [
      { required: true, message: '价格描述不能为空', trigger: 'blur' }
    ],
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    linkImage: [
      { required: true, message: '服务图标不能为空', trigger: 'blur' }
    ],
    imageUrl: [{ required: true, message: '背景图不能为空', trigger: 'blur' }],
    conCatdesc: [
      { required: true, message: '联系方式不能为空', trigger: 'blur' }
    ],
    type: [{ required: true, message: '分类不能为空', trigger: 'blur' }],
    sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
    status: [
      {
        required: true,
        message: '状态不能为空',
        trigger: 'blur'
      }
    ],
    type: [{ required: true, message: '分类不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await ServerLinksApi.createServerLink(form);
        message.success(t('common.createSuccess'));
      } else {
        await ServerLinksApi.updateServerLink(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('success');
    } finally {
      loading.value = false;
    }
  };
  const getServerOption = async () => {
    const result = await ServerLinksApi.getServerLinksPage({ level: 0 });
    serverOption.value = result;
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    getServerOption();
    try {
      if (props.data) {
        const result = await ServerLinksApi.getServerLink(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        form.level = 0;
        form.parentId = 0;
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
