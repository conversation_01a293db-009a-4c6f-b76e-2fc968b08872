<template>
  <ele-drawer
    form
    :size="'70%'"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="90px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="服务商名称" prop="vendorName">
              <el-input
                v-model.trim="queryParams.vendorName"
                placeholder="请输入服务商名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="状态"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="openEdit(null)"
          >
            添加
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.COMMON_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="danger" @click="removeBatch(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <template #footer>
      <el-button @click="cancle">关 闭</el-button>
    </template>
  </ele-drawer>
  <VendorCheck v-model="showEdit" :data="serverId" @done="reload" />
</template>
<script setup lang="ts">
  import { useFormData } from '@/utils/use-form-data';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import * as ServerVendorsApi from '@/api/cux/vendors/server';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import VendorCheck from './VendorCheck.vue';
  import { dateFormatter } from '@/utils/formatTime';
  import download from '@/utils/download';

  /** 活动列 表单 */
  defineOptions({ name: 'ServerVendors' });
  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  const serverId = ref(-1);

  /** 提交状态 */
  const loading = ref(false);
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    serverId: undefined,
    vendorId: undefined,
    vendorName: undefined,
    status: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'vendorName',
      label: '服务商名称',
      align: 'center',
      minWidth: 110
    },
    // {
    //   prop: 'status',
    //   label: '状态',
    //   align: 'center',
    //   minWidth: 110,
    //   slot: 'status'
    // },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ServerVendorsApi.getServerVendorsPage({
      ...where,
      ...filters,
      ...pages,
      serverId: props.data.id
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await ServerVendorsApi.deleteServerVendors(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await ServerVendorsApi.exportServerVendors(queryParams);
      download.excel(data, '服务商分配列.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      serverId.value = props.data.id;
      queryParams.serverId = props.data.id;
    } finally {
      loading.value = false;
    }
  };
</script>
