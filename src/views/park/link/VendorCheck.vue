<template>
  <ele-modal
    :width="740"
    title="分配服务商"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    destroy-on-close
    :model-value="modelValue"
    v-model="visible"
    @open="handleOpen"
  >
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      row-click-checked
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :empty-props="false"
    />
    <template #footer>
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref } from 'vue';
  import { dateFormatter } from '@/utils/formatTime';
  import * as VendorsApi from '@/api/cux/vendors';

  const emit = defineEmits(['done']);

  const props = defineProps({
    data: Number
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'vendorName',
      label: '服务商',
      minWidth: 100
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      width: 180,
      formatter: dateFormatter
    }
  ]);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return VendorsApi.getVendorsAssign({
      ...where,
      ...filters,
      ...pages,
      serverId: props.data
    });
  };
  /** 提交状态 */
  const loading = ref(false);

  const cancle = () => {
    visible.value = false;
  };
  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    try {
      selections.value.forEach((item) => {
        item.serverId = props.data;
        item.vendorId = item.id;
        item.id = undefined;
      });
      await VendorsApi.assignVendors(selections.value);
      emit('done');
      cancle();
    } finally {
      loading.value = false;
    }
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      selections.value = [];
    } finally {
      loading.value = false;
    }
  };
</script>
