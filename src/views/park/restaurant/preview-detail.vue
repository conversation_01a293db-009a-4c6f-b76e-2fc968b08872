<template>
  <ele-modal
    :width="1000"
    v-model="visible"
    position="center"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ padding: '0 8px 8px 8px' }"
    @open="handleOpen"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="modal-header">
      <h3 class="title">{{ form.title }}</h3>
      <p class="date">{{ formatDate(new Date(), 'YYYY-MM-DD') }}</p>
    </div>
    <el-divider></el-divider>
    <el-scrollbar height="420">
      <div v-html="form.detail"></div>
    </el-scrollbar>
  </ele-modal>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate } from '@/utils/formatTime';
  import * as RestaurantApi from '@/api/cux/restaurant';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    detail: undefined,
    id: undefined,
    title: undefined,
    createTime: undefined
  });

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    resetFields();
    const result = await RestaurantApi.getRestaurant(props.data.id);
    assignFields(result);
  };
</script>

<style scoped>
  .modal-header {
    text-align: center; /* 使标题和时间标签居中 */
  }
  .title {
    margin: 0;
    font-size: 24px; /* 增加标题的字体大小 */
  }
  .date {
    margin: 0;
    color: #909399; /* 修改时间标签的字体颜色为更浅的颜色 */
  }
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* 设置标签之间的间距 */
  }
  .tag-item {
    margin-bottom: 4px; /* 可选：设置标签底部间距 */
  }
</style>
