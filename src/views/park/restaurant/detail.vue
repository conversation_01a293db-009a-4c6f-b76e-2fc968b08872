<template>
  <ele-drawer
    form
    :size="'60%'"
    v-model="visible"
    title="食堂菜单维护"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="标题" prop="title">
        <el-input clearable v-model="form.title" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <image-upload
          :limit="1"
          v-model="form.icon"
          :fileLimit="10"
          :item-style="{ width: '120px', height: '120px', margin: 0 }"
          :button-style="{ width: '120px', height: '120px', margin: 0 }"
        />
      </el-form-item>
      <el-row :gutter="8">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="form.sort" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="详细描述" prop="detail">
        <tinymce-editor ref="editorRef" :init="config" v-model="form.detail" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import * as RestaurantApi from '@/api/cux/restaurant';
  import { useFormData } from '@/utils/use-form-data';
  import ImageUpload from '@/components/ImageUpload/index.vue';
  const emit = defineEmits(['done']);

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });
  /** 表单实例 */
  const formRef = ref(null);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  const disabled = ref(false);
  const editorRef = ref(null);
  /** 编辑器配置 */
  const config = ref({
    height: 600
  });
  const loading = ref(false);
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    title: undefined,
    status: undefined,
    icon: undefined,
    detail: undefined,
    sort: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    icon: [{ required: true, message: '图标不能为空', trigger: 'blur' }],
    sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
    detail: [{ required: true, message: '详细描述不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗
  /** 保存编辑 */
  const save = async () => {
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await RestaurantApi.createRestaurant(form);
        message.success(t('common.createSuccess'));
      } else {
        await RestaurantApi.updateRestaurant(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      emit('done');
    } finally {
      loading.value = false;
    }
  };
  /** 是否是修改 */
  const isUpdate = ref(false);
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      resetFields();
      if (props.data) {
        const result = await RestaurantApi.getRestaurant(props.data.id);
        assignFields(result);
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } finally {
      loading.value = false;
    }
  };
  onMounted(async () => {
    const flag = await RestaurantApi.checkRestaurantEditFlag();
    disabled.value = !flag;
  });
</script>
