<template>
  <ele-page flex-table>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            v-permission="['park:restaurant:create']"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            v-permission="['park:restaurant:export']"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #icon="{ row }">
          <el-image :src="row.icon" class="h-45px w-45px mr-10px rounded" />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            v-permission="['park:restaurant:update']"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['park:restaurant:query']"
            direction="vertical"
          />
          <el-link
            v-permission="['park:restaurant:query']"
            type="primary"
            :underline="false"
            @click="openPreDetail(row)"
          >
            预览
          </el-link>
          <el-divider
            v-permission="['park:restaurant:delete']"
            direction="vertical"
          />
          <el-link
            type="danger"
            v-permission="['park:restaurant:delete']"
            :underline="false"
            @click="removeBatch(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 详细弹窗 -->
    <detail v-model="showEdit" :data="current" @done="reload" />
    <preview-detail v-model="showPreDetail" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';
  import Detail from './detail.vue';
  import PreviewDetail from './preview-detail.vue';

  import * as RestaurantApi from '@/api/cux/restaurant';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';

  defineOptions({ name: 'CuxRestaurantIndex' });
  const message = useMessage(); // 消息弹窗

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'title',
      label: '标题',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'icon',
      label: '图标',
      align: 'center',
      minWidth: 110,
      slot: 'icon'
    },
    {
      prop: 'sort',
      label: '排序',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 80,
      slot: 'status'
    },
    {
      prop: 'createTime',
      label: '创建日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 是否显示编辑弹窗 */
  const showDetail = ref(false);

  const showPreDetail = ref(false);
  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return RestaurantApi.getRestaurantPage({ ...where, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const openDetail = (row) => {
    current.value = row ?? null;
    showDetail.value = true;
  };
  const openPreDetail = (row) => {
    current.value = row ?? null;
    showPreDetail.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await ServerApi.deleteDemand(row.id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };
</script>
