<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <expert-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        :export-config="{ fileName: '专家列表' }"
        cache-key="cuxExpertTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            v-permission="['cux:expert:create']"
            @click="openEdit()"
          >
            新增专家
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            v-permission="['cux:expert:export']"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #avatar="{ row }">
          <el-image :src="row.avatar" class="h-38px w-38px mr-10px rounded" />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #sex="{ row }">
          <dict-data
            :code="DICT_TYPE.SYSTEM_USER_SEX"
            type="tag"
            :model-value="row.sex"
          />
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            v-permission="['cux:expert:update']"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['cux:expert:query']"
            direction="vertical"
          />
          <el-link
            v-permission="['cux:expert:query']"
            type="primary"
            :underline="false"
            @click="openDetail(row)"
          >
            详细
          </el-link>
          <el-divider
            v-permission="['cux:expert:delete']"
            direction="vertical"
          />
          <el-link
            type="danger"
            v-permission="['cux:expert:delete']"
            :underline="false"
            @click="removeBatch(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <expert-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 详细弹窗 -->
    <expert-detail v-model="showDetail" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import {
    PlusOutlined,
    DeleteOutlined,
    DownloadOutlined
  } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';

  import { EleMessage } from 'ele-admin-plus/es';
  import ExpertSearch from './components/expert-search.vue';
  import ExpertEdit from './components/expert-edit.vue';
  import ExpertDetail from './components/expert-detail.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';

  import * as ExpertApi from '@/api/cux/expert';
  import { dateFormatter } from '@/utils/formatTime';

  defineOptions({ name: 'CuxExpert' });
  const message = useMessage(); // 消息弹窗

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '专家名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'email',
      label: '邮箱',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'mobile',
      label: '手机号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'avatar',
      label: '照片',
      align: 'center',
      minWidth: 110,
      slot: 'avatar'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 80,
      slot: 'status'
    },
    {
      prop: 'sex',
      label: '性别',
      align: 'center',
      minWidth: 80,
      slot: 'sex'
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建日期',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 是否显示编辑弹窗 */
  const showDetail = ref(false);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return ExpertApi.getExpertPage({ ...where, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const openDetail = (row) => {
    current.value = row ?? null;
    showDetail.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await DemandApi.deleteExpert(row.id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading('请求中..');
    tableRef.value?.fetch?.(({ where }) => {
      ExpertApi.exportExpert(where)
        .then((data) => {
          loading.close();
          download.excel(data, '专家列表.xls');
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
