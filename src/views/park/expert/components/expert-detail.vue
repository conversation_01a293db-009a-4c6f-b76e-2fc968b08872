<!-- 专家详情弹窗 -->
<template>
  <ele-modal
    :width="1000"
    v-model="visible"
    position="center"
    title="专家详情"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ padding: '0 8px 8px 8px' }"
    @open="handleOpen"
    :show-close="false"
  >
    <div class="expert-detail">
      <!-- 头像和基本信息 -->
      <div class="header">
        <el-avatar :size="100" :src="form.avatar" />
        <div class="info" style="margin-left: 20px">
          <div class="name-status">
            <h2>姓名: {{ form.name }}</h2>
            <dict-data
              :code="DICT_TYPE.COMMON_STATUS"
              type="tag"
              :model-value="form.status"
            />
          </div>
          <div class="contact-info">
            <p><strong>邮箱：</strong> {{ form.email }}</p>
            <p
              ><strong>性别：</strong>
              <dict-data
                :code="DICT_TYPE.SYSTEM_USER_SEX"
                type="text"
                :model-value="form.sex"
              />
            </p>
            <p><strong>手机：</strong> {{ form.mobile }}</p>
          </div>
        </div>
      </div>

      <!-- 标签 -->
      <div class="section">
        <h3 class="section-title">标签：</h3>
        <div class="tag-container">
          <el-tag
            v-for="(item, index) in form.label"
            :key="index"
            class="tag-item"
            >{{ item.trim() }}</el-tag
          >
        </div>
      </div>
      <!-- 详细描述 -->
      <el-scrollbar height="300">
        <div v-html="form.detail"></div>
      </el-scrollbar>
    </div>
    <template #footer>
      <el-button @click="handleCancel">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, nextTick } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import * as ExpertApi from '@/api/cux/expert';
  import { DICT_TYPE } from '@/utils/dict';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const editorRef = ref(null);
  /** 编辑器配置 */
  const config = ref({
    height: 380
  });
  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    name: '',
    email: '',
    mobile: '',
    avatar: '',
    status: void 0,
    sex: '',
    label: '',
    detail: '',
    remark: ''
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    resetFields();
    if (props.data) {
      const res = await ExpertApi.getExpert(props.data.id);
      res.label = res.label.split(',');
      assignFields(res);
      isUpdate.value = true;
    } else {
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>

<style scoped>
  .expert-detail {
    padding: 20px;
  }

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-right: 20px;
  }

  .info {
    flex: 1;
  }

  .name-status {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .name-status h2 {
    margin-right: 10px;
  }

  .status-tag {
    margin-left: 10px;
  }

  .contact-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .contact-info p {
    margin: 0;
    flex: 1;
  }

  .section {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
  }

  .section-title {
    margin: 0 10px 0 0; /* 调整标题和标签之间的间距 */
  }
  .detail-content {
    white-space: pre-wrap; /* 保留空白符并自动换行 */
    word-break: break-all; /* 防止长单词溢出 */
  }
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    flex: 1; /* 使标签容器占据剩余空间 */
  }

  .tag-item {
    margin-bottom: 4px;
  }
</style>
