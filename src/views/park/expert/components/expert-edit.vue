<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    form
    :size="'50%'"
    v-model="visible"
    :title="isUpdate ? '修改' : '新增'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="专家名称" prop="name">
        <el-input clearable v-model="form.name" placeholder="请输入专家名称" />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="邮箱" prop="email">
            <el-input clearable v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="联系方式" prop="mobile">
            <el-input
              clearable
              v-model="form.email"
              placeholder="请输入手机号码"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="照片" prop="avatar">
        <UploadImg
          v-model="form.avatar"
          :limit="1"
          height="64px"
          width="64px"
        />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="form.sex">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="标签" prop="label">
        <ele-edit-tag
          v-model="form.label"
          size="default"
          type="primary"
          effect="light"
          :tooltip-props="{ effect: 'danger' }"
          :readonly="false"
          :disabled="false"
        />
      </el-form-item>
      <el-form-item label="需求详细" prop="detail">
        <tinymce-editor ref="editorRef" :init="config" v-model="form.detail" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          clearable
          v-model="form.remark"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';

  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as ExpertApi from '@/api/cux/expert';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    name: '',
    email: '',
    mobile: '',
    avatar: '',
    status: void 0,
    sex: '',
    label: '',
    detail: '',
    remark: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [{ required: true, message: '专家姓名不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '需求状态不能为空', trigger: 'blur' }],
    sex: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
    detail: [{ required: true, message: '专家详细不能为空', trigger: 'blur' }]
  });

  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? ExpertApi.updateExpert
        : ExpertApi.createExpert;
      try {
        let requst = { ...form };
        requst.label = requst.label.join(', ');
        await saveOrUpdate(requst);
        EleMessage.success('修改成功');
        cancle();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      const res = await ExpertApi.getExpert(props.data.id)
      res.label = res.label
        .split(',');
      assignFields(res);
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
