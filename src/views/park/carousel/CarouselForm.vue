<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="轮播图地址" prop="imageUrl">
        <image-upload
          :limit="1"
          v-model="form.imageUrl"
          :fileLimit="10"
          :item-style="{ width: '160px', height: '160px', margin: 0 }"
          :button-style="{ width: '160px', height: '160px', margin: 0 }"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.PARK_CAROUSEL_TYPE)"
            :key="dict.value"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="链接信息" prop="link">
        <el-input v-model="form.link" placeholder="请输入链接信息" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          :controls="false"
          v-model="form.sort"
          placeholder="请输入排序"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import * as CarouselApi from '@/api/cux/carousel';
  import { useFormData } from '@/utils/use-form-data';
  import ImageUpload from '@/components/ImageUpload/index.vue';

  /** 首页轮播图 表单 */
  defineOptions({ name: 'CarouselForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    imageUrl: undefined,
    status: undefined,
    type: undefined,
    link: undefined,
    sort: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    imageUrl: [
      {
        required: true,
        message: '轮播图地址不能为空',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '类型不能为空',
        trigger: 'blur'
      }
    ],
    status: [
      {
        required: true,
        message: '状态不能为空',
        trigger: 'blur'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await CarouselApi.createCarousel(form);
        message.success(t('common.createSuccess'));
      } else {
        await CarouselApi.updateCarousel(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await CarouselApi.getCarousel(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
