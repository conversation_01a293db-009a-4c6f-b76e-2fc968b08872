<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="类型" prop="type">
              <el-select
                v-model="queryParams.type"
                placeholder="请选择类型"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.PARK_CAROUSEL_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
        </template>
        <template #type="{ row }">
          <dict-data
            :code="DICT_TYPE.PARK_CAROUSEL_TYPE"
            type="tag"
            :model-value="row.type"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #imageUrl="{ row }">
          <el-image
            class="h-60px w-60px"
            lazy
            :src="row.imageUrl"
            :preview-src-list="[row.imageUrl]"
            preview-teleported
            fit="cover"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="openEdit(row)">
            编辑
          </el-link>
          <el-divider direction="vertical" />
          <el-link :underline="false" type="primary" @click="removeBatch(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <CarouselForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as CarouselApi from '@/api/cux/carousel';
  import CarouselForm from './CarouselForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 首页轮播图 列表 */
  defineOptions({ name: 'CarouselIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    imageUrl: undefined,
    status: undefined,
    link: undefined,
    type: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'imageUrl',
      label: '轮播图地址',
      align: 'center',
      minWidth: 110,
      slot: 'imageUrl'
    },
    {
      prop: 'type',
      label: '类型',
      align: 'center',
      minWidth: 110,
      slot: 'type'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'link',
      label: '链接信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'sort',
      label: '排序',
      align: 'center',
      minWidth: 60
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return CarouselApi.getCarouselPage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await CarouselApi.deleteCarousel(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
