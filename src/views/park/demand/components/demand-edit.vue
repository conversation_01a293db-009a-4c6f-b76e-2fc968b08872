<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    form
    :size="'50%'"
    v-model="visible"
    :title="isUpdate ? '修改' : '新增'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="需求描述" prop="title">
        <el-input clearable v-model="form.title" placeholder="请输入需求描述" />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="需求时间" prop="demandDate">
            <el-date-picker
              v-model="form.demandDate"
              clearable
              placeholder="请选择需求时间"
              type="date"
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="需求状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="需求标签" prop="label">
        <ele-edit-tag
          v-model="form.label"
          size="default"
          type="primary"
          effect="light"
          :tooltip-props="{ effect: 'danger' }"
          :readonly="false"
          :disabled="false"
        />
      </el-form-item>
      <el-form-item label="介绍图" prop="imageUrl">
        <image-upload
          :limit="1"
          v-model="form.imageUrl"
          :fileLimit="10"
          :item-style="{ width: '80px', height: '80px', margin: 0 }"
          :button-style="{ width: '80px', height: '80px', margin: 0 }"
        />
      </el-form-item>
      <el-form-item label="需求简介" prop="demandIntro">
        <el-input
          clearable
          v-model="form.demandIntro"
          placeholder="请输入需求简介"
        />
      </el-form-item>
      <el-form-item label="需求详细" prop="detail">
        <tinymce-editor ref="editorRef" :init="config" v-model="form.detail" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          clearable
          v-model="form.remark"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as DemandApi from '@/api/cux/demand';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import ImageUpload from '@/components/ImageUpload/index.vue';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const editorRef = ref(null);
  /** 编辑器配置 */
  const config = ref({
    height: 380
  });
  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    demandDate: undefined,
    status: '',
    label: '',
    detail: '',
    remark: '',
    demandIntro: undefined,
    imageUrl: undefined
  });

  /** 表单验证规则 */
  const rules = reactive({
    demandIntro: [
      { required: true, message: '需求简介不能为空', trigger: 'blur' }
    ],
    imageUrl: [{ required: true, message: '介绍图不能为空', trigger: 'blur' }],
    title: [{ required: true, message: '需求描述不能为空', trigger: 'blur' }],
    demandDate: [
      { required: true, message: '需求日期不能为空', trigger: 'blur' }
    ],
    status: [{ required: true, message: '需求状态不能为空', trigger: 'blur' }],
    label: [{ required: true, message: '需求标签不能为空', trigger: 'blur' }],
    detail: [{ required: true, message: '需求详细不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value
        ? DemandApi.updateDemand
        : DemandApi.createDemand;
      try {
        let requst = { ...form };
        requst.label = requst.label.join(', ');
        await saveOrUpdate(requst);
        EleMessage.success('修改成功');
        handleCancel();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      const res = await DemandApi.getDemand(props.data.id);
      res.label = res.label.split(',');
      assignFields(res);
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
