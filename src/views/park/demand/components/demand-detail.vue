<template>
  <ele-modal
    :width="1000"
    v-model="visible"
    position="center"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ padding: '0 8px 8px 8px' }"
    @open="handleOpen"
    destroy-on-close
    :show-close="false"
    :close-on-click-modal="false"
  >
    <div class="modal-header">
      <h3 class="title">{{ form.title }}</h3>
      <p class="date"
        >需求日期: {{ formatDate(form.demandDate, 'YYYY-MM-DD') }}</p
      >
    </div>
    <el-divider></el-divider>
    <el-scrollbar height="420">
      <div v-html="form.detail"></div>
    </el-scrollbar>
    <div class="tag-container">
      需求标签：
      <el-tag
        v-for="(item, index) in form.label"
        :key="index"
        class="tag-item"
        >{{ item }}</el-tag
      >
    </div>
    <template #footer>
      <el-button @click="handleCancel">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import * as DemandApi from '@/api/cux/demand';
  import { formatDate } from '@/utils/formatTime';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const editorRef = ref(null);
  /** 编辑器配置 */
  const config = ref({
    height: 380
  });
  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    demandDate: undefined,
    status: '',
    label: '',
    detail: '',
    remark: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    title: [{ required: true, message: '需求描述不能为空', trigger: 'blur' }],
    demandDate: [
      { required: true, message: '需求日期不能为空', trigger: 'blur' }
    ],
    status: [{ required: true, message: '需求状态不能为空', trigger: 'blur' }],
    label: [{ required: true, message: '需求标签不能为空', trigger: 'blur' }],
    detail: [{ required: true, message: '需求详细不能为空', trigger: 'blur' }]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    resetFields();
    if (props.data) {
      const res = await DemandApi.getDemand(props.data.id);
      res.label = res.label.split(',');
      assignFields(res);
      isUpdate.value = true;
    } else {
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>

<style scoped>
  .modal-header {
    text-align: center; /* 使标题和时间标签居中 */
  }
  .title {
    margin: 0;
    font-size: 24px; /* 增加标题的字体大小 */
  }
  .date {
    margin: 0;
    color: #909399; /* 修改时间标签的字体颜色为更浅的颜色 */
  }
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* 设置标签之间的间距 */
  }
  .tag-item {
    margin-bottom: 4px; /* 可选：设置标签底部间距 */
  }
</style>
