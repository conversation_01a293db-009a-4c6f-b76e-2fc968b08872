<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <demand-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        :export-config="{ fileName: '需求列表' }"
        cache-key="cuxDemandTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            v-permission="['cux:demand:create']"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            class="ele-btn-icon"
            :icon="DownloadOutlined"
            v-permission="['cux:demand:export']"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            v-permission="['cux:demand:update']"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-permission="['cux:demand:query']"
            direction="vertical"
          />
          <el-link
            v-permission="['cux:demand:query']"
            type="primary"
            :underline="false"
            @click="openDetail(row)"
          >
            详细
          </el-link>
          <el-divider
            v-permission="['cux:demand:delete']"
            direction="vertical"
          />
          <el-link
            type="danger"
            v-permission="['cux:demand:delete']"
            :underline="false"
            @click="removeBatch(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <demand-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 详细弹窗 -->
    <demand-detail v-model="showDetail" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';
  import { EleMessage } from 'ele-admin-plus/es';
  import DemandSearch from './components/demand-search.vue';
  import DemandEdit from './components/demand-edit.vue';
  import DemandDetail from './components/demand-detail.vue';
  import * as DemandApi from '@/api/cux/demand';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';

  defineOptions({ name: 'CuxDemand' });
  const message = useMessage(); // 消息弹窗

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'title',
      label: '需求描述',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'demandDate',
      label: '需求日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 80,
      slot: 'status'
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 是否显示编辑弹窗 */
  const showDetail = ref(false);
  /** 表格数据源 */
  const datasource = ({ pages, where }) => {
    return DemandApi.getDemandPage({ ...where, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const openDetail = (row) => {
    current.value = row ?? null;
    showDetail.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await DemandApi.deleteDemand(row.id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };

  /** 导出数据 */
  const exportData = () => {
    const loading = EleMessage.loading('请求中..');
    tableRef.value?.fetch?.(({ where }) => {
      DemandApi.exportDemand(where)
        .then((data) => {
          loading.close();
          download.excel(data, '需求列表.xls');
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };
</script>
