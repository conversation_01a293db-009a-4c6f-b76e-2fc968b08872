<template>
  <el-dialog v-model="refund" title="保证金详情" width="500">
    <el-form ref="fromRef" :inline="true" label-width="100px">
      <ele-card header="合同信息">
        <el-form-item label="合同编号"></el-form-item>
        <el-form-item label="合同名称"></el-form-item>
        <el-form-item label="客户名称"></el-form-item>
        <el-form-item label="合同签订日期"></el-form-item>
        <el-form-item label="客户类型"></el-form-item>
        <el-form-item label="签订渠道"></el-form-item>
        <el-form-item label="经办人"></el-form-item>
        <el-form-item label="保证金"></el-form-item>
      </ele-card>
      <ele-card header="收款/退款记录">
        <el-table :data="[]">
          <el-table-column label="收款/退款日期" prop=""></el-table-column>
          <el-table-column label="收款/退款金额" prop=""></el-table-column>
          <el-table-column label="操作类型" prop=""></el-table-column>
          <el-table-column label="备注" prop=""></el-table-column>
          <el-table-column label="附件" prop="">
            <template #default="">
              <el-button type="primary" link>查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </ele-card>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button type="primary">确认</el-button>
        <el-button>关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
  .frequency-box {
    display: flex;
    align-items: center;
  }
  .flex-box {
    display: flex;
    align-items: center;
  }
</style>
