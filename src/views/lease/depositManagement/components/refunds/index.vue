<template>
  <el-dialog v-model="refund" title="保证金退款" width="500">
    <el-form ref="fromRef" :inline="true" label-width="100px">
      <ele-card header="合同信息">
        <el-form-item label="合同编号"></el-form-item>
        <el-form-item label="合同名称"></el-form-item>
        <el-form-item label="客户名称"></el-form-item>
        <el-form-item label="合同签订日期"></el-form-item>
        <el-form-item label="客户类型"></el-form-item>
        <el-form-item label="签订渠道"></el-form-item>
        <el-form-item label="经办人"></el-form-item>
        <el-form-item label="保证金"></el-form-item>
      </ele-card>
      <ele-card header="欠款信息">
        <el-form-item label="滞纳金"></el-form-item>
        <el-form-item label="违约金"></el-form-item>
        <el-form-item label="扣除欠款">
          <el-input type="number">
            <template #append>全部</template>
          </el-input>
        </el-form-item>
      </ele-card>
      <ele-card header="退款信息">
        <el-form-item label="退款金额">
          <el-input disabled></el-input>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="开始时间"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="开始时间"
          />
        </el-form-item>
        <el-form-item label="退款方式">
          <el-select placeholder="请选择">
            <el-option label="银行转账" :value="1"></el-option>
            <el-option label="现金" :value="2"></el-option>
            <el-option label="支票" :value="3"></el-option>
            <el-option label="汇票" :value="4"></el-option>
            <el-option label="其它" :value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="退款账号">
          <el-input placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" rows="5" placeholder="请输入"></el-input>
        </el-form-item>
      </ele-card>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button type="primary">确认</el-button>
        <el-button>关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
  .frequency-box {
    display: flex;
    align-items: center;
  }
  .flex-box {
    display: flex;
    align-items: center;
  }
</style>
