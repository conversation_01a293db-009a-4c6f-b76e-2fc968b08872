import { defineComponent, reactive, toRefs, ref } from 'vue';
import { useRouter } from 'vue-router';
import { dateFormatter } from '@/utils/formatTime';
export default defineComponent({
  setup() {
    //获取表单form元素
    const ruleForm = ref(null);
    const router = useRouter();
    const _that = reactive({
      columns: [
        {
          prop: 'name',
          label: '账单编号',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '合同编号',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '客户名称',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '收款金额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '滞纳金',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '缴费日期',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '已收金额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '应退金额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'createTime',
          label: '状态',
          width: 140,
          align: 'center',
          slot: 'status'
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 180,
          align: 'center',
          slot: 'action',
          hideInPrint: true
        }
      ]
    });
    //提醒设置
    const getRemindSetting = (row) => {};
    return {
      ...toRefs(_that),
      getRemindSetting
    };
  }
});
