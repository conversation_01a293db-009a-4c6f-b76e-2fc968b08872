<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="申请原因" prop="reqReason">
              <el-input
                v-model.trim="queryParams.reqReason"
                placeholder="请输入申请原因"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="招待状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择招待状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_HOSPITALITY_AMT_APPROVE_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发放状态" prop="distributionStatus">
              <el-select
                v-model="queryParams.distributionStatus"
                placeholder="请选择招待金发放状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_HOSPITALITY_AMT_GRANT_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发放金额" prop="distributionAmount">
              <el-input
                v-model.trim="queryParams.distributionAmount"
                placeholder="请输入发放金额"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="核减状态" prop="reductionStatus">
              <el-select
                v-model="queryParams.reductionStatus"
                placeholder="请选择招待金核减状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_HOSPITALITY_AMT_CUT_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:public-hospitality:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:public-hospitality:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_HOSPITALITY_AMT_APPROVE_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #distributionStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_HOSPITALITY_AMT_GRANT_STATUS"
            :model-value="row.distributionStatus"
          />
        </template>
        <template #reductionStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_HOSPITALITY_AMT_CUT_STATUS"
            :model-value="row.reductionStatus"
          />
        </template>
        <template #action="{ row }">
          <template v-if="row.status === 0">
            <el-link
              :underline="false"
              type="primary"
              @click="openEdit(row)"
              v-permission="['lease:public-hospitality:update']"
            >
              编辑
            </el-link>
            <el-divider
              direction="vertical"
              v-permission="['lease:public-hospitality:update']"
            />
            <el-link
              :underline="false"
              type="primary"
              @click="submit(row)"
              v-permission="['lease:public-hospitality:update']"
            >
              提交
            </el-link>
            <el-divider
              direction="vertical"
              v-permission="['lease:public-hospitality:delete']"
            />
            <el-link
              :underline="false"
              type="primary"
              @click="removeBatch(row)"
              v-permission="['lease:public-hospitality:delete']"
            >
              删除
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <PublicHospitalityForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as PublicHospitalityApi from '@/api/lease/publichospitality';
  import PublicHospitalityForm from './PublicHospitalityForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { ElLoading } from 'element-plus';

  /** 公共招待申请 列表 */
  defineOptions({ name: 'PublicHospitalityIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    reqReason: undefined,
    peopleNumber: undefined,
    allAmount: undefined,
    hospitalityStandards: undefined,
    status: undefined,
    distributionStatus: undefined,
    distributionAmount: undefined,
    distributionMessage: undefined,
    distributionTime: [],
    reductionStatus: undefined,
    reductionAmount: undefined,
    reductionMessage: undefined,
    reductionTime: [],
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: [],
    companyKey: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'reqReason',
      label: '申请原因',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'peopleNumber',
      label: '招待人数',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'allAmount',
      label: '招待金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'hospitalityStandards',
      label: '招待标准(元/人)',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'distributionStatus',
      label: '招待金发放状态',
      align: 'center',
      minWidth: 110,
      slot: 'distributionStatus'
    },
    {
      prop: 'distributionAmount',
      label: '发放金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'distributionMessage',
      label: '发放消息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'distributionTime',
      label: '发放时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'reductionStatus',
      label: '招待金核减状态',
      align: 'center',
      minWidth: 110,
      slot: 'reductionStatus'
    },
    {
      prop: 'reductionAmount',
      label: '核减金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'reductionMessage',
      label: '核减消息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'reductionTime',
      label: '核减时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return PublicHospitalityApi.getPublicHospitalityPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 提交公共招待申请 */
  const submit = async (row) => {
    // 提交的二次确认
    await message.confirm('确认要提交该公共招待申请吗？', '提示');
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在提交中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      // 发起提交
      await PublicHospitalityApi.submitPublicHospitality(row);
      message.success('提交成功');
      // 刷新列表
      reload();
      // 关闭加载状态
      loadingInstance.close();
    } catch (error) {
      // 用户取消或提交失败时不显示错误
      if (error !== 'cancel') {
        console.error('提交失败:', error);
      }
      // 关闭加载状态
      loadingInstance.close();
    }
  };

  const removeBatch = async (row) => {
    // 删除的二次确认
    await message.delConfirm();
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在删除中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      // 发起删除
      await PublicHospitalityApi.deletePublicHospitality(row.id);
      message.success(t('common.delSuccess'));
      // 关闭加载状态
      loadingInstance.close();
      // 刷新列表
      reload();
    } catch (error) {
      // 用户取消或删除失败时不显示错误
      if (error !== 'cancel') {
        console.error('删除失败:', error);
      }
      // 关闭加载状态
      loadingInstance.close();
    }
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data =
        await PublicHospitalityApi.exportPublicHospitality(queryParams);
      download.excel(data.data, '公共招待申请.xls');
    } catch (error) {
      // 用户取消或导出失败时不显示错误
      if (error !== 'cancel') {
        console.error('导出失败:', error);
      }
    } finally {
      exportLoading.value = false;
    }
  };
</script>
