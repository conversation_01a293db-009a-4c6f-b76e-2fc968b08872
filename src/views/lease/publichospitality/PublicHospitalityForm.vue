<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="申请原因" prop="reqReason">
        <el-input v-model="form.reqReason" placeholder="请输入申请原因" />
      </el-form-item>
      <el-form-item label="招待人数" prop="peopleNumber">
        <el-input
          v-model="form.peopleNumber"
          placeholder="请输入招待人数"
          @input="handlePeopleNumberChange"
        />
      </el-form-item>
      <el-form-item label="招待金额" prop="allAmount">
        <el-input v-model="form.allAmount" readonly />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import * as PublicHospitalityApi from '@/api/lease/publichospitality';
  import { useFormData } from '@/utils/use-form-data';
  import { useI18n } from 'vue-i18n';
  import { useMessage } from '@/hooks/web/useMessage';

  /** 公共招待申请 表单 */
  defineOptions({ name: 'PublicHospitalityForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    reqReason: undefined,
    peopleNumber: undefined,
    allAmount: 0,
    hospitalityStandards: undefined,
    status: undefined,
    distributionStatus: undefined,
    distributionAmount: undefined,
    distributionMessage: undefined,
    distributionTime: undefined,
    reductionStatus: undefined,
    reductionAmount: undefined,
    reductionMessage: undefined,
    reductionTime: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    companyKey: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    reqReason: [
      { required: true, message: '申请原因不能为空', trigger: 'blur' }
    ],
    peopleNumber: [
      { required: true, message: '招待人数不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await PublicHospitalityApi.createPublicHospitality(form);
        message.success(t('common.createSuccess'));
      } else {
        await PublicHospitalityApi.updatePublicHospitality(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await PublicHospitalityApi.getPublicHospitality(
          props.data.id
        );
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      //查询公共招待标准
      form.hospitalityStandards =
        await PublicHospitalityApi.getHospitalityStandards();
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };

  // 招待人数变化时自动计算招待金额
  const handlePeopleNumberChange = () => {
    const num = parseInt(form.peopleNumber, 10);
    form.allAmount = isNaN(num) ? 0 : num * form.hospitalityStandards;
  };
</script>
