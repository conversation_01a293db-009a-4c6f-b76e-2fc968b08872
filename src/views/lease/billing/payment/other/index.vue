<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!-- <role-search @search="reload" /> -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemContractTable"
      >
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { getRolePage, deleteRole, exportRole } from '@/api/system/role';
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '账单编号',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'code',
        label: '客户名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'sort',
        label: '房产名称',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'sort',
        label: '合同名称',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'sort',
        label: '合同编号',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'sort',
        label: '费用类型',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'createTime',
        label: '收款金额',
        width: 140,
        align: 'center',
        formatter: dateFormatter
      },
      {
        prop: 'createTime',
        label: '滞纳金',
        width: 140,
        align: 'center',
        formatter: dateFormatter
      },
      {
        prop: 'createTime',
        label: '缴费日期',
        width: 140,
        align: 'center',
        formatter: dateFormatter
      },
      {
        prop: 'sort',
        label: '已收金额',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'sort',
        label: '应退金额',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'sort',
        label: '已退金额',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'sort',
        label: '账单状态',
        align: 'center',
        minWidth: 50
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getRolePage({ ...where, ...filters, ...pages });
  };
</script>
