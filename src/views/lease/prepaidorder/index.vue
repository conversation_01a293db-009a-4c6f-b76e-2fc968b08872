<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单编号" prop="orderNo">
              <el-input
                v-model.trim="queryParams.orderNo"
                placeholder="请输入账单编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                v-model.trim="queryParams.customerName"
                placeholder="请输入客户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:prepaid-order:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_PREPAID_ORDER_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:prepaid-order:update']"
          >
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <PrepaidOrderForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as PrepaidOrderApi from '@/api/lease/prepaidorder';
  import PrepaidOrderForm from './PrepaidOrderForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';

  /** 预存金订单 列表 */
  defineOptions({ name: 'PrepaidOrderIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    orderNo: undefined,
    prepaidId: undefined,
    customerId: undefined,
    customerCode: undefined,
    customerName: undefined,
    socialCreditCode: undefined,
    type: undefined,
    opType: undefined,
    opSubType: undefined,
    amount: undefined,
    actualAmount: undefined,
    deductionAmount: 0,
    refundAmount: 0,
    relType: undefined,
    relId: undefined,
    relInfo: undefined,
    relStatus: undefined,
    status: undefined,
    startTime: [],
    endTime: [],
    tradeTime: [],
    paymentMethod: undefined,
    paymentAccount: undefined,
    paymentPayee: undefined,
    paymentDetail: undefined,
    isSuccess: undefined,
    thirdOrderNo: undefined,
    callbackMsg: undefined,
    isInvoice: undefined,
    invoiceId: undefined,
    extend: undefined,
    remark: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 20,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'orderNo',
      label: '账单编号',
      align: 'center',
      minWidth: 140
    },
    {
      prop: 'customerName',
      label: '客户名称',
      align: 'center',
      minWidth: 140
    },
    {
      prop: 'paymentMethod',
      label: '收款方式',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'amount',
      label: '应收金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'actualAmount',
      label: '实收金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'deductionAmount',
      label: '扣款金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'refundAmount',
      label: '退款金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'startTime',
      label: '开始时间',
      align: 'center',
      minWidth: 100,
      formatter: dateFormatter
    },
    {
      prop: 'endTime',
      label: '结束时间',
      align: 'center',
      minWidth: 100,
      formatter: dateFormatter
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 100,
      formatter: dateFormatter
    },
    {
      prop: 'status',
      label: '账单状态',
      align: 'center',
      minWidth: 80,
      slot: 'status'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 80,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return PrepaidOrderApi.getPrepaidOrderPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await PrepaidOrderApi.exportPrepaidOrder(queryParams);
      download.excel(data, '预存金订单.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
