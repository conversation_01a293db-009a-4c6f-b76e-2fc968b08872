<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="订单编号" prop="orderNo">
        <el-input v-model="form.orderNo" placeholder="请输入订单编号" />
      </el-form-item>
      <el-form-item label="预存金ID" prop="prepaidId">
        <el-input v-model="form.prepaidId" placeholder="请输入预存金ID" />
      </el-form-item>
      <el-form-item label="客户ID" prop="customerId">
        <el-input v-model="form.customerId" placeholder="请输入客户ID" />
      </el-form-item>
      <el-form-item label="客户编号" prop="customerCode">
        <el-input v-model="form.customerCode" placeholder="请输入客户编号" />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input v-model="form.customerName" placeholder="请输入客户名称" />
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="socialCreditCode">
        <el-input
          v-model="form.socialCreditCode"
          placeholder="请输入统一社会信用代码"
        />
      </el-form-item>
      <el-form-item label="操作类型, 1-增加; 2-减少;" prop="type">
        <el-select
          v-model="form.type"
          placeholder="请选择操作类型, 1-增加; 2-减少;"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作类型, 1-充值; 2-扣款; 3-退款;" prop="opType">
        <el-select
          v-model="form.opType"
          placeholder="请选择操作类型, 1-充值; 2-扣款; 3-退款;"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="操作子类型, 1-充值; 2-物业缴费; 3-合同缴费; 4-合同退款;"
        prop="opSubType"
      >
        <el-select
          v-model="form.opSubType"
          placeholder="请选择操作子类型, 1-充值; 2-物业缴费; 3-合同缴费; 4-合同退款;"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="应收金额" prop="amount">
        <el-input v-model="form.amount" placeholder="请输入应收金额" />
      </el-form-item>
      <el-form-item label="实收金额" prop="actualAmount">
        <el-input v-model="form.actualAmount" placeholder="请输入实收金额" />
      </el-form-item>
      <el-form-item label="关联类型" prop="relType">
        <el-select v-model="form.relType" placeholder="请选择关联类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="关联标识" prop="relId">
        <el-input v-model="form.relId" placeholder="请输入关联标识" />
      </el-form-item>
      <el-form-item label="关联信息" prop="relInfo">
        <el-input v-model="form.relInfo" placeholder="请输入关联信息" />
      </el-form-item>
      <el-form-item label="关联状态" prop="relStatus">
        <el-radio-group v-model="form.relStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="状态, 1-待确认; 2-处理中; 3-成功; 4-失败;"
        prop="status"
      >
        <el-radio-group v-model="form.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          type="date"
          value-format="x"
          placeholder="选择开始时间"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="date"
          value-format="x"
          placeholder="选择结束时间"
        />
      </el-form-item>
      <el-form-item label="交易时间" prop="tradeTime">
        <el-date-picker
          v-model="form.tradeTime"
          type="date"
          value-format="x"
          placeholder="选择交易时间"
        />
      </el-form-item>
      <el-form-item label="收款方式" prop="paymentMethod">
        <el-input v-model="form.paymentMethod" placeholder="请输入收款方式" />
      </el-form-item>
      <el-form-item label="收款账号" prop="paymentAccount">
        <el-input v-model="form.paymentAccount" placeholder="请输入收款账号" />
      </el-form-item>
      <el-form-item label="收款人" prop="paymentPayee">
        <el-input v-model="form.paymentPayee" placeholder="请输入收款人" />
      </el-form-item>
      <el-form-item label="收款明细" prop="paymentDetail">
        <el-input v-model="form.paymentDetail" placeholder="请输入收款明细" />
      </el-form-item>
      <el-form-item label="是否成功" prop="isSuccess">
        <el-input v-model="form.isSuccess" placeholder="请输入是否成功" />
      </el-form-item>
      <el-form-item label="三方订单编号" prop="thirdOrderNo">
        <el-input
          v-model="form.thirdOrderNo"
          placeholder="请输入三方订单编号"
        />
      </el-form-item>
      <el-form-item label="回调信息" prop="callbackMsg">
        <el-input v-model="form.callbackMsg" placeholder="请输入回调信息" />
      </el-form-item>
      <el-form-item label="是否开票" prop="isInvoice">
        <el-input v-model="form.isInvoice" placeholder="请输入是否开票" />
      </el-form-item>
      <el-form-item label="开票ID" prop="invoiceId">
        <el-input v-model="form.invoiceId" placeholder="请输入开票ID" />
      </el-form-item>
      <el-form-item label="扩展信息" prop="extend">
        <el-input v-model="form.extend" placeholder="请输入扩展信息" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PrepaidOrderApi from '@/api/lease/prepaidorder';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 预存金订单 表单 */
  defineOptions({ name: 'PrepaidOrderForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    orderNo: undefined,
    prepaidId: undefined,
    customerId: undefined,
    customerCode: undefined,
    customerName: undefined,
    socialCreditCode: undefined,
    type: undefined,
    opType: undefined,
    opSubType: undefined,
    amount: undefined,
    actualAmount: undefined,
    relType: undefined,
    relId: undefined,
    relInfo: undefined,
    relStatus: undefined,
    status: undefined,
    startTime: undefined,
    endTime: undefined,
    tradeTime: undefined,
    paymentMethod: undefined,
    paymentAccount: undefined,
    paymentPayee: undefined,
    paymentDetail: undefined,
    isSuccess: undefined,
    thirdOrderNo: undefined,
    callbackMsg: undefined,
    isInvoice: undefined,
    invoiceId: undefined,
    extend: undefined,
    remark: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    orderNo: [{ required: true, message: '订单编号不能为空', trigger: 'blur' }]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await PrepaidOrderApi.createPrepaidOrder(form);
        message.success(t('common.createSuccess'));
      } else {
        await PrepaidOrderApi.updatePrepaidOrder(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await PrepaidOrderApi.getPrepaidOrder(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
