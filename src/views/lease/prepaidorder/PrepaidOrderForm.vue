<template>
  <ele-modal
    form
    :width="880"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form ref="formRef" :model="form" label-width="80px" v-loading="loading">
      <ele-card header="账户信息">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户编号" prop="accountCode">
              <el-input class="input-200" disabled v-model="form.accountCode" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerId">
              <el-select
                v-model="form.customerId"
                placeholder="请选择"
                disabled
              >
                <el-option
                  v-for="item in customerSimpleList"
                  :key="item.id"
                  :label="item.customerName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="联系人" prop="contactName">
              <el-input class="input-200" disabled v-model="form.contactName" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="联系电话" prop="contactNumber">
              <el-input
                class="input-200"
                disabled
                v-model="form.contactNumber"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户余额" prop="lockAmount">
              <el-input class="input-200" disabled v-model="form.lockAmount" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户状态" prop="billStatus">
              <el-select
                clearable
                v-model="form.billStatus"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getStrDictOptions(
                    DICT_TYPE.CO_PREPAID_BILL_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-input class="input-200" disabled v-model="form.createTime" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建人" prop="createUserName">
              <el-input
                class="input-200"
                disabled
                v-model="form.createUserName"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
      <ele-card header="账单明细">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单编号" prop="orderNo">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.orderNo"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单类型" prop="type">
              <el-select
                clearable
                v-model="form.rechargeInfo.type"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_PREPAID_ORDER_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单状态" prop="status">
              <el-select
                clearable
                v-model="form.rechargeInfo.status"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_PREPAID_ORDER_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="应收金额" prop="amount">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.amount"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="开始时间" prop="startTime">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.startTime"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="结束时间" prop="endTime">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.endTime"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="交易时间" prop="tradeTime">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.tradeTime"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="实收金额" prop="actualAmount">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.actualAmount"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="收款方式" prop="paymentMethod">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.paymentMethod"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="收款账号" prop="paymentAccount">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.paymentAccount"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="收款人" prop="paymentPayee">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.paymentPayee"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="扣款金额" prop="amount">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.deductionAmount"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="确认人" prop="createUserName">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.createUserName"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.createTime"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建人" prop="createUserName">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.createUserName"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="退款金额" prop="amount">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.refundAmount"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="24" :md="12" :sm="12" :xs="24">
            <el-form-item label="收款明细" prop="paymentDetail">
              <el-input
                class="input-200"
                disabled
                v-model="form.rechargeInfo.paymentDetail"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PrepaidInfoApi from '@/api/lease/prepaidinfo';
  import {
    DICT_TYPE,
    getStrDictOptions,
    getIntDictOptions
  } from '@/utils/dict';
  import { useFormData } from '@/utils/use-form-data';
  import dayjs from 'dayjs'; // 引入 dayjs 库

  /** 预存金信息 表单 */
  defineOptions({ name: 'PrepaidInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  // 客户信息列表
  const customerSimpleList = ref([]);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    customerId: undefined,
    accountCode: undefined,
    socialCreditCode: undefined,
    amount: undefined,
    lockAmount: 0,
    rechargeAmount: undefined,
    deductAmount: undefined,
    refundAmount: undefined,
    extend: undefined,
    remark: undefined,
    billStatus: undefined,
    customerName: undefined,
    contactName: undefined,
    contactNumber: undefined,
    createUserName: undefined,
    createTime: [],
    rechargeInfo: {
      orderNo: undefined,
      prepaidId: undefined,
      customerId: undefined,
      customerCode: undefined,
      customerName: undefined,
      socialCreditCode: undefined,
      type: undefined,
      opType: undefined,
      opSubType: undefined,
      amount: undefined,
      actualAmount: undefined,
      refundAmount: 0,
      deductionAmount: 0,
      relType: undefined,
      relId: undefined,
      relInfo: undefined,
      relStatus: undefined,
      status: undefined,
      startTime: [],
      endTime: [],
      tradeTime: [],
      paymentMethod: undefined,
      paymentAccount: undefined,
      paymentPayee: undefined,
      paymentDetail: undefined,
      isSuccess: undefined,
      thirdOrderNo: undefined,
      callbackMsg: undefined,
      isInvoice: undefined,
      invoiceId: undefined,
      extend: undefined,
      remark: undefined,
      createTime: []
    }
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    // 加载公告类型列表
    customerSimpleList.value = await PrepaidInfoApi.getCustomerInfoSimple();
    try {
      if (props.data) {
        const result = await PrepaidInfoApi.getPrepaidDetail(props.data.id);
        // 转换 createTime 格式
        if (result.createTime) {
          result.createTime = dayjs(result.createTime).format(
            'YYYY-MM-DD HH:mm:ss'
          );
        }
        result.rechargeInfo.createTime = dayjs(
          result.rechargeInfo.createTime
        ).format('YYYY-MM-DD HH:mm:ss');
        result.rechargeInfo.startTime = dayjs(
          result.rechargeInfo.startTime
        ).format('YYYY-MM-DD');
        result.rechargeInfo.endTime = dayjs(result.rechargeInfo.endTime).format(
          'YYYY-MM-DD'
        );
        if (result.rechargeInfo.tradeTime) {
          result.rechargeInfo.tradeTime = dayjs(
            result.rechargeInfo.tradeTime
          ).format('YYYY-MM-DD HH:mm:ss');
        }

        assignFields({ ...result });
      } else {
        resetFields();
      }
      title.value = '账单明细';
    } finally {
      loading.value = false;
    }
  };
</script>
