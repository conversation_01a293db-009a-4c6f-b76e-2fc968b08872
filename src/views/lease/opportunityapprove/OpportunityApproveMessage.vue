<template>
  <ele-modal
    form
    :width="860"
    top="40px"
    v-model="visible"
    :title="审批消息"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-row :gutter="8">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="审批动作" prop="approveFlag">
            <el-radio-group v-model="form.approveFlag">
              <el-radio :value="1" label="通过" />
              <el-radio :value="0" label="拒绝" />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="审批意见" prop="approveMessage">
        <el-input
          clearable
          v-model="form.approveMessage"
          placeholder="请输入审批意见"
          maxlength="300"
          show-word-limit
          type="textarea"
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        确认
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import * as OpportunityApproveApi from '@/api/lease/opportunityapprove';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    opportunityId: void 0,
    approveFlag: undefined
  });

  /** 表单验证规则 */
  const rules = reactive({
    approveFlag: [
      { required: true, message: '审批动作不能为空', trigger: 'blur' }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        await OpportunityApproveApi.approveOpportunityApprove({ ...form });
        EleMessage.success('审批成功');
        handleCancel();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      assignFields({
        id: props.data.id,
        opportunityId: props.data.opportunityId
      });
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
