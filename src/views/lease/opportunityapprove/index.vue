<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="审批状态" prop="approveStatus">
              <el-select
                v-model="queryParams.approveStatus"
                placeholder="请选择审批状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_OPPORTUNITY_APPROVE_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="提交时间" prop="submmitTime">
              <el-date-picker
                v-model="queryParams.submmitTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:opportunity-approve:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #approveStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_OPPORTUNITY_APPROVE_STATUS"
            :model-value="row.approveStatus"
          />
        </template>
        <template #source="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE"
            :model-value="row.source"
          />
        </template>
        <template #stage="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BUSINESS_OPPORTUNITY_STAGE"
            :model-value="row.stage"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="approveOpportunity(row)"
            v-permission="['lease:opportunity-approve:approve']"
          >
            审批
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <OpportunityApproveMessage
      v-model="showApprove"
      :data="current"
      @done="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import download from '@/utils/download';
  import * as OpportunityApproveApi from '@/api/lease/opportunityapprove';
  import { useFormData } from '@/utils/use-form-data';
  import { ref } from 'vue';
  import OpportunityApproveMessage from './OpportunityApproveMessage.vue';

  /** 商机审批 列表 */
  defineOptions({ name: 'OpportunityApproveIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    opportunityId: undefined,
    approveStatus: undefined,
    approveMessage: undefined,
    approveTime: [],
    submmitTime: [],
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: [],
    companyKey: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'approveStatus',
      label: '审批状态',
      align: 'center',
      minWidth: 110,
      slot: 'approveStatus'
    },
    {
      prop: 'approveTime',
      label: '审批时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'approveMessage',
      label: '审批消息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'submmitTime',
      label: '提交时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'title',
      label: '商机标题',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'source',
      label: '商机来源',
      align: 'center',
      minWidth: 110,
      slot: 'source'
    },
    {
      prop: 'stage',
      label: '商机阶段',
      align: 'center',
      minWidth: 110,
      slot: 'stage'
    },
    {
      prop: 'customerName',
      label: '客户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerShortName',
      label: '客户简称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 120,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示审批弹窗 */
  const showApprove = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return OpportunityApproveApi.getOpportunityApprovePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await OpportunityApproveApi.deleteOpportunityApprove(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };

  /**
   *
   * @param row 打开审批弹窗
   */
  const approveOpportunity = async (row) => {
    current.value = row ?? null;
    showApprove.value = true;
  };

  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data =
        await OpportunityApproveApi.exportOpportunityApprove(queryParams);
      download.excel(data, '商机审批.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
