<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 合同维护 </span>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="140px"
    @submit.prevent=""
  >
    <ele-card header="基础信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同编号" prop="code">
            <el-input
              maxlength="40"
              disabled
              clearable
              v-model="form.code"
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同名称" prop="name">
            <el-input
              maxlength="40"
              clearable
              v-model="form.name"
              placeholder="请输入合同名称"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="签订渠道" prop="signingChannel">
            <el-radio-group v-model="form.signingChannel">
              <el-radio :value="1" label="线上" />
              <el-radio :value="2" label="线下" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同签订日期" prop="signingDate">
            <el-date-picker
              type="date"
              v-model="form.signingDate"
              value-format="x"
              placeholder="请选择合同签订日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经办人" prop="handler">
            <ele-table-select
              filterable
              clearable
              placeholder="请选择"
              value-key="id"
              v-model:model-value="form.handler"
              label-key="nickname"
              v-model="selectedValue2"
              :table-props="tableProps2"
              @select="onHandlerSelect"
              :init-value="initHandlerValue"
              @filterChange="onHandlerFilterChange"
              @visibleChange="onHandlerVisibleChange"
            >
            </ele-table-select>
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="客户信息">
      <el-row :gutter="16">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="customerType">
            <el-radio-group
              v-model="form.customerType"
              @change="onCustomerTypeChange"
            >
              <el-radio :value="1" label="企业" />
              <el-radio :value="2" label="个人" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerId">
            <ele-table-select
              ref="customSelectRef"
              filterable
              clearable
              placeholder="请选择"
              value-key="id"
              v-model:model-value="form.customerId"
              label-key="customerName"
              v-model="selectedValue1"
              :table-props="tableProps1"
              @select="onCustomSelect"
              :init-value="initCustomerValue"
              @filterChange="onCustomerFilterChange"
              @visibleChange="onCustomerVisibleChange"
            >
            </ele-table-select>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="customerIndustry">
            <el-select
              disabled
              clearable
              v-model="form.customerIndustry"
              placeholder="请选择所属行业"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="身份证" prop="customerIdcard">
            <el-input
              disabled
              clearable
              v-model="form.customerIdcard"
              placeholder="请输入身份证号"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="customerPhone">
            <el-input
              clearable
              disabled
              maxlength="11"
              v-model="form.customerPhone"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="邮箱" prop="customerEmail">
            <el-input
              clearable
              disabled
              v-model="form.customerEmail"
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="租赁费用">
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁期限" prop="dateTime">
            <el-date-picker
              v-model="form.dateTime"
              type="daterange"
              value-format="x"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="首期收款日期" prop="paymentFirstDate">
            <el-date-picker
              type="date"
              v-model="form.paymentFirstDate"
              value-format="x"
              placeholder="请选择合同签订日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="收款截止日期" prop="paymentEndDateDays">
            <div class="formdata-flex">
              <el-select
                clearable
                v-model="form.paymentEndDateType"
                placeholder="请选择"
                class="ele-fluid"
              >
                <el-option :value="1" label="每期开始日前" />
                <el-option :value="2" label="每期开始日后" />
                <el-option :value="3" label="每期结束日前" />
                <el-option :value="4" label="每期结束日后" />
              </el-select>
              <div>
                <el-input
                  clearable
                  v-model="form.paymentEndDateDays"
                  placeholder="请输入天数"
                >
                  <template #append>天</template>
                </el-input>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="付款周期" prop="paymentCycle">
            <div class="formdata-flex">
              <el-input
                clearable
                type="number"
                v-model="form.paymentCycle"
                placeholder="请输入周期"
                class="input-wh"
              >
                <template #append>个月</template>
              </el-input>
              <el-switch
                v-model="form.isPaymentFull"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
              &nbsp;一次性付清
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁保证金(元)" prop="ensureFee">
            <el-input
              clearable
              v-model="form.ensureFee"
              placeholder="请输入租赁保证金(元)"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="违约金(元)" prop="renegeFee">
            <el-input
              clearable
              v-model="form.renegeFee"
              placeholder="请输入违约金(元)"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="滞纳金比例(%)" prop="penaltyAmtRatio">
            <el-input
              clearable
              v-model="form.penaltyAmtRatio"
              placeholder="请输入滞纳金比例(%)"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card style="min-height: 350px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
        <el-tab-pane label="房产信息" name="0">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getHouseDel(1, '', null)">
                添加
              </el-button>
            </div>
            <el-table
              :data="form.stationContractHouses"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="园区" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.parkInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼栋" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.buildingInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼层" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.floorInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间号" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间编码" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNum"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="面积(㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.area"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="单价(元/㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.price"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index, row }">
                  <el-button
                    type="primary"
                    link
                    @click="getHouseDel(2, $index, null)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="收款信息" name="1">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getPaymentPlanDel(1, '')">
                添加
              </el-button>
              <el-button type="primary" @click="importData"> 导入 </el-button>
              <!-- <el-button type="primary" @click="generatePlanConfirm()">
                生成收款计划
              </el-button> -->
            </div>
            <el-table
              :data="form.stationContractPaymentPlans"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="开始日期">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.startDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入开始日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.startDate"
                      value-format="x"
                      placeholder="请选择开始日期"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="结束日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.endDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入结束日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.endDate"
                      value-format="x"
                      placeholder="请选择结束日期"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="收款日" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.paymentDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入结束日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.paymentDate"
                      value-format="x"
                      placeholder="请选择收款日"
                      class="ele-fluid"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="固定租金(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.paymentAmt`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入固定租金',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      :min="0"
                      :precision="2"
                      v-model="row.paymentAmt"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <!-- <el-table-column label="浮动租金(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.floatingRent`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入浮动租金',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input type="number" v-model="row.floatingRent"></el-input>
                  </el-form-item>
                </template>
              </el-table-column> -->
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getPaymentPlanDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件信息" name="2">
          <div style="overflow: auto">
            <div class="file-box">
              <el-upload :show-file-list="false" :http-request="httpRequest">
                <el-button type="primary">上传文件</el-button>
              </el-upload>
            </div>
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.stationContractFiles"
            >
              <el-table-column label="文件名" prop="name"></el-table-column>
              <el-table-column label="大小" prop="size">
                <template #default="scope">
                  <span>{{ scope.row.size }} KB</span>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="uploadTime">
                <template #default="scope">
                  <span>{{ formatDate2(scope.row.uploadTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-link
                    type="primary"
                    :underline="false"
                    download
                    :href="scope.row.url"
                  >
                    下载
                  </el-link>
                  <el-divider direction="vertical" />
                  <el-link
                    type="danger"
                    :underline="false"
                    @click="deleteFile(scope.row)"
                  >
                    删除
                  </el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </el-form>
  <!-- 底部工具栏 -->
  <ele-bottom-bar>
    <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
      <span>{{ validMsg }}</span>
    </ele-text>
    <template #extra>
      <el-button type="primary" :loading="loading" class="ml-2" @click="submit"
        >保存</el-button
      >
    </template>
  </ele-bottom-bar>
  <HouseEditForm v-model="showHouseEdit" :data="current" @done="recordHandle" />
  <DataImport v-model="showImport" @done="excelImport" />
</template>

<script lang="ts" setup>
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate2 } from '@/utils/formatTime';
  import HouseEditForm from './HouseEditForm.vue';
  import { getCustomerSimpleList } from '@/api/lease/attract/index';
  import { updateFile, getGeneratePlan } from '@/api/lease/contract';
  import { getSimpleUserByTypeList } from '@/api/system/user';
  import DataImport from './DataImport.vue';
  import {
    updateStationContract,
    createStationContract,
    getStationContractDetail
  } from '@/api/lease/stationcontract/index';
  import { getLongHouseLov } from '@/api/lease/longhouse/index';
  import { is } from 'bpmn-js/lib/util/ModelUtil';
  const activeName = ref('0');
  const props = defineProps({
    data: Object
  });
  /** 加载状态 */
  const loading = ref(false);
  const emit = defineEmits(['done']);
  const isUpdate = ref(false);
  /** 是否显示编辑弹窗 */
  const showHouseEdit = ref(false);
  /** 当前编辑数据 */
  const current = ref(null);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);
  /**导入弹窗 */
  const showImport = ref(false);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    type: undefined,
    code: undefined,
    name: undefined,
    status: undefined,
    approveStatus: undefined,
    signingDate: undefined,
    signingChannel: 1,
    handler: undefined,
    handlerName: undefined,
    partaId: undefined,
    partaName: undefined,
    customerId: undefined,
    customerType: 1,
    customerName: undefined,
    customerIndustry: undefined,
    customerIdcard: undefined,
    customerPhone: undefined,
    customerEmail: undefined,
    startDate: undefined,
    endDate: undefined,
    rentalPlan: undefined,
    monthBillingWay: undefined,
    daysOfYear: undefined,
    paymentFirstDate: undefined,
    dateTime: [],
    paymentEndDateType: undefined,
    paymentEndDateDays: undefined,
    unitPrice: undefined,
    rentalUnit: undefined,
    isPaymentFull: undefined,
    paymentCycle: undefined,
    ensureFee: undefined,
    renegeFee: undefined,
    penaltyAmtRatio: undefined,
    penaltyAmtType: undefined,
    rentalArea: undefined,
    totalAmt: undefined,
    receivedTotalAmt: undefined,
    rentalHoliday: undefined,
    rentIncrease: undefined,
    extend: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: undefined,
    stationContractFiles: [],
    stationContractHouses: [],
    stationContractPaymentPlans: []
  });

  const importData = () => {
    showImport.value = true;
  };

  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    rentalPlan: [
      { required: true, message: '请选择租金方案', trigger: 'blur' }
    ],
    dateTime: [
      { required: true, message: '请选择租赁期限日期', trigger: 'blur' }
    ],
    handler: [{ required: true, message: '请选择经办人', trigger: 'blur' }],
    customerId: [{ required: true, message: '请输入客户名称', trigger: 'blur' }]
  });
  //上传文件
  const httpRequest = async ({ file }) => {
    let formData = new FormData();
    formData.append('file', file);
    let res = await updateFile(formData);
    form.stationContractFiles.push({
      fileId: res.data.id,
      name: res.data.name,
      url: res.data.url,
      type: res.data.type,
      size: res.data.size,
      uploadTime: res.data.createTime,
      path: res.data.path
    });
  };
  const deleteFile = async (row) => {
    form.stationContractFiles.splice(row, 1);
  };
  /** 表单验证失败提示信息 */
  const validMsg = ref('');

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      try {
        loading.value = true;
        validMsg.value = '';
        loading.value = true;
        let res = null;
        let params = {
          ...form,
          startDate: '',
          endDate: ''
        };
        if (params.dateTime && params.dateTime.length > 0) {
          params.startDate = params.dateTime[0];
          params.endDate = params.dateTime[1];
        }
        if (!isUpdate.value) {
          res = await createStationContract(params);
        } else {
          res = await updateStationContract(params);
        }
        if (res) {
          EleMessage.success('提交成功');
          cancle();
        }
      } finally {
        loading.value = false;
      }
    });
  };
  //新增房产信息
  const getHouseDel = async (type, index, row) => {
    if (type == 1) {
      if (row) {
        current.value = { ...row, index: index, isOldFlag: 'Y' };
      } else {
        current.value = null;
      }
      showHouseEdit.value = true;
      // 初始化流程定义详情
      await nextTick();
    } else {
      form.stationContractHouses.splice(index, 1);
    }
  };

  /** 关闭弹窗 */
  const recordHandle = (data) => {
    if (data) {
      if (data.isOldFlag == 'N') {
        data.isOldFlag = 'Y';
        form.stationContractHouses.push(data);
      } else {
        form.stationContractHouses.splice(data.index, 1, data);
      }
    }
  };
  //新增收款信息
  const getPaymentPlanDel = (type, index) => {
    if (type == 1) {
      form.stationContractPaymentPlans.push({
        startDate: '',
        endDate: '',
        paymentDate: '',
        paymentAmt: 0
      });
    } else {
      form.stationContractPaymentPlans.splice(index, 1);
    }
  };
  //生成付款计划
  const generatePlanConfirm = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      try {
        loading.value = true;
        form.stationContractPaymentPlans = [];
        validMsg.value = '';
        let params = {
          ...form,
          startDate: '',
          endDate: ''
        };
        if (params.dateTime && params.dateTime.length > 0) {
          params.startDate = params.dateTime[0];
          params.endDate = params.dateTime[1];
        }
        let res = await getGeneratePlan(params);
        form.stationContractPaymentPlans.push(...res);
      } finally {
        loading.value = false;
      }
    });
  };
  const initFormInfo = async () => {
    resetFields();
    await loadData();
  };
  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      isUpdate.value = true;
      loading.value = true;
      try {
        // 获取最新数据
        const response = await getStationContractDetail(props.data.id);
        if (response) {
          assignFields(response);
          //设置回显数据
          setInitValue(response);
          // 初始化下拉框
          loadUserCustomer({customerType: response.customerType});
          //定义一个数组
          form.dateTime = [];
          form.dateTime.push(response.startDate);
          form.dateTime.push(response.endDate);
          selectedValue2.value = response.handler;
          selectedValue1.value = response.customerId;
        }
      } catch (error) {
        console.error('获取合同详情失败', error);
      } finally {
        loading.value = false;
      }
    } else {
      isUpdate.value = false;
      // 初始化下拉框
      loadUserCustomer({customerType: form.customerType});
    }
  };
  defineExpose({ initFormInfo });


  /** 关闭导入弹窗 */
  const excelImport = (data) => {
    if (data) {
      for (let i = 0; i < data.length; i++) {
        form.stationContractPaymentPlans.push({
          startDate: data[i].startDate,
          endDate: data[i].endDate,
          paymentDate: data[i].paymentDate,
          paymentAmt: data[i].fixedRent
        });
      }
    }
  };

  //设置回显值
const setInitValue = (response) => {
  //经办人回显
  initHandlerValue.value = {
      id: response.handler,
      nickname: response.handlerName
    };
  //客户回显
  initCustomerValue.value = {
      id: response.customerId,
      customerName: response.customerName,
      contactName: response.contactName
    };
  };

  //页面初始化时，初始化经办人和客户信息
const loadUserCustomer = (param) => {
  //经办人下拉框初始化
  querySimpleUserList();
  //客户下拉框初始化
  querySimpleCustomerList(param.customerType);
  };
  

//------------------------------------------------------经办人逻辑-----------------------------------------------------
//经办人回显
  const initHandlerValue = ref();
  //经办人选中的值
  const selectedValue2 = ref();
  //下拉框查询的值
  let allHandlerData = [];

  const tableProps2 = reactive<any>({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'username',
        label: '用户名',
        slot: 'nickname',
        align: 'center',
        width: 130
      },
      {
        prop: 'nickname',
        label: '昵称',
        slot: 'nickname',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  /** 选中事件 */
  const onHandlerSelect = (selection) => {
    form.handlerName = selection.nickname;
  };

  /** 经办人下拉框查询 */
  const querySimpleUserList = () => {
    getSimpleUserByTypeList(1).then((data) => {
      tableProps2.datasource = data;
      allHandlerData = data;
    });
  };
    

  /** 经办人筛选输入框值改变事件 */
  const onHandlerFilterChange = (keyword) => {
    tableProps2.datasource = allHandlerData.filter((d) => {
      return d.username?.includes?.(keyword) || d.nickname?.includes?.(keyword);
      
    });
  };

  /** 经办人下拉框展开状态改变事件 */
  const onHandlerVisibleChange = (visible) => {
    if (visible) {
      tableProps2.datasource = allHandlerData;
    }
  };
  

//------------------------------------------------------客户逻辑-----------------------------------------------------

  const customSelectRef = ref(null);

  /** 表格下拉选中值 */
  const selectedValue1 = ref();

  //下拉框查询的值
  let allCustomerData = [];

  //经办人回显
  const initCustomerValue = ref();

  /** 客户表格配置 */
  const tableProps1 = reactive<any>({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'customerName',
        label: '客户名',
        align: 'center',
        width: 170
      },
      {
        prop: 'contactName',
        label: '昵称',
        slot: '联系人',
        align: 'center',
        width: 170
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });
  

/** 选中事件 */
  const onCustomSelect = (selection) => {
    form.customerName = selection.customerName;
    form.contactName = selection.contactName;
    form.customerPhone = selection.contactNumber;
    form.customerEmail = selection.contactEmail;
    form.contactAddress = selection.contactAddress;
    form.customerIdcard = selection.idNumber;
    form.customerIndustry = selection.industry;
  };

/** 客户重置事件 */
  const resetCustom = () => {
    selectedValue1.value = undefined;
    form.customerId = undefined;
    form.customerName = undefined;
    form.contactName = undefined;
    form.customerPhone = undefined;
    form.customerEmail = undefined;
    form.contactAddress = undefined;
    form.customerIdcard = undefined;
    form.customerIndustry = undefined;
  };

/**选择客户类型的时候，要清空客户信息 */
  const onCustomerTypeChange = (value) => {
    resetCustom();
    customSelectRef.value?.tableRef?.reload();
    querySimpleCustomerList(value);
  };

/** 客户下拉框查询 */
  const querySimpleCustomerList = (customerType) => {
    getCustomerSimpleList(customerType).then((data) => {
      tableProps1.datasource = data;
      allCustomerData = data;
    });
  };

  /** 经办人筛选输入框值改变事件 */
  const onCustomerFilterChange = (keyword) => {
    tableProps1.datasource = allCustomerData.filter((d) => {
      return d.customerName?.includes?.(keyword) || d.contactName?.includes?.(keyword);
      
    });
  };

  /** 经办人下拉框展开状态改变事件 */
  const onCustomerVisibleChange = (visible) => {
    if (visible) {
      tableProps1.datasource = allCustomerData;
    }
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>
