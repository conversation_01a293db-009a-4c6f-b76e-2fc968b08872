<template>
  <ele-modal
    form
    :width="480"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @close="cancle"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <ele-card header="房产信息">
        <el-row :gutter="0">
          <el-col :lg="24" :md="12" :sm="12" :xs="24">
            <el-form-item prop="parkCode" label="园区信息">
              <ele-table-select
                ref="houseParkSelectRef"
                filterable
                clearable
                placeholder="请选择"
                value-key="spaceName"
                v-model:model-value="form.parkCode"
                label-key="spaceName"
                @change="onHouseParkChange"
                v-model="selectedHousePark"
                :table-props="tableHousePark"
                @select="onParkSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="12" :sm="12" :xs="24">
            <el-form-item prop="buildingCode" label="楼栋信息">
              <ele-table-select
                ref="houseBuildingSelectRef"
                filterable
                clearable
                placeholder="请选择"
                value-key="spaceName"
                v-model:model-value="form.buildingCode"
                label-key="spaceName"
                @change="onHouseBuildingChange"
                v-model="selectedHouseBuilding"
                :table-props="tableHouseBuilding"
                @select="onBuildingSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="12" :sm="12" :xs="24">
            <el-form-item prop="floorCode" label="楼层信息">
              <ele-table-select
                ref="houseFloorSelectRef"
                filterable
                clearable
                placeholder="请选择"
                value-key="spaceName"
                v-model:model-value="form.floorCode"
                label-key="spaceName"
                @change="onHouseFloorChange"
                v-model="selectedHouseFloor"
                :table-props="tableHouseFloor"
                @select="onFloorSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="12" :sm="12" :xs="24">
            <el-form-item prop="roomNum" label="房间编码">
              <ele-table-select
                ref="houseRoomNumSelectRef"
                filterable
                clearable
                placeholder="请选择"
                value-key="spaceName"
                v-model:model-value="form.roomNum"
                label-key="spaceName"
                v-model="selectedHouseRoomNum"
                :table-props="tableHouseRoomNum"
                @select="onRoomNumSelect"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="12" :sm="12" :xs="24">
            <el-form-item prop="price" label="价格">
              <el-input
                maxlength="40"
                clearable
                v-model="form.price"
                placeholder="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >确认</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { getLongHouseLovPage } from '@/api/lease/longhouse/index';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';

  /** 预存金信息 表单 */
  defineOptions({ name: 'PrepaidInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    parkId: void 0,
    parkInfo: undefined,
    parkCode: undefined,
    buildingId: void 0,
    buildingInfo: undefined,
    buildingCode: undefined,
    floorId: void 0,
    floorInfo: undefined,
    floorCode: undefined,
    roomId: void 0,
    roomNo: undefined,
    area: undefined,
    houseId: undefined,
    roomNum: undefined,
    price: undefined,
    isOldFlag: "N"
  });

  /** 表单验证规则 */
  const rules = reactive({
    parkCode: [{ required: true, message:'请选择园区' , trigger: 'blur' }],
    price: [
      { required: true, message: '请输入价格', trigger: 'blur' },
      { validator: (rule, value, callback) => {
          if (value <= 0) {
            callback(new Error('价格必须大于0'));
          } else {
            callback();
          }
        }, trigger: 'blur' }
    ]
  });

  const resetFormDataAndTables = () => {
    resetFields();
    selectedHousePark.value = undefined;
    selectedHouseBuilding.value = undefined;
    selectedHouseFloor.value = undefined;
    selectedHouseRoomNum.value = undefined;
    tableHouseBuilding.datasource = [];
    tableHouseFloor.datasource = [];
    tableHouseRoomNum.datasource = [];
  };

  const cancle = () => {
    visible.value = false;
    resetFormDataAndTables();
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      // 转换开始时间和结束时间为时间戳
      const response = {
        ...form
      };
      visible.value = false;
      // 发送操作成功的事件
      emit('done', response);
    } finally {
      loading.value = false;
      resetFormDataAndTables();
    }
  };

  const handleOpen = async () => {
    loading.value = true;
    if (props.data) {
      const result = props.data;
      assignFields({ ...result });
      selectedHousePark.value = result.parkInfo;
      selectedHouseBuilding.value = result.buildingInfo;
      selectedHouseFloor.value = result.floorInfo;
      selectedHouseRoomNum.value = result.roomNo;

      // 依次初始化表格配置
      tableHousePark.datasource = ({ pages, where, orders }) => {
        return getLongHouseLovPage({
          level: '1',
          type: 'park',
          ...where, ...orders, ...pages
        });
      };

      tableHouseBuilding.datasource = ({ pages, where, orders }) => {
        return getLongHouseLovPage({
          level: '2',
          type: 'building',
          spaceCode: form.parkCode,
          ...where, ...orders, ...pages
        });
      };

      tableHouseFloor.datasource = ({ pages, where, orders }) => {
        return getLongHouseLovPage({
          level: '3',
          type: 'floor',
          spaceCode: form.buildingCode,
          ...where, ...orders, ...pages
        });
      };

      tableHouseRoomNum.datasource = ({ pages, where, orders }) => {
        return getLongHouseLovPage({
          level: '4',
          type: 'roomNum',
          spaceCode: form.floorCode,
          ...where, ...orders, ...pages
        });
      };
      isUpdate.value = true;
    } else {
      resetFormDataAndTables();
      isUpdate.value = false;
    }
    loading.value = false;
  };

  const selectedHousePark = ref();
  const tableHousePark = reactive<any>({
    datasource: ({ pages, where, orders }) => {
      return getLongHouseLovPage({
        level: '1',
        type: 'park',
        ...where, ...orders, ...pages
      });
    },
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'spaceCode',
        label: '空间编码',
        slot: 'spaceName',
        align: 'center',
        width: 130
      },
      {
        prop: 'spaceName',
        label: '空间名称',
        slot: 'spaceName',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  const selectedHouseBuilding = ref();
  const tableHouseBuilding = reactive<any>({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'spaceCode',
        label: '空间编码',
        slot: 'spaceName',
        align: 'center',
        width: 130
      },
      {
        prop: 'spaceName',
        label: '空间名称',
        slot: 'spaceName',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  const selectedHouseFloor = ref();
  const tableHouseFloor = reactive<any>({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'spaceCode',
        label: '空间编码',
        slot: 'spaceName',
        align: 'center',
        width: 130
      },
      {
        prop: 'spaceName',
        label: '空间名称',
        slot: 'spaceName',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  const selectedHouseRoomNum = ref();
  const tableHouseRoomNum = reactive<any>({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'spaceCode',
        label: '空间编码',
        slot: 'spaceName',
        align: 'center',
        width: 130
      },
      {
        prop: 'spaceName',
        label: '空间名称',
        slot: 'spaceName',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  const houseParkSelectRef = ref(null);
  const houseBuildingSelectRef = ref(null);
  const houseFloorSelectRef = ref(null);
  const houseRoomNumSelectRef = ref(null);

  /** 园区选中事件 */
  const onParkSelect = (selection) => {
    if (selection.spaceCode !== form.parkCode) {
      resetBuildingInfo();
      form.parkId = selection.id;
      form.parkInfo = selection.spaceName;
      form.parkCode = selection.spaceCode;
      form.houseId = selection.id;
      form.area = selection.area;
      form.price = selection.price;
      if (selection.spaceCode) {
        tableHouseBuilding.datasource = ({ pages, where, orders }) => {
          return getLongHouseLovPage({
            level: '2',
            type: 'building',
            spaceCode: selection.spaceCode,
            ...where, ...orders, ...pages
          });
        };
      }
      
    }
  };

  /** 楼栋选中事件 */
  const onBuildingSelect = (selection) => {
    if (selection.spaceCode !== form.buildingCode) {
      resetFloorInfo();
      form.buildingId = selection.id;
      form.buildingInfo = selection.spaceName;
      form.buildingCode = selection.spaceCode;
      form.houseId = selection.id;
      form.area = selection.area;
      form.price = selection.price;
      if (selection.spaceCode) {
        tableHouseFloor.datasource = ({ pages, where, orders }) => {
          return getLongHouseLovPage({
            level: '3',
            type: 'floor',
            spaceCode: selection.spaceCode,
            ...where, ...orders, ...pages
          });
        };
      }
    }
  };

  /** 楼层选种事件选中事件 */
  const onFloorSelect = (selection) => {
    if (selection.spaceCode !== form.floorCode) {
      //重新选择楼层后，要重置房间信息
      resetRoomInfo();
      form.floorId = selection.id;
      form.floorInfo = selection.spaceName;
      form.floorCode = selection.spaceCode;
      form.houseId = selection.id;
      form.area = selection.area;
      form.price = selection.price;
      if (selection.spaceCode) {
        tableHouseRoomNum.datasource = ({ pages, where, orders }) => {
          return getLongHouseLovPage({
            level: '4',
            type: 'roomNum',
            spaceCode: selection.spaceCode,
            ...where, ...orders, ...pages
          });
        };
      }
    }
  };

  /** 房间选中事件 */
  const onRoomNumSelect = (selection) => {
    if (selection.spaceCode !== form.roomNum) {
      form.roomNum = selection.spaceCode;
      form.roomNo = selection.spaceName;
      form.houseId = selection.id;
      form.area = selection.area;
      form.price = selection.price;
    }
  };

  /** 园区重置事件 */
  const resetParkInfo = () => {
    resetBuildingInfo();
    selectedHousePark.value = undefined;
    form.parkId = undefined;
    form.parkInfo = undefined;
    form.parkCode = undefined;
    form.houseId = undefined;
    
  };

  /** 楼栋重置事件 */
  const resetBuildingInfo = () => {
    resetFloorInfo();
    selectedHouseBuilding.value = undefined;
    form.buildingId = undefined;
    form.buildingInfo = undefined;
    form.buildingCode = undefined;
    form.houseId = form.parkId;
  };

  /**楼层重置事件 */
  const resetFloorInfo = () => {
    resetRoomInfo();
    selectedHouseFloor.value = undefined;
    form.floorId = undefined;
    form.floorInfo = undefined;
    form.floorCode = undefined;
    form.houseId = form.buildingId;
    
  };

  /**房间重置事件 */
  const resetRoomInfo = () => {
    selectedHouseRoomNum.value = undefined;
    form.roomId = undefined;
    form.roomNum = undefined;
    form.roomNo = undefined;
    form.houseId = undefined;
    form.area = undefined;
    form.price = undefined;
  };
  const onHouseParkChange = (value) => {
    // 如果value为空，则执行的是清理园区操作，则要重置楼栋信息
    if(!value){
      resetParkInfo();
    }else{
      resetBuildingInfo();
    }
  };

  const onHouseBuildingChange = (value) => {
    //  如果value为空，则执行的是清理楼栋操作，则要重置楼栋信息
    // 否则只需要清理楼层信息即可
    if(!value){
      resetBuildingInfo();
    }else{
      resetFloorInfo();
    }
  };

  const onHouseFloorChange = (value) => {
    // 如果value为空，则执行的是清理楼层操作，则要重置楼层信息
    // 否则只需要清理房间信息即可
    if(!value){
      resetFloorInfo();
    }else{
      resetRoomInfo();
    }
  };

  const onHouseRoomChange = (value) => {
    // 如果value为空，则执行的是清理房间操作，则要重置房间信息
    if(!value){
      resetRoomInfo();
    }
  };
</script>
