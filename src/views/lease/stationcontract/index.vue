<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card v-if="showStep == 1" :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同编码" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入合同编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同名称" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入合同名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同状态" prop="status">
              <el-select
                clearable
                v-model="queryParams.status"
                placeholder=""
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card
      v-if="showStep == 1"
      flex-table
      :body-style="{ paddingTop: '8px' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        v-model:selections="selections"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:station-contract:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            v-permission="['lease:station-contract:update']"
            @click="submitBatch(null)"
          >
            提交
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:station-contract:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #customerType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CUSTOMER_TYPE"
            :model-value="row.customerType"
          />
        </template>
        <template #rentalPlan="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_RENTAL_PLAN"
            :model-value="row.rentalPlan"
          />
        </template>
        <template #signingChannel="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_SIGN_CHANNEL"
            :model-value="row.signingChannel"
          />
        </template>
        <template #code="{ row }">
          <el-link :underline="false" type="primary" @click="openQuery(row)">
            {{ row.code }}
          </el-link>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:station-contract:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:station-contract:delete']"
            v-if="!row.status || row.status === 1"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:station-contract:delete']"
            v-if="!row.status || row.status === 1"
          >
            删除
          </el-link>
          <el-divider direction="vertical" />
          <ele-dropdown
            v-if="moreItems.length"
            :items="moreItems"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <StationContractEditForm
      ref="stationContractRef"
      v-if="showStep == 2"
      :data="current"
      @done="stopDone"
    />
    <StationContractForm
      ref="stationContractQueryRef"
      v-if="showStep == 3"
      :data="current"
      @done="stopDone"
    />
    <ContractTerminalForm
      ref="contractTerminalRef"
      v-if="showStep == 6"
      :data="current"
      @done="stopDone"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { PlusOutlined, DownloadOutlined } from '@/components/icons';
  import { dateFormatter, dateFormatter2 } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as StationContractApi from '@/api/lease/stationcontract';
  import StationContractEditForm from './StationContractEditForm.vue';
  import StationContractForm from './StationContractForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { EleMessage } from 'ele-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading } from 'element-plus';
  import ContractTerminalForm from './ContractTerminalForm.vue';

  /** 工位合同 列表 */
  defineOptions({ name: 'StationContractIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  const showStep = ref(1); // 当前步骤
  const contractTerminalRef = ref(null);
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    id: undefined,
    type: undefined,
    code: undefined,
    name: undefined,
    status: undefined,
    approveStatus: undefined,
    signingDate: [],
    signingChannel: undefined,
    handler: undefined,
    handlerName: undefined,
    partaId: undefined,
    partaName: undefined,
    customerId: undefined,
    customerType: undefined,
    customerName: undefined,
    customerIndustry: undefined,
    customerIdcard: undefined,
    customerPhone: undefined,
    customerEmail: undefined,
    startDate: [],
    endDate: [],
    rentalPlan: undefined,
    monthBillingWay: undefined,
    daysOfYear: undefined,
    paymentFirstDate: [],
    paymentEndDateType: undefined,
    paymentEndDateDays: undefined,
    unitPrice: undefined,
    rentalUnit: undefined,
    isPaymentFull: undefined,
    paymentCycle: undefined,
    ensureFee: undefined,
    renegeFee: undefined,
    penaltyAmtRatio: undefined,
    penaltyAmtType: undefined,
    rentalArea: undefined,
    totalAmt: undefined,
    receivedTotalAmt: undefined,
    rentalHoliday: undefined,
    rentIncrease: undefined,
    extend: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '合同编码',
      align: 'center',
      minWidth: 180,
      slot: 'code'
    },
    {
      prop: 'name',
      label: '合同名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '合同状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'signingDate',
      label: '合同签订日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter2
    },
    {
      prop: 'signingChannel',
      label: '合同签订渠道',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'handlerName',
      label: '经办人',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerType',
      label: '客户类型',
      align: 'center',
      minWidth: 110,
      slot: 'customerType'
    },
    {
      prop: 'customerName',
      label: '客户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerIndustry',
      label: '客户行业',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerIdcard',
      label: '客户身份证',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerPhone',
      label: '客户电话',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerEmail',
      label: '客户邮箱',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'startDate',
      label: '租赁开始时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'endDate',
      label: '租赁结束时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'rentalPlan',
      label: '租金方案',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'monthBillingWay',
      label: '月计费方式',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'daysOfYear',
      label: '年计算方式',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'paymentFirstDate',
      label: '首期收款日期',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'paymentEndDateType',
      label: '收款截止日期',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'paymentEndDateDays',
      label: '收款截止日期',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'unitPrice',
      label: '租金单价',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'rentalUnit',
      label: '租金单位',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'isPaymentFull',
      label: '是否一次性付清',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'paymentCycle',
      label: '付款周期',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'ensureFee',
      label: '租赁保证金',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'renegeFee',
      label: '违约金',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'penaltyAmtRatio',
      label: '滞纳金比例',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'penaltyAmtType',
      label: '滞纳金基数类型',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'rentalArea',
      label: '租赁面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'totalAmt',
      label: '总费用',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'receivedTotalAmt',
      label: '已收总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'rentalHoliday',
      label: '免租期',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'rentIncrease',
      label: '租金递增',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'extend',
      label: '扩展信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中

  const stationContractRef = ref(null);

  const stationContractQueryRef = ref(null);

  /** 表格选中数据 */
  const selections = ref([]);

  const stopDone = () => {
    showStep.value = 1;
    reload();
  };

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return StationContractApi.getStationContractPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    stationContractRef.value?.initFormInfo(current);
  };

  /** 打开查询弹窗 */
  const openQuery = async (row) => {
    current.value = row ?? null;
    showStep.value = 3;
    // 初始化流程定义详情
    await nextTick();
    stationContractQueryRef.value?.initFormInfo(current);
  };

  const removeBatch = async (row) => {
    // 删除的二次确认
    await message.delConfirm();

    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在删除中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      // 发起删除
      await StationContractApi.deleteStationContract(row.id);
      // 关闭加载状态
      loadingInstance.close();
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch (error) {
      // 关闭加载状态
      loadingInstance.close();
      console.error('删除失败:', error);
    }
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await StationContractApi.exportStationContract(queryParams);
      download.excel(data, '工位合同.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  /** 批量提交 */
  const submitBatch = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }

    // 检查合同状态是否为新建
    const allowedStatuses = [0];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有新建或者审批拒绝或者撤回的数据项才能提交');
      return;
    }

    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在提交中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    const batchIds = selections.value.map((d) => d.id);
    try {
      await StationContractApi.submitStationContract({
        type: 'submit',
        batchIds: batchIds
      });
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('提交成功');
      reload();
    } finally {
      // 关闭加载状态
      loadingInstance.close();
    }
  };

  //生成合同账单
  const toGenerateBill = async (row) => {
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '生成账单中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await StationContractApi.generateBill(row.id);
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('生成成功');
      reload();
    } finally {
      // 关闭加载状态
      loadingInstance.close();
    }
  };

  //生成押金
  const toDeposit = async (row) => {
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '生成押金中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await StationContractApi.getToDeposit(row.id);
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('生成成功');
      reload();
    } finally {
      // 关闭加载状态
      loadingInstance.close();
    }
  };

  //退租合同
  const terminalContract = async (row) => {
    current.value = row ?? null;
    showStep.value = 6;
    // 初始化流程定义详情
    await nextTick();
    contractTerminalRef.value?.initFormInfo();
  };
  //作废合同
  const cancelContract = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查账单状态是否为待同步或者失败状态
    const allowedStatuses = [1, 2, 3];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有待审核或者未开始或者执行中状态的数据项才能作废');
      return;
    }
    ElMessageBox.confirm(
      `是否确认作废合同编号为"${rows.map((d) => d.code).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        // 开启全局加载状态
        const loadingInstance = ElLoading.service({
          lock: true,
          text: '作废合同中...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        StationContractApi.getCancelContract(rows[0].id)
          .then(() => {
            // 关闭加载状态
            loadingInstance.close();
            EleMessage.success('作废成功');
            reload();
          })
          .catch((e) => {
            // 关闭加载状态
            loadingInstance.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    items.push({ title: '生成合同账单', command: 'synInterface' });
    items.push({ title: '生成押金', command: 'toDeposit' });
    items.push({ title: '退租', command: 'throwContract' });
    items.push({ title: '作废', command: 'cancelledContract' });
    return items;
  });

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'synInterface') {
      toGenerateBill(row);
    } else if (key === 'toDeposit') {
      toDeposit(row);
    } else if (key === 'throwContract') {
      terminalContract(row);
    } else if (key === 'cancelledContract') {
      cancelContract(row);
    }
  };
</script>
