<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 合同查看 </span>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    label-width="140px"
    @submit.prevent=""
  >
    <ele-card header="基础信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同编号" prop="code">
            <el-input
              maxlength="40"
              disabled
              clearable
              v-model="form.code"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同名称" prop="name">
            <el-input
              maxlength="40"
              disabled
              clearable
              v-model="form.name"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="签订渠道" prop="signingChannel">
            <el-radio-group v-model="form.signingChannel" disabled>
              <el-radio :value="1" label="线上" />
              <el-radio :value="2" label="线下" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同签订日期" prop="signingDate">
            <el-date-picker
              type="date"
              v-model="form.signingDate"
              value-format="x"
              class="ele-fluid"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经办人" prop="handlerName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.handlerName"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="客户信息">
      <el-row :gutter="16">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="customerType">
            <el-radio-group
            disabled
              v-model="form.customerType"
              @change="onCustomerTypeChange"
            >
              <el-radio :value="1" label="企业" />
              <el-radio :value="2" label="个人" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.customerName"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="customerIndustry">
            <el-select
              disabled
              clearable
              placeholder=""
              v-model="form.customerIndustry"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="身份证" prop="customerIdcard">
            <el-input
              disabled
              clearable
              v-model="form.customerIdcard"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="customerPhone">
            <el-input
              clearable
              disabled
              maxlength="11"
              v-model="form.customerPhone"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="邮箱" prop="customerEmail">
            <el-input
              clearable
              disabled
              v-model="form.customerEmail"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="租赁费用">
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁期限" prop="dateTime">
            <el-date-picker
            disabled
              v-model="form.dateTime"
              type="daterange"
              value-format="x"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="首期收款日期" prop="paymentFirstDate">
            <el-date-picker
            disabled
              type="date"
              v-model="form.paymentFirstDate"
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="收款截止日期" prop="paymentEndDateDays">
            <div class="formdata-flex">
              <el-select
                clearable
                disabled
                v-model="form.paymentEndDateType"
                class="ele-fluid"
              >
                <el-option :value="1" label="每期开始日前" />
                <el-option :value="2" label="每期开始日后" />
                <el-option :value="3" label="每期结束日前" />
                <el-option :value="4" label="每期结束日后" />
              </el-select>
              <div>
                <el-input
                  clearable
                  disabled
                  v-model="form.paymentEndDateDays"
                >
                  <template #append>天</template>
                </el-input>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="付款周期" prop="paymentCycle">
            <div class="formdata-flex">
              <el-input
                clearable
                disabled
                type="number"
                v-model="form.paymentCycle"
                class="input-wh"
              >
                <template #append>个月</template>
              </el-input>
              <el-switch
                v-model="form.isPaymentFull"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
              &nbsp;一次性付清
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁保证金(元)" prop="ensureFee">
            <el-input
              clearable
              disabled
              v-model="form.ensureFee"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="违约金(元)" prop="renegeFee">
            <el-input
              clearable
              disabled
              v-model="form.renegeFee"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="滞纳金比例(%)" prop="penaltyAmtRatio">
            <el-input
              clearable
              disabled
              v-model="form.penaltyAmtRatio"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card v-if="form.status === 5"  header="退租信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租日期" prop="throwLeaseDate">
            <el-date-picker
              type="date"
              v-model="form.throwLease.throwLeaseDate"
              disabled
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租原因" prop="reason">
            <el-radio-group v-model="form.throwLease.reason" disabled>
              <el-radio
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.CO_CONTRACT_THROWLEASE_REASON
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="24" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租说明" prop="explanation">
            <el-input
              clearable
              disabled
              v-model="form.throwLease.explanation"
              maxlength="500"
              show-word-limit
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card style="min-height: 350px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
        <el-tab-pane label="房产信息" name="0">
          <div style="overflow: auto">
            <div class="file-box"></div>
            <el-table
              :data="form.stationContractHouses"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="园区" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.parkInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼栋" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.buildingInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼层" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.floorInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间号" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间编码" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNum"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="面积(㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.area"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="单价(元/㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.price"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="收款信息" name="1">
          <div style="overflow: auto">
            <div class="file-box"></div>
            <el-table
              :data="form.stationContractPaymentPlans"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="开始日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                  disabled
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.startDate`"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.startDate"
                      value-format="x"
                      class="date-time-o"
                      disabled
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="结束日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.endDate`"
                  >
                    <el-date-picker
                      type="date"
                      disabled
                      v-model="row.endDate"
                      value-format="x"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="收款日" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.paymentDate`"
                  >
                    <el-date-picker
                      type="date"
                      disabled
                      v-model="row.paymentDate"
                      value-format="x"
                      class="ele-fluid"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="固定租金(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.paymentAmt`"
                  >
                    <el-input-number
                    disabled
                      :min="0"
                      :precision="2"
                      v-model="row.paymentAmt"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <!-- <el-table-column label="浮动租金(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`stationContractPaymentPlans.${$index}.floatingRent`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入浮动租金',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input type="number" v-model="row.floatingRent"></el-input>
                  </el-form-item>
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件信息" name="2">
          <div style="overflow: auto">
            <div class="file-box"></div>
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.stationContractFiles"
            >
              <el-table-column label="文件名" prop="name"></el-table-column>
              <el-table-column label="大小" prop="size">
                <template #default="scope">
                  <span>{{ scope.row.size }} KB</span>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="uploadTime">
                <template #default="scope">
                  <span>{{ formatDate2(scope.row.uploadTime) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </el-form>
</template>

<script lang="ts" setup>
  import {
    DICT_TYPE,
    getStrDictOptions,
    getIntDictOptions
  } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate2 } from '@/utils/formatTime';
  import { getCustomerPage } from '@/api/lease/attract/index';
  import { updateFile, getGeneratePlan } from '@/api/lease/contract';
  import { getSimpleUserSelectList } from '@/api/system/user';
  import {
    getStationContractDetail
  } from '@/api/lease/stationcontract/index';
  const activeName = ref('0');
  const props = defineProps({
    data: Object
  });
  /** 加载状态 */
  const loading = ref(false);
  const emit = defineEmits(['done']);
  const isUpdate = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);
  /**导入弹窗 */
  const showImport = ref(false);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    type: undefined,
    code: undefined,
    name: undefined,
    status: undefined,
    approveStatus: undefined,
    signingDate: [],
    signingChannel: 1,
    handler: undefined,
    handlerName: undefined,
    partaId: undefined,
    partaName: undefined,
    customerId: undefined,
    customerType: 1,
    customerName: undefined,
    customerIndustry: undefined,
    customerIdcard: undefined,
    customerPhone: undefined,
    customerEmail: undefined,
    startDate: [],
    endDate: [],
    rentalPlan: undefined,
    monthBillingWay: undefined,
    daysOfYear: undefined,
    paymentFirstDate: [],
    dateTime: [],
    paymentEndDateType: undefined,
    paymentEndDateDays: undefined,
    unitPrice: undefined,
    rentalUnit: undefined,
    isPaymentFull: undefined,
    paymentCycle: undefined,
    ensureFee: undefined,
    renegeFee: undefined,
    penaltyAmtRatio: undefined,
    penaltyAmtType: undefined,
    rentalArea: undefined,
    totalAmt: undefined,
    receivedTotalAmt: undefined,
    rentalHoliday: undefined,
    rentIncrease: undefined,
    extend: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: [],
    stationContractFiles: [],
    stationContractHouses: [],
    stationContractPaymentPlans: [],
    throwLease: {
      throwLeaseDate: undefined,
      reason: -1,
      explanation: undefined
    }
  });

  const importData = () => {
    showImport.value = true;
  };

  /** 表单验证失败提示信息 */
  const validMsg = ref('');

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };
  
  const initFormInfo = async () => {
    resetFields();
    await loadData();
  };
  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      isUpdate.value = true;
      loading.value = true;
      try {
        // 获取最新数据
        const response = await getStationContractDetail(props.data.id);
        if (response) {
          assignFields(response);
          //定义一个数组
          form.dateTime = [];
          form.dateTime.push(response.startDate);
          form.dateTime.push(response.endDate);
        }
      } catch (error) {
        console.error('获取合同详情失败', error);
      } finally {
        loading.value = false;
      }
    } else {
      isUpdate.value = false;
    }
  };
  defineExpose({ initFormInfo });

</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>
