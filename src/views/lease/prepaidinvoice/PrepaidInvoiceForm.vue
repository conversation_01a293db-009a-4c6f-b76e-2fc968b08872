<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="130px"
      v-loading="loading"
    >
      <el-form-item label="账户编号" prop="accountCode">
        <el-input
          v-model="form.accountCode"
          placeholder="请输入账户编号"
          disabled
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="form.customerName"
          placeholder="请输入客户名称"
          disabled
        />
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="socialCreditCode">
        <el-input
          v-model="form.socialCreditCode"
          placeholder="请输入统一社会信用代码"
          disabled
        />
      </el-form-item>
      <el-form-item label="发票流水号" prop="invoiceNo">
        <el-input
          v-model="form.invoiceNo"
          placeholder="请输入发票流水号"
          disabled
        />
      </el-form-item>
      <el-form-item label="发票类型" prop="type">
        <el-select
          clearable
          v-model="form.type"
          placeholder="请选择发票类型"
          class="ele-fluid"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.CO_PREPAID_INVOICE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发票抬头" prop="title">
        <el-input v-model="form.title" placeholder="请输入发票抬头" disabled />
      </el-form-item>
      <el-form-item label="纳税人识别号" prop="taxpayerNo">
        <el-input
          v-model="form.taxpayerNo"
          placeholder="请输入纳税人识别号"
          disabled
        />
      </el-form-item>
      <el-form-item label="开票金额" prop="amount">
        <el-input v-model="form.amount" placeholder="请输入开票金额" disabled />
      </el-form-item>
      <el-form-item label="开票内容" prop="content">
        <el-input v-model="form.content" placeholder="请输入开票内容" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input v-model="form.contact" placeholder="请输入联系方式" />
      </el-form-item>
      <el-form-item label="邮箱地址" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱地址" />
      </el-form-item>
      <el-form-item label="开票状态" prop="status">
        <el-select
          clearable
          v-model="form.status"
          placeholder="请选择开票状态"
          class="ele-fluid"
          disabled
        >
          <el-option
            v-for="dict in getIntDictOptions(
              DICT_TYPE.CO_PREPAID_INVOICE_STATUS
            )"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PrepaidInvoiceApi from '@/api/lease/prepaidinvoice';
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';
  import { useFormData } from '@/utils/use-form-data';

  /** 预存金发票 表单 */
  defineOptions({ name: 'PrepaidInvoiceForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    prepaidId: undefined,
    customerId: undefined,
    accountCode: undefined,
    customerName: undefined,
    socialCreditCode: undefined,
    invoiceNo: undefined,
    type: undefined,
    title: undefined,
    taxpayerNo: undefined,
    amount: undefined,
    content: undefined,
    contact: undefined,
    email: undefined,
    orderNum: undefined,
    status: undefined,
    thirdInvoiceNo: undefined,
    isInvoice: undefined,
    invoiceTime: undefined,
    callbackMsg: undefined,
    extend: undefined,
    remark: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({});
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await PrepaidInvoiceApi.createPrepaidInvoice(form);
        message.success(t('common.createSuccess'));
      } else {
        await PrepaidInvoiceApi.updatePrepaidInvoice(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await PrepaidInvoiceApi.getPrepaidInvoice(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
