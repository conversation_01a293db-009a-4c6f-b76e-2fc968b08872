<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户编号" prop="accountCode">
              <el-input
                v-model.trim="queryParams.accountCode"
                placeholder="请输入账户编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                v-model.trim="queryParams.customerName"
                placeholder="请输入客户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发票流水号" prop="invoiceNo">
              <el-input
                v-model.trim="queryParams.invoiceNo"
                placeholder="请输入发票流水号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:prepaid-invoice:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_PREPAID_INVOICE_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_PREPAID_INVOICE_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:prepaid-invoice:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:prepaid-invoice:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="openLineEdit(row)"
            v-permission="['lease:prepaid-invoice:update']"
          >
            明细
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:prepaid-invoice:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:prepaid-invoice:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <PrepaidInvoiceForm v-model="showEdit" :data="current" @done="reload" />
    <PrepaidInvoiceLineForm
      v-model="showLineEdit"
      :data="current"
      @done="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as PrepaidInvoiceApi from '@/api/lease/prepaidinvoice';
  import PrepaidInvoiceForm from './PrepaidInvoiceForm.vue';
  import PrepaidInvoiceLineForm from './PrepaidInvoiceLineForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';

  /** 预存金发票 列表 */
  defineOptions({ name: 'PrepaidInvoiceIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    prepaidId: undefined,
    customerId: undefined,
    accountCode: undefined,
    customerName: undefined,
    socialCreditCode: undefined,
    invoiceNo: undefined,
    type: undefined,
    title: undefined,
    taxpayerNo: undefined,
    amount: undefined,
    content: undefined,
    contact: undefined,
    email: undefined,
    orderNum: undefined,
    status: undefined,
    thirdInvoiceNo: undefined,
    isInvoice: undefined,
    invoiceTime: [],
    callbackMsg: undefined,
    extend: undefined,
    remark: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'accountCode',
      label: '账户编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerName',
      label: '客户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'socialCreditCode',
      label: '统一社会信用代码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'invoiceNo',
      label: '发票流水号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'type',
      label: '发票类型',
      align: 'center',
      minWidth: 110,
      slot: 'type'
    },
    {
      prop: 'title',
      label: '发票抬头',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'taxpayerNo',
      label: '纳税人识别号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'amount',
      label: '开票金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'content',
      label: '开票内容',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contact',
      label: '联系方式',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'email',
      label: '邮箱地址',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '开票状态, 1-开票中; 2-成功; 3-失败;',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'thirdInvoiceNo',
      label: '三方开票编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 是否显示明细弹窗 */
  const showLineEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return PrepaidInvoiceApi.getPrepaidInvoicePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开发票行弹窗 */
  const openLineEdit = (row) => {
    current.value = row ?? null;
    showLineEdit.value = true;
  };

  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await PrepaidInvoiceApi.deletePrepaidInvoice(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await PrepaidInvoiceApi.exportPrepaidInvoice(queryParams);
      download.excel(data, '预存金发票.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
