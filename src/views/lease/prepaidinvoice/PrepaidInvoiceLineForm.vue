<template>
  <ele-modal
    :width="640"
    title="发票明细"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :pagination="false"
      :toolbar="false"
      :empty-props="false"
    >
      <template #type="{ row }">
        <dict-data
          type="tag"
          :code="DICT_TYPE.CO_PREPAID_ORDER_TYPE"
          :model-value="row.type"
        />
      </template>
    </ele-pro-table>
    <template #footer>
      <el-button @click="updateModelValue(false)">返回</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import * as PrepaidInvoiceApi from '@/api/lease/prepaidinvoice';
  import { DICT_TYPE } from '@/utils/dict';
  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 用户 */
    data: Object
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'orderNo',
      label: '账单编号',
      minWidth: 100
    },
    {
      prop: 'type',
      label: '账单类型',
      minWidth: 100,
      slot: 'type'
    },
    {
      prop: 'amount',
      label: '金额',
      minWidth: 110
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 查询 */
  const query = async () => {
    const result = await PrepaidInvoiceApi.getPrepaidInvoiceLine(props.data.id);
    datasource.value = result;
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue && props.data) {
        query();
      } else {
        selections.value = [];
      }
    }
  );
</script>
