import { defineComponent, reactive, toRefs, ref } from 'vue';
import { useRouter } from 'vue-router';
import { dateFormatter } from '@/utils/formatTime';
export default defineComponent({
  props: {
    refund: {
      type: Boolean,
      default: false
    },
    formInfo: {
      type: Object,
      default: {}
    }
  },
  setup(props, { emit }) {
    //获取表单form元素
    const ruleForm = ref(null);
    const router = useRouter();
    const _that = reactive({});
    //关闭弹窗
    const close = () => {
      emit('close');
    };
    return {
      ...toRefs(_that),
      close
    };
  }
});
