<template>
  <el-dialog v-model="refund" title="退款" width="800">
    <el-form
      ref="fromRef"
      :inline="true"
      :model="formInfo"
      label-position="left"
      :label-width="80"
    >
      <ele-card header="账户信息">
        <el-form-item label="账户编号"></el-form-item>
        <el-form-item label="客户名称"></el-form-item>
        <el-form-item label="账户余额"></el-form-item>
      </ele-card>
      <ele-card header="退款信息">
        <el-form-item label="退款金额">
          <el-input class="input-200" type="number">
            <template #append>全部</template>
          </el-input>
        </el-form-item>
        <el-form-item label="退款方式">
          <el-select placeholder="请选择" class="input-200">
            <el-option label="银行转账" :value="1"></el-option>
            <el-option label="现金" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            class="input-200"
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="开始时间"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            class="input-200"
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="结束时间"
          />
        </el-form-item>

        <el-form-item label="退款账号">
          <el-input placeholder="请输入" class="input-400"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            class="input-400"
            rows="5"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </ele-card>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button type="primary">确认</el-button>
        <el-button type="primary" @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script src="./index.js"></script>
<style lang="scss">
  .input-200 {
    width: 200px !important;
  }
  .input-400 {
    width: 510px;
  }
</style>
