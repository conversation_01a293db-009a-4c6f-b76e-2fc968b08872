import { defineComponent, reactive, toRefs, ref } from 'vue';
import { useRouter } from 'vue-router';
import { dateFormatter } from '@/utils/formatTime';
export default defineComponent({
  props: {
    recharge: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    //获取表单form元素
    const ruleForm = ref(null);
    const router = useRouter();
    const _that = reactive({
      formInfo: {}
    });
    //关闭弹窗
    const close = () => {
      emit('close');
    };
    return {
      ...toRefs(_that),
      close
    };
  }
});
