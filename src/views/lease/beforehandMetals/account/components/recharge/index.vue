<template>
  <el-dialog v-model="recharge" title="预存金充值" :width="1200">
    <el-form ref="fromRef" :inline="true" :model="formInfo" :label-width="80">
      <ele-card header="客户信息">
        <el-form-item label="客户名称">
          <el-select placeholder="请选择" class="input-200">
            <el-option label="人才客户" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系人">
          <el-input
            class="input-200"
            disabled
            v-model="formInfo.rechargeAmount"
          />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input
            class="input-200"
            disabled
            v-model="formInfo.rechargeAmount"
          />
        </el-form-item>
        <el-form-item label="账户编号">
          <el-input
            class="input-200"
            disabled
            v-model="formInfo.rechargeAmount"
          />
        </el-form-item>
        <el-form-item label="账户状态">
          <el-input
            class="input-200"
            disabled
            v-model="formInfo.rechargeAmount"
          />
        </el-form-item>
        <el-form-item label="账户余额">
          <el-input
            class="input-200"
            disabled
            v-model="formInfo.rechargeAmount"
          />
        </el-form-item>
      </ele-card>
      <ele-card header="充值信息">
        <el-form-item label="充值金额">
          <el-input
            class="input-200"
            type="number"
            v-model="formInfo.rechargeAmount"
            placeholder="请输入充值金额"
          >
            <template #append>全部</template>
          </el-input>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            class="input-200"
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="开始时间"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            class="input-200"
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            placeholder="结束时间"
          />
        </el-form-item>
      </ele-card>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button type="primary">确认</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script src="./index.js"></script>
<style lang="scss">
  .input-200 {
    width: 200px !important;
  }
  .input-400 {
    width: 510px;
  }
</style>
