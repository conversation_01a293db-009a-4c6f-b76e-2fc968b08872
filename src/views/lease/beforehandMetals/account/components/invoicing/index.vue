<template>
  <el-dialog v-model="invoicing" title="开票" :width="1200">
    <el-form ref="fromRef" :inline="true" :model="formInfo" :label-width="80">
      <ele-card header="客户信息">
        <el-form-item label="客户名称">
          <el-select placeholder="请选择" class="input-200">
            <el-option label="人才客户" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开票金额">
          <el-input class="input-200" disabled />
        </el-form-item>
      </ele-card>
      <ele-card header="扣款信息">
        <el-form-item label="开票状态">
          <el-select placeholder="请选择" class="input-200">
            <el-option label="全部" :value="0"></el-option>
            <el-option label="已开发票" :value="1"></el-option>
            <el-option label="未开发票" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </ele-card>
    </el-form>
    <el-table :data="dataList" border>
      <el-table-column type="selection" width="55" />
      <el-table-column
        label="扣款时间"
        prop="createTime"
        align="center"
      ></el-table-column>
      <el-table-column
        label="账单编号"
        prop="invoiceNo"
        align="center"
      ></el-table-column>
      <el-table-column
        label="扣款金额"
        prop="amount"
        align="center"
      ></el-table-column>
      <el-table-column label="开票状态" prop="status" align="center">
        <template #default="{ row }">
          {{ statusName[row.status] }}
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div style="text-align: center">
        <el-button type="primary">确认</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script src="./index.js"></script>
<style lang="scss">
  .input-200 {
    width: 200px !important;
  }
  .input-400 {
    width: 510px;
  }
</style>
