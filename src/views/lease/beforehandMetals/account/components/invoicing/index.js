import { defineComponent, reactive, toRefs, ref } from 'vue';
import { useRouter } from 'vue-router';
import { dateFormatter } from '@/utils/formatTime';
import * as accountServer from './../../index.server';
export default defineComponent({
  props: {
    invoicing: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    //获取表单form元素
    const ruleForm = ref(null);
    const router = useRouter();
    const _that = reactive({
      formInfo: {},
      dataList: [], //发票列表
      statusName: { 1: '开票中', 2: '成功', 3: '失败' } //发票状态
    });
    //初始化预存金发票列表
    const getAccountInvoiceList = async () => {
      let res = await accountServer.getAccountInvoiceList();
      if (res.code === 0) {
        _that.dataList = res.data.list;
      }
    };
    //关闭弹窗
    const close = () => {
      emit('close');
    };
    return {
      ...toRefs(_that),
      close
    };
  }
});
