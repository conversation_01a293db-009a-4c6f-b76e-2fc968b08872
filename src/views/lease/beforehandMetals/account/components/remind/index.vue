<template>
  <el-dialog v-model="remind" title="提醒设置" width="900">
    <el-form ref="fromRef" :inline="true" :label-width="140">
      <ele-card header="账户信息">
        <el-form-item label="账户编号"></el-form-item>
        <el-form-item label="客户名称"></el-form-item>
        <el-form-item label="账户余额"></el-form-item>
      </ele-card>
      <ele-card header="提醒信息">
        <el-form-item label="提醒金额">
          <el-input type="number" class="input-200" placeholder="请输入金额">
            <template #append>全部</template>
          </el-input>
        </el-form-item>
        <el-form-item label="提醒频率">
          <div class="frequency-box">
            1次/
            <el-input type="number" class="input-200" placeholder="请输入">
              <template #append>小时</template>
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            format="YYYY/MM/DD"
            value-format="YYYY/MM/DD"
            type="date"
            class="input-200"
            placeholder="开始时间"
          />
        </el-form-item>
        <el-form-item label="提醒对象">
          <div class="frequency-box">
            <div class="flex-box">
              <el-select placeholder="请选择" class="input-120">
                <el-option label="客户" :value="1"></el-option>
                <el-option label="工作人员" :value="2"></el-option>
              </el-select>
            </div>
            <div class="flex-box">
              <el-select placeholder="请选择" class="input-120">
                <el-option label="人才科技" :value="1"></el-option>
                <el-option label="客服A" :value="2"></el-option>
                <el-option label="客服B" :value="2"></el-option>
              </el-select>
            </div>
            <div class="flex-box">
              <div> 提醒方式： </div>
              <el-select placeholder="请选择" class="input-120">
                <el-option label="站内信" :value="1"></el-option>
                <el-option label="短信" :value="2"></el-option>
                <el-option label="邮箱" :value="2"></el-option>
              </el-select>
            </div>
            <div class="flex-box">
              <el-button type="primary"> + </el-button>
              <el-button> - </el-button>
            </div>
          </div>
        </el-form-item>
      </ele-card>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button type="primary">确认</el-button>
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
  .frequency-box {
    display: flex;
    align-items: center;
  }
  .flex-box {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
</style>
<script src="./index.js"></script>
<style lang="scss">
  .input-120 {
    width: 120px;
  }
  .input-200 {
    width: 200px !important;
  }
  .input-400 {
    width: 510px;
  }
</style>
