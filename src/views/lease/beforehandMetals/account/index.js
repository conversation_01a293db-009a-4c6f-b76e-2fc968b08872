import { defineComponent, reactive, toRefs, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { dateFormatter } from '@/utils/formatTime';
import refund from './components/refund/index.vue';
import remind from './components/remind/index.vue';
import recharge from './components/recharge/index.vue';
import invoicing from './components/invoicing/index.vue';
import * as accountServer from './index.server';
export default defineComponent({
  components: { refund, remind, recharge, invoicing },
  setup() {
    //获取表单form元素
    const ruleForm = ref(null);
    const router = useRouter();
    const _that = reactive({
      form: { pageNo: 1, pageSize: 20 },
      columns: [
        {
          prop: 'customerCode',
          label: '账户编号',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'customerName',
          label: '客户名称',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '联系人姓名',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '联系电话',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'lockAmount',
          label: '账户余额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'rechargeAmount',
          label: '累计充值',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'deductAmount',
          label: '累计扣款',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'refundAmount',
          label: '累计退款',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 140,
          align: 'center',
          formatter: dateFormatter
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 180,
          align: 'center',
          slot: 'action',
          hideInPrint: true
        }
      ],
      datasource: [],
      //是否退款
      isRefund: false,
      //是否设置提醒
      isRemind: false,
      //充值
      isRecharge: false,
      //开票
      isInvoicing: false
    });
    //初始化数据显示
    const getAccountPage = async () => {
      let res = await accountServer.getAccountPage(_that.form);
      if (res.code === 0) {
        _that.datasource = res.data.list;
      }
    };
    //查询
    const search = () => {
      getAccountPage();
    };
    //重置
    const reset = () => {
      _that.form = { pageNo: 1, pageSize: 50 };
      getAccountPage();
    };
    //提醒设置
    const getRemindSetting = (row) => {};
    //退款
    const getAccountRefund = (row) => {};
    //详情
    const getAccountDetial = (row) => {};
    //关闭弹窗
    const close = () => {
      _that.isRefund = false;
      _that.isRemind = false;
      _that.isRecharge = false;
      _that.isInvoicing = false;
    };

    onMounted(() => {
      getAccountPage();
    });
    return {
      ...toRefs(_that),
      getRemindSetting,
      getAccountRefund,
      getAccountDetial,
      close,
      search,
      reset
    };
  }
});
