<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" :model="form">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称">
              <el-input
                clearable
                v-model.trim="form.roleName"
                placeholder="请输入客户名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户编号">
              <el-input
                clearable
                v-model.trim="form.roleKey"
                placeholder="请输入客户编号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            @click="isRecharge = true"
          >
            充值
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            @click="isInvoicing = true"
          >
            开票
          </el-button>
          <el-button type="primary" class="ele-btn-icon"> 导出 </el-button>
        </template>
        <template #action="{ row }">
          <el-button type="primary" link @click="getRemindSetting(row)">
            提醒设置
          </el-button>
          <el-button type="primary" link @click="getAccountRefund(row)">
            退款
          </el-button>
          <el-button type="primary" link @click="getAccountDetial(row)">
            详情
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
  <!--充值-->
  <recharge :recharge="isRecharge" @close="close"></recharge>
  <!--开票--->
  <invoicing :invoicing="isInvoicing" @close="close"></invoicing>
  <!--退款--->
  <refund :refund="isRefund" @close="close"></refund>
  <!--提醒设置-->
  <remind :remind="isRemind" @close="close"></remind>
</template>
<script src="./index.js"></script>
