import request from '@/utils/request/server';
import { download, toFormData, checkDownloadRes } from '@/utils/common';
import qs from 'qs';
//账户列表
export const getAccountPage = async (params) => {
  return await request.get({ url: '/lease/prepaid-info', params });
};
//账号充值
export const getAccountAdd = async (params) => {
  return await request.post({ url: '/lease/prepaid-info', params });
};
//退款
export const getAccountRefund = async (params) => {
  return await request.post({ url: '/lease/landlord', params });
};
//预存金发票列表
export const getAccountInvoiceList = async (params) => {
  return await request.get({ url: '/lease/prepaid-invoice', params });
};

//客户信息列表，进行客户信息查询
export const getCustomerAccountList = async (params) => {
  return await request.get({
    url: '/lease/prepaid-info/get-customer-simple',
    params
  });
};
