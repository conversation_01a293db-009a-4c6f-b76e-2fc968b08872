<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" :model="form">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称">
              <el-input
                clearable
                v-model.trim="form.roleName"
                placeholder="请输入客户名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单编号">
              <el-input
                clearable
                v-model.trim="form.roleKey"
                placeholder="请输入账单编号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单类型">
              <el-select placeholder="请选择">
                <el-option label="全部" value="all"></el-option>
                <el-option label="充值" :value="1"></el-option>
                <el-option label="扣款" :value="2"></el-option>
                <el-option label="退款" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #toolbar>
          <el-button type="primary" class="ele-btn-icon"> 导出 </el-button>
        </template>
        <template #opType="{ row }">
          {{ opTypeInfo[row.opType] }}
        </template>
        <template #status="{ row }">
          {{ statusInfo[row.status] }}
        </template>
        <template #action="{ row }">
          <el-button type="primary" link @click="getLedgerDetial(row)">
            详情
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script src="./index.js"></script>
