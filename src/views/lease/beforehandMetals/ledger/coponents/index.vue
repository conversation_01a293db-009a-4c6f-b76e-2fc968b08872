<template>
  <el-dialog v-model="refund" title="提醒设置" width="500">
    <el-form ref="fromRef" :inline="true" label-width="100px">
      <ele-card header="账户信息">
        <el-form-item label="账户编号"></el-form-item>
        <el-form-item label="客户名称"></el-form-item>
        <el-form-item label="联系人名称"></el-form-item>
        <el-form-item label="联系人电话"></el-form-item>
        <el-form-item label="账户余额"></el-form-item>
        <el-form-item label="账户状态"></el-form-item>
        <el-form-item label="创建时间"></el-form-item>
        <el-form-item label="创建人"></el-form-item>
      </ele-card>
      <ele-card header="账单明细">
        <el-form-item label="账单编号"></el-form-item>
        <el-form-item label="账单类型"></el-form-item>
        <el-form-item label="应收金额"></el-form-item>
        <el-form-item label="实收金额"></el-form-item>
        <el-form-item label="开始时间"></el-form-item>
        <el-form-item label="结束时间"></el-form-item>
        <el-form-item label="交易时间"></el-form-item>
        <el-form-item label="站单状态"></el-form-item>
        <el-form-item label="收款方式"></el-form-item>
        <el-form-item label="收款账号"></el-form-item>
        <el-form-item label="收款人"></el-form-item>
        <el-form-item label="确认人"></el-form-item>
        <el-form-item label="创建时间"></el-form-item>
        <el-form-item label="创建人"></el-form-item>
        <el-form-item label="明细"></el-form-item>
      </ele-card>
    </el-form>
    <template #footer>
      <div style="text-align: center">
        <el-button type="primary">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
  .frequency-box {
    display: flex;
    align-items: center;
  }
  .flex-box {
    display: flex;
    align-items: center;
  }
</style>
