import { defineComponent, reactive, toRefs, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { dateFormatter } from '@/utils/formatTime';
import * as ledgerServer from './index.server';
export default defineComponent({
  setup() {
    //获取表单form元素
    const ruleForm = ref(null);
    const router = useRouter();
    const _that = reactive({
      form: { pageNo: 1, pageSize: 50 },
      columns: [
        {
          prop: 'customerCode',
          label: '账户编号',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'customerName',
          label: '客户名称',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '账单编号',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'opType',
          label: '账单类型',
          align: 'center',
          minWidth: 110,
          slot: 'opType'
        },
        {
          prop: 'amount',
          label: '应收金额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'actualAmount',
          label: '实收金额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '应退金额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'name',
          label: '实退金额',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'startTime',
          label: '开始时间',
          width: 140,
          align: 'center',
          formatter: dateFormatter
        },
        {
          prop: 'endTime',
          label: '结束时间',
          width: 140,
          align: 'center',
          formatter: dateFormatter
        },
        {
          prop: 'tradeTime',
          label: '更新时间',
          width: 140,
          align: 'center',
          formatter: dateFormatter
        },
        {
          prop: 'status',
          label: '账单状态',
          width: 140,
          align: 'center',
          slot: 'status'
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 180,
          align: 'center',
          slot: 'action',
          hideInPrint: true
        }
      ],
      datasource: [],
      opTypeInfo: { 1: '充值', 2: '扣款', 3: '退款' }, //类型
      statusInfo: { 1: '待确认', 2: '处理中', 3: '成功', 4: '失败' } //状态
    });
    //初始化数据列表
    const getLedgerPage = async () => {
      let res = await ledgerServer.getLedgerPage(_that.form);
      if (res.code === 0) {
        _that.datasource = res.data.list;
      }
    };
    //查询
    const search = () => {
      getLedgerPage();
    };
    //重置
    const reset = () => {
      _that.form = { pageNo: 1, pageSize: 50 };
      getLedgerPage();
    };
    //提醒设置
    const getLedgerDetial = (row) => {};
    onMounted(() => {
      getLedgerPage();
    });
    return {
      ...toRefs(_that),
      getLedgerDetial,
      search,
      reset
    };
  }
});
