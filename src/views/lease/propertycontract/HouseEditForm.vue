<template>
  <el-dialog
    v-model="dialogVisible"
    title="房产信息"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="园区" prop="parkId">
            <el-select
              v-model="form.parkId"
              filterable
              remote
              :remote-method="remoteMethodPark"
              :loading="loadingPark"
              placeholder="请选择园区"
              class="ele-fluid"
              @change="parkChange"
            >
              <el-option
                v-for="item in optionsPark"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="楼栋" prop="buildingId">
            <el-select
              v-model="form.buildingId"
              filterable
              remote
              :remote-method="remoteMethodBuilding"
              :loading="loadingBuilding"
              placeholder="请选择楼栋"
              class="ele-fluid"
              @change="buildingChange"
            >
              <el-option
                v-for="item in optionsBuilding"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="楼层" prop="floorId">
            <el-select
              v-model="form.floorId"
              filterable
              remote
              :remote-method="remoteMethodFloor"
              :loading="loadingFloor"
              placeholder="请选择楼层"
              class="ele-fluid"
              @change="floorChange"
            >
              <el-option
                v-for="item in optionsFloor"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="房间" prop="roomId">
            <el-select
              v-model="form.roomId"
              filterable
              remote
              :remote-method="remoteMethodRoom"
              :loading="loadingRoom"
              placeholder="请选择房间"
              class="ele-fluid"
              @change="roomChange"
            >
              <el-option
                v-for="item in optionsRoom"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { EleMessage } from 'ele-admin-plus/es';
import { getLongHouseLov } from '@/api/lease/longhouse/index';

const props = defineProps({
  modelValue: Boolean,
  data: Object
});

const emit = defineEmits(['update:modelValue', 'done']);

/** 是否显示弹窗 */
const dialogVisible = ref(false);

/** 表单实例 */
const formRef = ref<FormInstance | null>(null);

/** 表单数据 */
const form = reactive({
  id: undefined,
  parkId: undefined,
  parkInfo: undefined,
  buildingId: undefined,
  buildingInfo: undefined,
  floorId: undefined,
  floorInfo: undefined,
  roomId: undefined,
  roomNo: undefined,
  roomNum: undefined,
  area: undefined
});

/** 表单验证规则 */
const rules = reactive({
  parkId: [{ required: true, message: '请选择园区', trigger: 'change' }],
  buildingId: [{ required: true, message: '请选择楼栋', trigger: 'change' }],
  floorId: [{ required: true, message: '请选择楼层', trigger: 'change' }],
  roomId: [{ required: true, message: '请选择房间', trigger: 'change' }]
});

//园区下拉框
const optionsPark = ref([]);
const loadingPark = ref(false);

//楼栋下拉框
const optionsBuilding = ref([]);
const loadingBuilding = ref(false);

//楼层下拉框
const optionsFloor = ref([]);
const loadingFloor = ref(false);

//房间下拉框
const optionsRoom = ref([]);
const loadingRoom = ref(false);

//监听弹窗显示
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      // 初始化下拉框
      loadPark();
      if (props.data) {
        Object.assign(form, props.data);
      }
    }
  }
);

//监听弹窗关闭
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:modelValue', val);
  }
);

/** 关闭弹窗 */
const close = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

/** 表单提交 */
const submit = () => {
  formRef.value?.validate?.((valid) => {
    if (valid) {
      emit('done', form);
      close();
    }
  });
};

//加载园区
const loadPark = () => {
  getLongHouseLov({ type: 'park' }).then((res) => {
    optionsPark.value = res.data;
  });
};

//加载楼栋
const loadBuilding = (parkId) => {
  getLongHouseLov({ type: 'building', parkId: parkId }).then((res) => {
    optionsBuilding.value = res.data;
  });
};

//加载楼层
const loadFloor = (buildingId) => {
  getLongHouseLov({ type: 'floor', buildingId: buildingId }).then((res) => {
    optionsFloor.value = res.data;
  });
};

//加载房间
const loadRoom = (floorId) => {
  getLongHouseLov({ type: 'room', floorId: floorId }).then((res) => {
    optionsRoom.value = res.data;
  });
};

//园区选择
const parkChange = (val) => {
  form.buildingId = undefined;
  form.buildingInfo = undefined;
  form.floorId = undefined;
  form.floorInfo = undefined;
  form.roomId = undefined;
  form.roomNo = undefined;
  form.roomNum = undefined;
  form.area = undefined;
  if (val) {
    loadBuilding(val);
    const park = optionsPark.value.find((item) => item.id === val);
    if (park) {
      form.parkInfo = park.name;
    }
  }
};

//楼栋选择
const buildingChange = (val) => {
  form.floorId = undefined;
  form.floorInfo = undefined;
  form.roomId = undefined;
  form.roomNo = undefined;
  form.roomNum = undefined;
  form.area = undefined;
  if (val) {
    loadFloor(val);
    const building = optionsBuilding.value.find((item) => item.id === val);
    if (building) {
      form.buildingInfo = building.name;
    }
  }
};

//楼层选择
const floorChange = (val) => {
  form.roomId = undefined;
  form.roomNo = undefined;
  form.roomNum = undefined;
  form.area = undefined;
  if (val) {
    loadRoom(val);
    const floor = optionsFloor.value.find((item) => item.id === val);
    if (floor) {
      form.floorInfo = floor.name;
    }
  }
};

//房间选择
const roomChange = (val) => {
  if (val) {
    const room = optionsRoom.value.find((item) => item.id === val);
    if (room) {
      form.roomNo = room.name;
      form.roomNum = room.code;
      form.area = room.area;
    }
  }
};

//远程搜索园区
const remoteMethodPark = (query) => {
  if (query !== '') {
    loadingPark.value = true;
    setTimeout(() => {
      loadingPark.value = false;
      getLongHouseLov({ type: 'park', name: query }).then((res) => {
        optionsPark.value = res.data;
      });
    }, 200);
  } else {
    optionsPark.value = [];
  }
};

//远程搜索楼栋
const remoteMethodBuilding = (query) => {
  if (query !== '') {
    loadingBuilding.value = true;
    setTimeout(() => {
      loadingBuilding.value = false;
      getLongHouseLov({
        type: 'building',
        parkId: form.parkId,
        name: query
      }).then((res) => {
        optionsBuilding.value = res.data;
      });
    }, 200);
  } else {
    optionsBuilding.value = [];
  }
};

//远程搜索楼层
const remoteMethodFloor = (query) => {
  if (query !== '') {
    loadingFloor.value = true;
    setTimeout(() => {
      loadingFloor.value = false;
      getLongHouseLov({
        type: 'floor',
        buildingId: form.buildingId,
        name: query
      }).then((res) => {
        optionsFloor.value = res.data;
      });
    }, 200);
  } else {
    optionsFloor.value = [];
  }
};

//远程搜索房间
const remoteMethodRoom = (query) => {
  if (query !== '') {
    loadingRoom.value = true;
    setTimeout(() => {
      loadingRoom.value = false;
      getLongHouseLov({
        type: 'room',
        floorId: form.floorId,
        name: query
      }).then((res) => {
        optionsRoom.value = res.data;
      });
    }, 200);
  } else {
    optionsRoom.value = [];
  }
};
</script> 