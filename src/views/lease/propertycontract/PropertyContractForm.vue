<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 合同维护 </span>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="140px"
    @submit.prevent=""
    :disabled="true"
  >
    <ele-card header="基础信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同编号" prop="code">
            <el-input
              maxlength="40"
              disabled
              clearable
              v-model="form.code"
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同名称" prop="name">
            <el-input
              maxlength="40"
              clearable
              v-model="form.name"
              placeholder="请输入合同名称"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="签订渠道" prop="signingChannel">
            <el-radio-group v-model="form.signingChannel">
              <el-radio :value="1" label="线上" />
              <el-radio :value="2" label="线下" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同签订日期" prop="signingDate">
            <el-date-picker
              type="date"
              v-model="form.signingDate"
              value-format="x"
              placeholder="请选择合同签订日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁期限" prop="dateTime">
            <el-date-picker
              v-model="form.dateTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁面积" prop="rentalArea">
            <el-input-number
              v-model="form.rentalArea"
              :min="0"
              :precision="2"
              :step="0.1"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="物业费单价" prop="unitPrice">
            <el-input-number
              v-model="form.unitPrice"
              :min="0"
              :precision="2"
              :step="0.1"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="物业费单位" prop="rentalUnit">
            <el-select
              v-model="form.rentalUnit"
              class="ele-fluid"
            >
              <el-option :value="1" label="元/平米/天" />
              <el-option :value="2" label="元/平米/月" />
              <el-option :value="3" label="元/平米/年" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经办人" prop="name">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.handlerName"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="customerType">
            <el-radio-group
              v-model="form.customerType"
              disabled
              @change="loadUserCustomer"
            >
              <el-radio :value="1" label="企业" />
              <el-radio :value="2" label="个人" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户" prop="customerName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.customerName"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="来源编号" prop="sourceCode">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.sourceCode"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="来源名称" prop="sourceName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.sourceName"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="提交人" prop="submmitByName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.submmitByName"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="提交时间" prop="submmitTime">
            <el-date-picker
              type="date"
              v-model="form.submmitTime"
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="form.remark"
              placeholder="请输入备注"
              :rows="3"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card style="min-height: 350px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
        <el-tab-pane label="房产信息" name="0">
          <div style="overflow: auto">
            <!-- <div class="file-box">
              <el-button type="primary" @click="getHouseDel(1, '', null)">
                添加
              </el-button>
            </div> -->
            <el-table
              :data="form.propertyContractHouses"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="园区" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.parkInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼栋" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.buildingInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼层" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.floorInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间号" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间编码" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNum"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="面积(㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.area"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="单价(元/㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.price"></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="操作">
                <template #default="{ $index, row }">
                  <el-button
                    type="primary"
                    link
                    @click="getHouseDel(2, $index, null)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="收款信息" name="1">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getPaymentPlanDel(1, '')">
                添加
              </el-button>
              <el-button type="primary" @click="importData"> 导入 </el-button>
              <!-- <el-button type="primary" @click="generatePlanConfirm()">
                生成收款计划
              </el-button> -->
            </div>
            <el-table
              :data="form.propertyContractPaymentPlans"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="开始日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`propertyContractPaymentPlans.${$index}.startDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入开始日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.startDate"
                      value-format="x"
                      placeholder="请选择开始日期"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="结束日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`propertyContractPaymentPlans.${$index}.endDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入结束日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.endDate"
                      value-format="x"
                      placeholder="请选择结束日期"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="收款日" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`propertyContractPaymentPlans.${$index}.paymentDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入结束日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.paymentDate"
                      value-format="x"
                      placeholder="请选择收款日"
                      class="ele-fluid"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="物业费(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`propertyContractPaymentPlans.${$index}.paymentAmt`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入物业费',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      :min="0"
                      :precision="2"
                      v-model="row.paymentAmt"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <!-- <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getPaymentPlanDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件信息" name="2">
          <div style="overflow: auto">
            <div class="file-box">
              <el-upload :show-file-list="false" :http-request="httpRequest">
                <el-button type="primary">上传文件</el-button>
              </el-upload>
            </div>
            <el-table
              :data="form.propertyContractFiles"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="文件名" prop="name"></el-table-column>
              <el-table-column label="大小" prop="size">
                <template #default="scope">
                  <span>{{ scope.row.size }} KB</span>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="uploadTime">
                <template #default="scope">
                  <span>{{ formatDate2(scope.row.uploadTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-link
                    type="primary"
                    :underline="false"
                    download
                    :href="scope.row.url"
                  >
                    下载
                  </el-link>
                  <!-- <el-divider direction="vertical" />
                  <el-link
                    type="danger"
                    :underline="false"
                    @click="deleteFile(scope.row)"
                  >
                    删除
                  </el-link> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </el-form>

  <!-- 底部工具栏 -->
  <ele-bottom-bar>
    <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
      <span>{{ validMsg }}</span>
    </ele-text>
    <template #extra>
      <el-button type="primary" :loading="loading" class="ml-2" @click="submit"
        >保存</el-button
      >
    </template>
  </ele-bottom-bar>
  <HouseEditForm v-model="showHouseEdit" :data="current" @done="recordHandle" />
  <DataImport v-model="showImport" @done="excelImport" />
</template>

<script lang="ts" setup>
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate2 } from '@/utils/formatTime';
  import HouseEditForm from './HouseEditForm.vue';
  import { getCustomerSimpleList } from '@/api/lease/attract/index';
  import { updateFile, getGeneratePlan } from '@/api/lease/contract';
  import { getSimpleUserByTypeList } from '@/api/system/user';
  import DataImport from './DataImport.vue';
  import {
    updatePropertyContract,
    createPropertyContract,
    getPropertyContractDetail
  } from '@/api/lease/propertycontract';
  import { getLongHouseLov } from '@/api/lease/longhouse/index';

  const activeName = ref('0');
  const props = defineProps({
    data: Object
  });

  /** 加载状态 */
  const loading = ref(false);
  const emit = defineEmits(['done']);
  const isUpdate = ref(false);
  /** 是否显示编辑弹窗 */
  const showHouseEdit = ref(false);
  /** 当前编辑数据 */
  const current = ref(null);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);
  /**导入弹窗 */
  const showImport = ref(false);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    code: undefined,
    name: undefined,
    status: undefined,
    approveStatus: undefined,
    signingDate: undefined,
    signingChannel: 1,
    handler: undefined,
    handlerName: undefined,
    partaId: undefined,
    partaName: undefined,
    customerId: undefined,
    customerType: 1,
    customerName: undefined,
    customerIndustry: undefined,
    customerIdcard: undefined,
    customerPhone: undefined,
    customerEmail: undefined,
    startDate: undefined,
    endDate: undefined,
    unitPrice: undefined,
    rentalUnit: undefined,
    rentalArea: undefined,
    extend: undefined,
    sourceId: undefined,
    contractType: undefined,
    sourceCode: undefined,
    sourceName: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: undefined,
    submmitTime: undefined,
    submmitByName: undefined,
    propertyContractFiles: [],
    propertyContractHouses: [],
    propertyContractPaymentPlans: []
  });

  const importData = () => {
    showImport.value = true;
  };

  /** 表单验证规则 */
  const rules = reactive({
    name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    signingChannel: [
      { required: true, message: '请选择签订渠道', trigger: 'change' }
    ],
    signingDate: [
      { required: true, message: '请选择合同签订日期', trigger: 'change' }
    ],
    handler: [{ required: true, message: '请选择经办人', trigger: 'change' }],
    customerType: [
      { required: true, message: '请选择客户类型', trigger: 'change' }
    ],
    customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
    dateTime: [
      { required: true, message: '请选择租赁期限', trigger: 'change' }
    ],
    unitPrice: [{ required: true, message: '请输入租金单价', trigger: 'blur' }],
    rentalUnit: [
      { required: true, message: '请选择租金单位', trigger: 'change' }
    ],
    rentalArea: [{ required: true, message: '请输入租赁面积', trigger: 'blur' }]
  });

  //上传文件
  const httpRequest = async ({ file }) => {
    let formData = new FormData();
    formData.append('file', file);
    let res = await updateFile(formData);
    form.propertyContractFiles.push({
      fileId: res.data.id,
      name: res.data.name,
      url: res.data.url,
      type: res.data.type,
      size: res.data.size,
      uploadTime: res.data.createTime,
      path: res.data.path
    });
  };
  const deleteFile = async (row) => {
    form.propertyContractFiles.splice(row, 1);
  };

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };

  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      try {
        loading.value = true;
        validMsg.value = '';
        loading.value = true;
        let res = null;
        let params = {
          ...form,
          startDate: '',
          endDate: ''
        };
        if (params.dateTime && params.dateTime.length > 0) {
          params.startDate = params.dateTime[0];
          params.endDate = params.dateTime[1];
        }
        if (!isUpdate.value) {
          res = await createPropertyContract(params);
        } else {
          res = await updatePropertyContract(params);
        }
        if (res) {
          EleMessage.success('提交成功');
          cancle();
        }
      } finally {
        loading.value = false;
      }
    });
  };

  //新增房产信息
  const getHouseDel = async (type, index, row) => {
    if (type == 1) {
      if (row) {
        current.value = { ...row, index: index, isOldFlag: 'Y' };
      } else {
        current.value = null;
      }
      showHouseEdit.value = true;
      // 初始化流程定义详情
      await nextTick();
    } else {
      form.propertyContractHouses.splice(index, 1);
    }
  };

  /** 关闭弹窗 */
  const recordHandle = (data) => {
    if (data) {
      if (data.isOldFlag == 'Y') {
        form.propertyContractHouses[data.index] = data;
      } else {
        form.propertyContractHouses.push(data);
      }
    }
  };

  //新增收款信息
  const getPaymentPlanDel = (type, index) => {
    if (type == 1) {
      form.propertyContractPaymentPlans.push({
        startDate: '',
        endDate: '',
        paymentDate: '',
        paymentAmt: 0
      });
    } else {
      form.propertyContractPaymentPlans.splice(index, 1);
    }
  };

  //生成付款计划
  const generatePlanConfirm = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      try {
        loading.value = true;
        form.propertyContractPaymentPlans = [];
        validMsg.value = '';
        let params = {
          ...form,
          startDate: '',
          endDate: ''
        };
        if (params.dateTime && params.dateTime.length > 0) {
          params.startDate = params.dateTime[0];
          params.endDate = params.dateTime[1];
        }
        let res = await getGeneratePlan(params);
        if (res) {
          EleMessage.success('生成成功');
          cancle();
        }
      } finally {
        loading.value = false;
      }
    });
  };

  const initFormInfo = async () => {
    resetFields();
    await loadData();
  };
  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      isUpdate.value = true;
      loading.value = true;

      try {
        // 获取最新数据
        const response = await getPropertyContractDetail(props.data.id);
        if (response) {
          assignFields(response);
          //设置回显数据
          setInitValue(response);
          // 初始化下拉框
          loadUserCustomer({ customerType: response.customerType });
          //定义一个数组
          form.dateTime = [];
          form.dateTime.push(response.startDate);
          form.dateTime.push(response.endDate);
          selectedValue2.value = response.handler;
          selectedValue1.value = response.customerId;
        }
      } catch (error) {
        console.error('获取合同详情失败', error);
      } finally {
        loading.value = false;
      }
    } else {
      isUpdate.value = false;
      // 初始化下拉框
      loadUserCustomer({ customerType: form.customerType });
    }
  };

  defineExpose({ initFormInfo });
  
  /** 关闭导入弹窗 */
  const excelImport = (data) => {
    if (data) {
      for (let i = 0; i < data.length; i++) {
        form.propertyContractPaymentPlans.push({
          startDate: data[i].startDate,
          endDate: data[i].endDate,
          paymentDate: data[i].paymentDate,
          paymentAmt: data[i].fixedRent
        });
      }
    }
  };

  //设置回显值
  const setInitValue = (response) => {
    //经办人回显
    initHandlerValue.value = {
      id: response.handler,
      nickname: response.handlerName
    };
    //客户回显
    initCustomerValue.value = {
      id: response.customerId,
      customerName: response.customerName,
      contactName: response.contactName
    };
  };

  //页面初始化时，初始化经办人和客户信息
  const loadUserCustomer = (param) => {
    //经办人下拉框初始化
    querySimpleUserList();
    //客户下拉框初始化
    querySimpleCustomerList(param.customerType);
  };

  //------------------------------------------------------经办人逻辑----------------------------------------------------- 
  //经办人回显
  const initHandlerValue = ref();
  //经办人选中的值
  const selectedValue2 = ref();
  //下拉框查询的值
  let allHandlerData = [];
  
  const tableProps2 = reactive<any>({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'username',
        label: '用户名',
        slot: 'nickname',
        align: 'center',
        width: 130
      },
      {
        prop: 'nickname',
        label: '昵称',
        slot: 'nickname',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  /** 选中事件 */
  const onHandlerSelect = (selection) => {
    form.handlerName = selection.nickname;
  };

  /** 经办人下拉框查询 */
  const querySimpleUserList = () => {
    getSimpleUserByTypeList(1).then((data) => {
      tableProps2.datasource = data;
      allHandlerData = data;
    });
  };
    

  /** 经办人筛选输入框值改变事件 */
  const onHandlerFilterChange = (keyword) => {
    tableProps2.datasource = allHandlerData.filter((d) => {
      return d.username?.includes?.(keyword) || d.nickname?.includes?.(keyword);
      
    });
  };

  /** 经办人下拉框展开状态改变事件 */
  const onHandlerVisibleChange = (visible) => {
    if (visible) {
      tableProps2.datasource = allHandlerData;
    }
  };
  

//------------------------------------------------------客户逻辑-----------------------------------------------------

  const customSelectRef = ref(null);

  /** 表格下拉选中值 */
  const selectedValue1 = ref();

  //下拉框查询的值
  let allCustomerData = [];

  //经办人回显
  const initCustomerValue = ref();

  /** 客户表格配置 */
  const tableProps1 = reactive<any>({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'customerName',
        label: '客户名',
        align: 'center',
        width: 170
      },
      {
        prop: 'contactName',
        label: '昵称',
        slot: '联系人',
        align: 'center',
        width: 170
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  /** 选中事件 */
  const onCustomSelect = (selection) => {
    form.customerName = selection.customerName;
    form.contactName = selection.contactName;
    form.customerPhone = selection.contactNumber;
    form.customerEmail = selection.contactEmail;
    form.contactAddress = selection.contactAddress;
    form.customerIdcard = selection.idNumber;
    form.customerIndustry = selection.industry;
  };

  /** 客户重置事件 */
  const resetCustom = () => {
    selectedValue1.value = undefined;
    form.customerId = undefined;
    form.customerName = undefined;
    form.contactName = undefined;
    form.customerPhone = undefined;
    form.customerEmail = undefined;
    form.contactAddress = undefined;
    form.customerIdcard = undefined;
    form.customerIndustry = undefined;
  };

  /** 选择客户类型的时候，要清空客户信息 */
  const onCustomerTypeChange = (value) => {
    resetCustom();
    customSelectRef.value?.tableRef?.reload();
    querySimpleCustomerList(value);
  };

  /** 客户下拉框查询 */
  const querySimpleCustomerList = (customerType) => {
    getCustomerSimpleList(customerType).then((data) => {
      tableProps1.datasource = data;
      allCustomerData = data;
    });
  };

  /** 经办人筛选输入框值改变事件 */
  const onCustomerFilterChange = (keyword) => {
    tableProps1.datasource = allCustomerData.filter((d) => {
      return d.customerName?.includes?.(keyword) || d.contactName?.includes?.(keyword);
      
    });
  };

  /** 经办人下拉框展开状态改变事件 */
  const onCustomerVisibleChange = (visible) => {
    if (visible) {
      tableProps1.datasource = allCustomerData;
    }
  };
</script>
<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>