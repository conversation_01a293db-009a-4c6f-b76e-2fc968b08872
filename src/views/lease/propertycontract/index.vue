<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card v-if="showStep == 1" :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同编码" label-width="100px" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入合同编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同名称" label-width="100px" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入合同名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同状态" label-width="100px" prop="status">
              <el-select
                clearable
                v-model="queryParams.status"
                placeholder=""
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item
              label="父合同编码"
              prop="sourceCode"
              label-width="100px"
            >
              <el-input
                v-model.trim="queryParams.sourceCode"
                placeholder="请输入合同编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item
              label="父合同名称"
              prop="sourceName"
              label-width="100px"
            >
              <el-input
                v-model.trim="queryParams.sourceName"
                placeholder="请输入合同名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 表格 -->
    <ele-card
      v-if="showStep == 1"
      flex-table
      :body-style="{ paddingTop: '8px' }"
    >
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        v-model:selections="selections"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:property-contract:create']"
            :icon="Plus"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="UploadOutlined"
            v-permission="['lease:property-contract:update']"
            @click="submitBatch(null)"
          >
            提交
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:property-contract:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #type="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_TYPE"
            :model-value="row.type"
          />
        </template>
        <template #rentalPlan="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_RENTAL_PLAN"
            :model-value="row.rentalPlan"
          />
        </template>
        <template #signingChannel="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_SIGN_CHANNEL"
            :model-value="row.signingChannel"
          />
        </template>
        <template #code="{ row }">
          <el-link :underline="false" type="primary" @click="openQuery(row)">
            {{ row.code }}
          </el-link>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:property-contract:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:property-contract:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:property-contract:delete']"
          >
            删除
          </el-link>
          <el-divider direction="vertical" />
          <ele-dropdown
            v-if="moreItems.length"
            :items="moreItems"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <PropertyContractEditForm
      ref="propertyContractRef"
      v-if="showStep == 2"
      :data="current"
      @done="stopDone"
    />
    <PropertyContractForm
      ref="propertyContractQueryRef"
      v-if="showStep == 3"
      :data="current"
      @done="stopDone"
    />
    <ContractTerminalForm
      ref="contractTerminalRef"
      v-if="showStep == 6"
      :data="current"
      @done="stopDone"
    />
    <PropertyContractSelectDialog
      v-model:dialogVisible="selectDialogVisible"
      @done="handleDialogDone"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { UploadOutlined, DownloadOutlined } from '@/components/icons';
  import { dateFormatter2 } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as PropertyContractApi from '@/api/lease/propertycontract';
  import PropertyContractEditForm from './PropertyContractEditForm.vue';
  import PropertyContractForm from './PropertyContractForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { EleMessage } from 'ele-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading } from 'element-plus';
  import ContractTerminalForm from './ContractTerminalForm.vue';
  import PropertyContractSelectDialog from './PropertyContractSelectDialog.vue';

  /** 物业合同 列表 */
  defineOptions({ name: 'PropertyContractIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  const showStep = ref(1); // 当前步骤
  const contractTerminalRef = ref(null);

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    code: undefined,
    name: undefined,
    status: undefined,
    approveStatus: undefined,
    signingDate: [],
    signingChannel: undefined,
    handler: undefined,
    handlerName: undefined,
    partaId: undefined,
    partaName: undefined,
    customerId: undefined,
    customerType: undefined,
    customerName: undefined,
    customerIndustry: undefined,
    customerIdcard: undefined,
    customerPhone: undefined,
    customerEmail: undefined,
    startDate: [],
    endDate: [],
    unitPrice: undefined,
    rentalUnit: undefined,
    rentalArea: undefined,
    extend: undefined,
    sourceId: undefined,
    contractType: undefined,
    sourceCode: undefined,
    sourceName: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: [],
    companyKey: undefined
  });

  /** 重置 */
  const reset = () => {
    resetFields();
    reload();
  };

  /** 表格实例 */
  const tableRef = ref();

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '合同编码',
      align: 'center',
      minWidth: 160,
      slot: 'code'
    },
    {
      prop: 'name',
      label: '合同名称',
      align: 'center',
      minWidth: 170
    },
    {
      prop: 'status',
      label: '合同状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'signingDate',
      label: '签订日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter2
    },
    {
      prop: 'customerName',
      label: '客户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'startDate',
      label: '开始日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter2
    },
    {
      prop: 'endDate',
      label: '结束日期',
      align: 'center',
      minWidth: 110,
      formatter: dateFormatter2
    },
    {
      prop: 'rentalArea',
      label: '租赁面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'sourceCode',
      label: '来源合同编码',
      align: 'center',
      minWidth: 160
    },
    {
      prop: 'sourceName',
      label: '来源合同名称',
      align: 'center',
      minWidth: 170
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中

  const propertyContractRef = ref(null);

  const propertyContractQueryRef = ref(null);

  /** 表格选中数据 */
  const selections = ref([]);

  const stopDone = () => {
    showStep.value = 1;
    reload();
  };
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return PropertyContractApi.getPropertyContractPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 打开编辑弹窗 */
  const openEdit = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    propertyContractRef.value?.initFormInfo(current);
  };

  /** 打开查询弹窗 */
  const openQuery = async (row) => {
    current.value = row ?? null;
    showStep.value = 3;
    // 初始化流程定义详情
    await nextTick();
    propertyContractQueryRef.value?.initFormInfo(current);
  };

  const removeBatch = async (row) => {
    // 删除的二次确认
    await message.delConfirm();

    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在删除中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      // 发起删除
      await PropertyContractApi.deletePropertyContract(row.id);
      // 关闭加载状态
      loadingInstance.close();
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch (error) {
      // 关闭加载状态
      loadingInstance.close();
      console.error('删除失败:', error);
    }
  };

  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data =
        await PropertyContractApi.exportPropertyContract(queryParams);
      download.excel(data, '物业合同.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
  /** 批量提交 */
  const submitBatch = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }

    // 检查合同状态是否为新建
    const allowedStatuses = [0];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有新建或者审批拒绝或者撤回的数据项才能提交');
      return;
    }

    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在提交中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    const batchIds = selections.value.map((d) => d.id);
    try {
      await PropertyContractApi.submitPropertyContract({
        type: 'submit',
        batchIds: batchIds
      });
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('提交成功');
      reload();
    } finally {
      // 关闭加载状态
      loadingInstance.close();
    }
  };
  //生成合同账单
  const toGenerateBill = async (row) => {
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '生成账单中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await PropertyContractApi.generateBill(row.id);
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('生成成功');
      reload();
    } finally {
      // 关闭加载状态
      loadingInstance.close();
    }
  };
  //退租合同
  const terminalContract = async (row) => {
    current.value = row ?? null;
    showStep.value = 6;
    // 初始化流程定义详情
    await nextTick();
    contractTerminalRef.value?.initFormInfo();
  };
  //作废合同
  const cancelContract = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查账单状态是否为待同步或者失败状态
    const allowedStatuses = [1, 2, 3];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有待审核或者未开始或者执行中状态的数据项才能作废');
      return;
    }
    ElMessageBox.confirm(
      `是否确认作废合同编号为"${rows.map((d) => d.code).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        // 开启全局加载状态
        const loadingInstance = ElLoading.service({
          lock: true,
          text: '作废合同中...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        PropertyContractApi.getCancelContract(rows[0].id)
          .then(() => {
            // 关闭加载状态
            loadingInstance.close();
            EleMessage.success('作废成功');
            reload();
          })
          .catch(() => {
            // 关闭加载状态
            loadingInstance.close();
          });
      })
      .catch(() => {});
  };
  //操作列更多下拉菜单
  const moreItems = computed(() => {
    const items = [];
    items.push({ title: '生成合同账单', command: 'synInterface' });
    items.push({ title: '退租', command: 'throwContract' });
    items.push({ title: '作废', command: 'cancelledContract' });
    return items;
  });
  //下拉菜单点击事件
  const dropClick = (key, row) => {
    if (key === 'synInterface') {
      toGenerateBill(row);
    } else if (key === 'throwContract') {
      terminalContract(row);
    } else if (key === 'cancelledContract') {
      cancelContract(row);
    }
  };

  const selectDialogVisible = ref(false);

  const handleAdd = () => {
    selectDialogVisible.value = true;
  };

  const handleDialogDone = () => {
    selectDialogVisible.value = false;
    reload();
  };
</script>
