<template>
  <el-dialog
    v-model="innerVisible"
    title="物业合同创建"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCancel"
  >
    <div class="dialog-content">
      <!-- 查询条件 -->
      <el-form :model="queryParams" inline class="search-bar" @submit.prevent>
        <el-form-item label="合同编码">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入合同编码"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="合同标题">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入合同标题"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户名称"
            clearable
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        highlight-current-row
        :row-key="(row) => row.id"
        :current-row="selectedRow"
        @current-change="handleCurrentChange"
        @row-dblclick="handleRowDblClick"
        height="350"
      >
        <el-table-column label="" width="50" align="center">
          <template #default="{ row }">
            <el-radio
              :model-value="selectedRow && selectedRow.id"
              :label="row.id"
              @change="() => selectRow(row)"
            >
              <span></span>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column type="index" label="序号" align="center" width="60" />
        <el-table-column prop="code" label="合同编码" align="center" />
        <el-table-column prop="name" label="合同标题" align="center" />
        <el-table-column prop="customerName" label="客户" align="center" />
      </el-table>

      <!-- 分页和底部按钮 -->
      <!-- 表格下方 -->
      <div class="pagination-bar">
        <el-pagination
          v-show="total > 0"
          :total="total"
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, prev, pager, next, sizes, jumper"
          @current-change="getList"
          @size-change="getList"
        />
      </div>
      <div class="footer-btns">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="!selectedRow"
          :loading="loading"
          @click="handleConfirm"
          >保存</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
  import { ref, reactive, watch } from 'vue';
  import {
    getPropertyContractEditFormPage,
    createPropertyContractEditForm
  } from '@/api/lease/propertycontract';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    dialogVisible: Boolean
  });
  const emit = defineEmits(['update:dialogVisible', 'done']);

  const innerVisible = ref(false);

  watch(
    () => props.dialogVisible,
    (val) => {
      innerVisible.value = val;
      if (val) {
        getList();
      }
    }
  );
  watch(innerVisible, (val) => {
    if (!val) emit('update:dialogVisible', false);
  });

  const loading = ref(false);
  const tableData = ref([]);
  const total = ref(0);
  const selectedRow = ref(null);

  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    code: '',
    name: '',
    customerName: ''
  });

  const handleQuery = () => {
    queryParams.pageNo = 1;
    getList();
  };

  const handleReset = () => {
    queryParams.code = '';
    queryParams.name = '';
    queryParams.customerName = '';
    handleQuery();
  };

  const getList = async () => {
    loading.value = true;
    try {
      const res = await getPropertyContractEditFormPage(queryParams);
      tableData.value = res.list || [];
      total.value = res.total || 0;
    } catch (e) {
      tableData.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  const selectRow = (row) => {
    selectedRow.value = row;
  };
  const handleCurrentChange = (row) => {
    selectedRow.value = row;
  };

  const handleCancel = () => {
    innerVisible.value = false;
    selectedRow.value = null;
  };

  const handleConfirm = async () => {
    if (!selectedRow.value) {
      ElMessage.warning('请选择一条数据');
      return;
    }
    loading.value = true;
    try {
      await createPropertyContractEditForm({
        id: selectedRow.value.id,
        type: 1
      });
      ElMessage.success('创建成功');
      innerVisible.value = false;
      emit('done');
    } catch (e) {
      ElMessage.error('创建失败');
    } finally {
      loading.value = false;
    }
  };

  const handleRowDblClick = (row) => {
    selectRow(row); // 先选中该行
    handleConfirm(); // 直接调用保存
  };
</script>
<style scoped>
  .search-bar {
    margin-bottom: 12px;
  }
  .el-table .el-radio__label {
    display: none !important;
  }
  .pagination-bar {
    margin-top: 10px;
    margin-bottom: 0;
    text-align: left;
  }

  .footer-btns {
    margin-top: 18px;
    text-align: right;
  }
</style>
