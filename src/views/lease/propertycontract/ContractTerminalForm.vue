<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3">
            退租-{{ props.data.code }}
          </span>
        </div>
      </template>
      <template #extra>
        <el-button
          v-if="active === 0"
          type="primary"
          :loading="loading"
          @click="onNext()"
        >
          提交
        </el-button>
      </template>
    </el-page-header>
  </ele-card>
  <ele-card header="退租信息" v-if="active === 0">
    <el-form
      ref="changeFormRef"
      :model="changeForm"
      :rules="changeRules"
      label-width="140px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租日期" prop="throwLeaseDate">
            <el-date-picker
              type="date"
              v-model="changeForm.throwLeaseDate"
              value-format="x"
              placeholder="请选择退租日期"
              class="ele-fluid"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租原因" prop="reason">
            <el-radio-group v-model="changeForm.reason">
              <el-radio
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.CO_CONTRACT_THROWLEASE_REASON
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="24" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租说明" prop="explanation">
            <el-input
              clearable
              v-model="changeForm.explanation"
              placeholder="请输入退租说明"
              maxlength="500"
              show-word-limit
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { EleMessage } from 'ele-admin-plus/es';
  import { ElLoading } from 'element-plus';
  import * as PropertyContractApi from '@/api/lease/propertycontract';
  const props = defineProps({
    data: Object
  });
  const emit = defineEmits(['done']);
  const loading = ref(false);
  /** 表单数据 */
  const [changeForm] = useFormData({
    type: 2,
    throwLeaseDate: undefined,
    reason: undefined,
    explanation: undefined
  });
  
  const disabledDate = (time: Date) => {
    return time.getTime() < Date.now();
  };
  /** 表单验证规则 */
  const changeRules = reactive<any>({
    throwLeaseDate: [
      { required: true, message: '退租日期不能为空', trigger: 'blur' }
    ]
  });

  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      
    } else {
      message.warning('加载失败，请重试');
    }
  };
  const message = useMessage(); // 消息弹窗
  /** 选中步骤 */
  const active = ref(0);
  /** 表单实例 */
  const changeFormRef = ref(null);

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };

  const onNext = async () => {
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '退租中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await PropertyContractApi.getThrowLeaseAffirm({
        ...changeForm,
        contractId: props.data.id
      });
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('退租成功');
    } catch (e) {
      // 关闭加载状态
      loadingInstance.close();
      // 错误处理
      EleMessage.error(e.message);
    }
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>
