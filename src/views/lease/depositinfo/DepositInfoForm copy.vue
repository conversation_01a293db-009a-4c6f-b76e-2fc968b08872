<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="合同id" prop="contractId">
        <el-input v-model="form.contractId" placeholder="请输入合同id" />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractNum">
        <el-input v-model="form.contractNum" placeholder="请输入合同编号" />
      </el-form-item>
      <el-form-item label="客户id" prop="customerId">
        <el-input v-model="form.customerId" placeholder="请输入客户id" />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerNum">
        <el-input v-model="form.customerNum" placeholder="请输入客户名称" />
      </el-form-item>
      <el-form-item label="应收金额" prop="shouldReceiveAmount">
        <el-input
          v-model="form.shouldReceiveAmount"
          placeholder="请输入应收金额"
        />
      </el-form-item>
      <el-form-item label="收款金额" prop="receiveAmount">
        <el-input v-model="form.receiveAmount" placeholder="请输入收款金额" />
      </el-form-item>
      <el-form-item label="滞纳金" prop="lateFee">
        <el-input v-model="form.lateFee" placeholder="请输入滞纳金" />
      </el-form-item>
      <el-form-item label="应退金额" prop="shouldRefundAmount">
        <el-input
          v-model="form.shouldRefundAmount"
          placeholder="请输入应退金额"
        />
      </el-form-item>
      <el-form-item label="已退金额" prop="refundAmount">
        <el-input v-model="form.refundAmount" placeholder="请输入已退金额" />
      </el-form-item>
      <el-form-item label="备用字段1" prop="atributeVarchar1">
        <el-input
          v-model="form.atributeVarchar1"
          placeholder="请输入备用字段1"
        />
      </el-form-item>
      <el-form-item label="备用字段2" prop="atributeVarchar2">
        <el-input
          v-model="form.atributeVarchar2"
          placeholder="请输入备用字段2"
        />
      </el-form-item>
      <el-form-item label="备用字段3" prop="atributeVarchar3">
        <el-input
          v-model="form.atributeVarchar3"
          placeholder="请输入备用字段3"
        />
      </el-form-item>
      <el-form-item label="备用字段4" prop="atributeVarchar4">
        <el-input
          v-model="form.atributeVarchar4"
          placeholder="请输入备用字段4"
        />
      </el-form-item>
      <el-form-item label="备用字段5" prop="atributeVarchar5">
        <el-input
          v-model="form.atributeVarchar5"
          placeholder="请输入备用字段5"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as DepositInfoApi from '@/api/lease/depositinfo';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 押金信息 表单 */
  defineOptions({ name: 'DepositInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    contractId: undefined,
    contractNum: undefined,
    customerId: undefined,
    customerNum: undefined,
    shouldReceiveAmount: undefined,
    receiveAmount: undefined,
    lateFee: undefined,
    shouldRefundAmount: undefined,
    refundAmount: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({
    contractId: [
      { required: true, message: '合同id不能为空', trigger: 'blur' }
    ],
    contractNum: [
      { required: true, message: '合同编号不能为空', trigger: 'blur' }
    ],
    customerId: [
      { required: true, message: '客户id不能为空', trigger: 'blur' }
    ],
    customerNum: [
      { required: true, message: '客户名称不能为空', trigger: 'blur' }
    ],
    shouldReceiveAmount: [
      { required: true, message: '应收金额不能为空', trigger: 'blur' }
    ],
    receiveAmount: [
      { required: true, message: '收款金额不能为空', trigger: 'blur' }
    ],
    lateFee: [{ required: true, message: '滞纳金不能为空', trigger: 'blur' }],
    shouldRefundAmount: [
      { required: true, message: '应退金额不能为空', trigger: 'blur' }
    ],
    refundAmount: [
      { required: true, message: '已退金额不能为空', trigger: 'blur' }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await DepositInfoApi.createDepositInfo(form);
        message.success(t('common.createSuccess'));
      } else {
        await DepositInfoApi.updateDepositInfo(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await DepositInfoApi.getDepositInfo(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
