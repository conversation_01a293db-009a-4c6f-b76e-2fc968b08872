<template>
  <ele-modal
    form
    :width="880"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form ref="formRef" :model="form" label-width="80px" v-loading="loading">
      <ele-card header="合同信息">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同编号" prop="code">
              <el-input class="input-200" disabled v-model="form.code" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同名称" prop="name">
              <el-input class="input-200" disabled v-model="form.name" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                class="input-200"
                disabled
                v-model="form.customerName"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同签订日期" prop="signingDate">
              <el-date-picker
                class="input-200"
                disabled
                v-model="form.signingDate"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户类型" prop="customerType">
              <el-select
                clearable
                v-model="form.customerType"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="签订渠道" prop="signingChannel">
              <el-select
                clearable
                v-model="form.signingChannel"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_SIGN_CHANNEL
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="经办人" prop="handlerName">
              <el-input class="input-200" disabled v-model="form.handlerName" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="保证金" prop="ensureFee">
              <el-input class="input-200" disabled v-model="form.ensureFee" />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>

      <ele-card header="退款信息">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="退款金额" prop="operaAmount">
              <el-input
                class="input-200"
                disabled
                v-model="form.depositLine.operaAmount"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                class="input-200"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                type="date"
                v-model="form.depositLine.startTime"
                placeholder="开始时间"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                class="input-200"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                type="date"
                v-model="form.depositLine.endTime"
                placeholder="结束时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item label="退款方式" prop="receiveType">
              <el-select
                clearable
                v-model="form.depositLine.receiveType"
                placeholder=""
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_DEPOSIT_REFUND_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item label="退款账户" prop="receiveAccount">
              <el-input
                class="input-200"
                v-model="form.depositLine.receiveAccount"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item label="备注" prop="remark">
              <el-input class="input-200" v-model="form.depositLine.remark" />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >确认</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as DepositInfoApi from '@/api/lease/depositinfo';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { useFormData } from '@/utils/use-form-data';
  import { ref } from 'vue';

  /** 押金信息 表单 */
  defineOptions({ name: 'DepositInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 附件实例 */
  const uploadFileRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    contractId: undefined,
    contractNum: undefined,
    customerId: undefined,
    customerNum: undefined,
    shouldReceiveAmount: undefined,
    receiveAmount: undefined,
    lateFee: undefined,
    shouldRefundAmount: undefined,
    refundAmount: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    depositStatus: undefined,
    code: undefined,
    name: undefined,
    signingDate: undefined,
    signingChannel: undefined,
    handler: undefined,
    handlerName: undefined,
    customerType: undefined,
    customerName: undefined,
    contactName: undefined,
    ensureFee: undefined,
    depositLine: {
      depositId: undefined,
      operaType: 3,
      operaAmount: undefined,
      startTime: undefined,
      endTime: undefined,
      receiveType: undefined,
      receiveAccount: undefined,
      receiveDate: undefined,
      refundType: undefined,
      refundAccount: undefined,
      refundDate: undefined,
      remark: undefined,
      actFile: undefined,
      file: {}
    },
    contractInfo: {
      code: '',
      name: '',
      signingDate: '',
      signingChannel: 1,
      handler: '',
      isRenewal: 0,
      customerRefId: 1,
      customerType: 1,
      customerName: '',
      contactName: '',
      contactNumber: '',
      files: '', //文件列表
      dateTime: [], //租赁期限
      daysOfYear: 1,
      monthBillingWay: 1,
      rentalPlan: 1,
      paymentFirstDate: '',
      paymentEndDateType: 1,
      paymentEndDateDays: '',
      unitPrice: '',
      rentalUnit: 1,
      paymentCycle: '',
      isPaymentFull: 0,
      ensureFee: '',
      renegeFee: '',
      penaltyAmtRatio: '',
      penaltyAmtType: 1,
      rentalArea: ''
    },
    depositLines: [
      {
        id: undefined,
        depositId: undefined,
        operaType: undefined,
        operaAmount: undefined,
        startTime: undefined,
        endTime: undefined,
        receiveType: undefined,
        receiveAccount: undefined,
        receiveDate: undefined,
        refundType: undefined,
        refundAccount: undefined,
        refundDate: undefined,
        actFile: undefined,
        remark: undefined,
        atributeVarchar1: undefined,
        atributeVarchar2: undefined,
        atributeVarchar3: undefined,
        atributeVarchar4: undefined,
        atributeVarchar5: undefined
      }
    ]
  });

  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      const rsp = {
        ...form,
        depositLine: {
          ...form.depositLine,
          startTime: new Date(form.depositLine.startTime).getTime(),
          endTime: new Date(form.depositLine.endTime).getTime()
        }
      };
      await DepositInfoApi.createDepositLine(rsp);
      message.success(t('common.createSuccess'));
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await DepositInfoApi.getDepositAllInfo(props.data.id);
        result.depositLine.operaAmount = result.shouldReceiveAmount;
        result.depositLine.operaType = 3;
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
