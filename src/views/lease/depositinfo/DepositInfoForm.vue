<template>
  <ele-modal
    form
    :width="1000"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form ref="formRef" :model="form" label-width="80px" v-loading="loading">
      <ele-card header="合同信息">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同编号" prop="code">
              <el-input class="input-200" disabled v-model="form.code" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同名称" prop="name">
              <el-input class="input-200" disabled v-model="form.name" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                class="input-200"
                disabled
                v-model="form.customerName"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同签订日期" prop="signingDate">
              <el-date-picker
                class="input-200"
                disabled
                v-model="form.signingDate"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户类型" prop="customerType">
              <el-select
                clearable
                v-model="form.customerType"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="签订渠道" prop="signingChannel">
              <el-select
                clearable
                v-model="form.signingChannel"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_SIGN_CHANNEL
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="经办人" prop="handlerName">
              <el-input class="input-200" disabled v-model="form.handlerName" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="保证金" prop="ensureFee">
              <el-input class="input-200" disabled v-model="form.ensureFee" />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>

      <ele-card header="收款/退款记录">
        <ele-pro-table
          row-key="id"
          :columns="columns"
          :datasource="datasource"
          :show-overflow-tooltip="true"
          :footer-style="{ paddingBottom: '16px' }"
        >
          <template #actFileUrl="{ row }">
            <el-link
              v-if="row.actFileUrl"
              type="primary"
              download
              :href="row.actFileUrl"
              :underline="false"
              target="_blank"
              >下载</el-link
            >
          </template>
          <template #operaType="{ row }">
            <dict-data
              type="tag"
              :code="DICT_TYPE.CO_DEPOSIT_OPERA_TYPE"
              :model-value="row.operaType"
            />
          </template>
          <template #synStatus="{ row }">
            <dict-data
              type="tag"
              :code="DICT_TYPE.CO_CONTRACT_PAY_SYN_STATUS"
              :model-value="row.synStatus"
            />
          </template>
          <template #action="{ row }">
            <el-link
              :underline="false"
              type="primary"
              @click="synBatch(row)"
              v-permission="['lease:deposit-info:update']"
            >
              同步
            </el-link>
          </template>
        </ele-pro-table>
      </ele-card>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as DepositInfoApi from '@/api/lease/depositinfo';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { useFormData } from '@/utils/use-form-data';
  import { ref, computed } from 'vue';
  import { dateFormatter2 } from '@/utils/formatTime';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  /** 押金信息 表单 */
  defineOptions({ name: 'DepositInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    contractId: undefined,
    contractNum: undefined,
    customerId: undefined,
    customerNum: undefined,
    shouldReceiveAmount: undefined,
    receiveAmount: undefined,
    lateFee: undefined,
    shouldRefundAmount: undefined,
    refundAmount: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    depositStatus: undefined,
    code: undefined,
    name: undefined,
    signingDate: undefined,
    signingChannel: undefined,
    handler: undefined,
    handlerName: undefined,
    customerType: undefined,
    customerName: undefined,
    contactName: undefined,
    ensureFee: undefined,
    contractInfo: {
      code: '',
      name: '',
      signingDate: '',
      signingChannel: 1,
      handler: '',
      isRenewal: 0,
      customerRefId: 1,
      customerType: 1,
      customerName: '',
      contactName: '',
      contactNumber: '',
      files: '', //文件列表
      dateTime: [], //租赁期限
      daysOfYear: 1,
      monthBillingWay: 1,
      rentalPlan: 1,
      paymentFirstDate: '',
      paymentEndDateType: 1,
      paymentEndDateDays: '',
      unitPrice: '',
      rentalUnit: 1,
      paymentCycle: '',
      isPaymentFull: 0,
      ensureFee: '',
      renegeFee: '',
      penaltyAmtRatio: '',
      penaltyAmtType: 1,
      rentalArea: ''
    },
    depositLines: [
      {
        id: undefined,
        depositId: undefined,
        operaType: undefined,
        operaAmount: undefined,
        startTime: undefined,
        endTime: undefined,
        receiveType: undefined,
        receiveAccount: undefined,
        receiveDate: undefined,
        refundType: undefined,
        refundAccount: undefined,
        refundDate: undefined,
        actFile: undefined,
        remark: undefined,
        atributeVarchar1: undefined,
        atributeVarchar2: undefined,
        atributeVarchar3: undefined,
        atributeVarchar4: undefined,
        atributeVarchar5: undefined
      }
    ]
  });

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'operaTime',
        label: '收款/退款日期',
        align: 'center',
        minWidth: 80,
        formatter: dateFormatter2
      },
      {
        prop: 'operaAmount',
        label: '收款/退款金额',
        align: 'center',
        minWidth: 80
      },
      {
        prop: 'operaType',
        label: '操作类型',
        align: 'center',
        minWidth: 80,
        slot: 'operaType'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'actFileUrl',
        label: '附件',
        width: 90,
        align: 'center',
        slot: 'actFileUrl'
      },
      {
        prop: 'synStatus',
        label: '同步状态',
        align: 'center',
        minWidth: 110,
        slot: 'synStatus'
      },
      {
        prop: 'synMessage',
        label: '同步外部信息',
        align: 'center',
        minWidth: 110
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 130,
        align: 'center',
        fixed: 'right',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  /** 表格数据源 */
  const datasource = () => {
    return DepositInfoApi.getDepositLineByDepositId(props.data.id);
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await DepositInfoApi.getDepositAllInfo(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };

  const synBatch = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查账单状态是否为待同步或者失败状态
    const allowedStatuses = [1, 5];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.synStatus)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有待同步或者失败状态的数据项才能提交');
      return;
    }
    ElMessageBox.confirm(`是否确认提交押金数据?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        DepositInfoApi.synDepositBill({ ids: rows.map((d) => d.id) })
          .then(() => {
            loading.close();
            EleMessage.success('提交成功');
            cancle();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>
