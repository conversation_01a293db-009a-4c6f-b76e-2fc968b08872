<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同编号" prop="contractNum">
              <el-input
                v-model.trim="queryParams.contractNum"
                placeholder="请输入合同编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerNum">
              <el-input
                v-model.trim="queryParams.customerNum"
                placeholder="请输入客户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:deposit-info:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #depositStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_DEPOSIT_STATUS"
            :model-value="row.depositStatus"
          />
        </template>
        <template #synStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_PAY_SYN_STATUS"
            :model-value="row.synStatus"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openDetail(row)"
            v-permission="['lease:deposit-info:query']"
          >
            详细
          </el-link>
          <!-- 根据 depositStatus 显示不同的按钮 -->
          <template v-if="row.depositStatus === 0">
            <el-divider
              direction="vertical"
              v-permission="['lease:deposit-info:update']"
            />
            <el-link
              :underline="false"
              type="primary"
              @click="openReceive(row)"
              v-permission="['lease:deposit-info:update']"
            >
              收款
            </el-link>
          </template>
          <template v-if="row.depositStatus === 2">
            <el-divider
              direction="vertical"
              v-permission="['lease:deposit-info:update']"
            />
            <el-link
              :underline="false"
              type="primary"
              @click="openRefund(row)"
              v-permission="['lease:deposit-info:update']"
            >
              退款
            </el-link>
          </template>
          <!-- <el-divider
            direction="vertical"
            v-permission="['lease:deposit-info:update']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="synBatch(row)"
            v-permission="['lease:deposit-info:update']"
          >
            同步
          </el-link> -->
        </template>
      </ele-pro-table>
    </ele-card>
    <DepositInfoForm v-model="showDetail" :data="current" @done="reload" />
    <DepositReceiveAmountForm
      v-model="showReceive"
      :data="current"
      @done="reload"
    />
    <DepositRefundAmountForm
      v-model="showRefund"
      :data="current"
      @done="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as DepositInfoApi from '@/api/lease/depositinfo';
  import DepositInfoForm from './DepositInfoForm.vue';
  import DepositReceiveAmountForm from './DepositReceiveAmountForm.vue';
  import DepositRefundAmountForm from './DepositRefundAmountForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  import { ref } from 'vue';

  /** 押金信息 列表 */
  defineOptions({ name: 'DepositInfoIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    contractId: undefined,
    contractNum: undefined,
    customerId: undefined,
    customerNum: undefined,
    shouldReceiveAmount: undefined,
    receiveAmount: undefined,
    lateFee: undefined,
    shouldRefundAmount: undefined,
    refundAmount: undefined,
    synStatus: undefined,
    synMessage: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'depositNum',
      label: '账单编号',
      align: 'center',
      minWidth: 150
    },
    {
      prop: 'contractNum',
      label: '合同编号',
      align: 'center',
      minWidth: 190
    },
    {
      prop: 'customerNum',
      label: '客户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'shouldReceiveAmount',
      label: '应收金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'receiveAmount',
      label: '收款金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'lateFee',
      label: '滞纳金',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'shouldRefundAmount',
      label: '应退金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'refundAmount',
      label: '已退金额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'depositStatus',
      label: '状态',
      align: 'center',
      minWidth: 80,
      slot: 'depositStatus'
    },
    {
      prop: 'synStatus',
      label: '同步状态',
      align: 'center',
      minWidth: 110,
      slot: 'synStatus'
    },
    {
      prop: 'synMessage',
      label: '同步外部信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 210,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示明细弹窗 */
  const showDetail = ref(false);
  /** 是否显示退款弹窗 */
  const showRefund = ref(false);
  /** 是否显示收款弹窗 */
  const showReceive = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return DepositInfoApi.getDepositInfoPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开明细弹窗 */
  const openDetail = (row) => {
    current.value = row ?? null;
    showDetail.value = true;
  };

  /** 打开收款弹窗 */
  const openReceive = (row) => {
    current.value = row ?? null;
    showReceive.value = true;
  };

  /** 打开退款弹窗 */
  const openRefund = (row) => {
    current.value = row ?? null;
    showRefund.value = true;
  };

  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await DepositInfoApi.deleteDepositInfo(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await DepositInfoApi.exportDepositInfo(queryParams);
      download.excel(data, '押金信息.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
