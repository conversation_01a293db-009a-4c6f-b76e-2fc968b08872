<template>
  <el-dialog v-model="dialogVisible" title="上传文件">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :action="uploadUrl"
      :auto-upload="false"
      :data="data"
      :disabled="formLoading"
      :limit="1"
      :on-change="handleFileChange"
      :on-error="submitFormError"
      :on-exceed="handleExceed"
      :on-success="submitFormSuccess"
      :http-request="httpRequest"
      accept=".jpg, .png, .gif"
      drag
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text"> 将文件拖到此处，或 <em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip" style="color: red">
          提示：仅允许导入 jpg、png、gif 格式文件！
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitFileForm"
        >确 定</el-button
      >
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { useUpload } from '@/components/UploadFile/src/useUpload';
  import { useMessage } from '@/hooks/web/useMessage';

  const message = useMessage(); // 消息弹窗

  const dialogVisible = ref(false); // 弹窗的是否展示
  const formLoading = ref(false); // 表单的加载中
  const fileList = ref([]); // 文件列表
  const data = ref({ path: '' });
  const uploadRef = ref();

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { uploadUrl, httpRequest } = useUpload();

  /** 打开弹窗 */
  const open = async () => {
    debugger;
    dialogVisible.value = true;
    // 重置上传状态和文件
    formLoading.value = false;
    uploadRef.value = props.data;
    if(props.data){
      fileList.value.push(props.data);
    }
  };
  defineExpose({ open }); // 提供 open 方法，用于打开弹窗

  /** 处理上传的文件发生变化 */
  const handleFileChange = (file) => {
    data.value.path = file.name;
  };

  /** 提交表单 */
  const submitFileForm = () => {
    if (fileList.value.length == 0) {
      message.error('请上传文件');
      return;
    }
    unref(uploadRef)?.submit();
  };

  /** 文件上传成功处理 */
  const emit = defineEmits(['success']); // 定义 success 事件，用于操作成功后的回调
  const submitFormSuccess = (response) => {
    // 清理
    dialogVisible.value = false;
    formLoading.value = false;
    unref(uploadRef)?.clearFiles();
    // 提示成功，并刷新
    message.success('新增成功');
    emit('success', response.data);
  };

  /** 上传错误提示 */
  const submitFormError = (): void => {
    message.error('上传失败，请您重新上传！');
    formLoading.value = false;
  };

  /** 重置表单 */
  const resetForm = () => {
    // 重置上传状态和文件
    formLoading.value = false;
    uploadRef.value?.clearFiles();
  };

  /** 文件数超出提示 */
  const handleExceed = (): void => {
    message.error('最多只能上传一个文件！');
  };
</script>
