<template>
  <ele-modal
      form
      :width="680"
      v-model="visible"
      :close-on-click-modal="false"
      destroy-on-close
      :title="title"
      @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="合同ID" prop="contractId">
        <el-input v-model="form.contractId" placeholder="请输入合同ID" />
      </el-form-item>
      <el-form-item label="合同编号" prop="contractCode">
        <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
      </el-form-item>
      <el-form-item label="付款计划ID" prop="planId">
        <el-input v-model="form.planId" placeholder="请输入付款计划ID" />
      </el-form-item>
      <el-form-item label="合同类型，1-租赁合同，2-长租合同，3-工位合同" prop="contractType">
        <el-select v-model="form.contractType" placeholder="请选择合同类型，1-租赁合同，2-长租合同，3-工位合同">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="账单编号" prop="billCode">
        <el-input v-model="form.billCode" placeholder="请输入账单编号" />
      </el-form-item>
      <el-form-item label="支付类型，1-计划；2-其他；" prop="paymentType">
        <el-select v-model="form.paymentType" placeholder="请选择支付类型，1-计划；2-其他；">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="费用类型" prop="costType">
        <el-select v-model="form.costType" placeholder="请选择费用类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="startDate">
        <el-date-picker
          v-model="form.startDate"
          type="date"
          value-format="x"
          placeholder="选择开始时间"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endDate">
        <el-date-picker
          v-model="form.endDate"
          type="date"
          value-format="x"
          placeholder="选择结束时间"
        />
      </el-form-item>
      <el-form-item label="实收日期" prop="actualPaymentDate">
        <el-date-picker
          v-model="form.actualPaymentDate"
          type="date"
          value-format="x"
          placeholder="选择实收日期"
        />
      </el-form-item>
      <el-form-item label="收款日期" prop="paymentDate">
        <el-date-picker
          v-model="form.paymentDate"
          type="date"
          value-format="x"
          placeholder="选择收款日期"
        />
      </el-form-item>
      <el-form-item label="固定租金" prop="fixedRent">
        <el-input v-model="form.fixedRent" placeholder="请输入固定租金" />
      </el-form-item>
      <el-form-item label="浮动租金" prop="floatingRent">
        <el-input v-model="form.floatingRent" placeholder="请输入浮动租金" />
      </el-form-item>
      <el-form-item label="是否逾期" prop="isOverdue">
        <el-input v-model="form.isOverdue" placeholder="请输入是否逾期" />
      </el-form-item>
      <el-form-item label="状态, 1-未收; 2-部分结清; 3-已结清; 4-已作废;" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败" prop="synStatus">
        <el-radio-group v-model="form.synStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="同步外部信息" prop="synMessage">
        <el-input v-model="form.synMessage" placeholder="请输入同步外部信息" />
      </el-form-item>
      <el-form-item label="应收总金额" prop="totalAmt">
        <el-input v-model="form.totalAmt" placeholder="请输入应收总金额" />
      </el-form-item>
      <el-form-item label="已收总金额" prop="receivedTotalAmt">
        <el-input v-model="form.receivedTotalAmt" placeholder="请输入已收总金额" />
      </el-form-item>
      <el-form-item label="退款总金额" prop="refundTotalAmt">
        <el-input v-model="form.refundTotalAmt" placeholder="请输入退款总金额" />
      </el-form-item>
      <el-form-item label="应退总金额" prop="refundableTotalAmt">
        <el-input v-model="form.refundableTotalAmt" placeholder="请输入应退总金额" />
      </el-form-item>
      <el-form-item label="扩展信息" prop="extend">
        <el-input v-model="form.extend" placeholder="请输入扩展信息" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading">保存</el-button>
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
import * as ContractBillApi from '@/api/lease/contractbill'
import TinymceEditor from '@/components/TinymceEditor/index.vue';
import { useFormData } from '@/utils/use-form-data';

/** 合同账单 表单 */
defineOptions({ name: 'ContractBillForm' })

const props = defineProps({
  /** 修改回显的数据 */
  data: Object
});

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const emit = defineEmits(['done']);

const title = ref('') // 弹窗的标题
/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean });

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
                    id: undefined,
                    contractId: undefined,
                    contractCode: undefined,
                    planId: undefined,
                    contractType: undefined,
                    billCode: undefined,
                    paymentType: undefined,
                    costType: undefined,
                    startDate: undefined,
                    endDate: undefined,
                    actualPaymentDate: undefined,
                    paymentDate: undefined,
                    fixedRent: undefined,
                    floatingRent: undefined,
                    isOverdue: undefined,
                    status: undefined,
                    synStatus: undefined,
                    synMessage: undefined,
                    totalAmt: undefined,
                    receivedTotalAmt: undefined,
                    refundTotalAmt: undefined,
                    refundableTotalAmt: undefined,
                    extend: undefined,
                    remark: undefined,
});
/** 表单验证规则 */
const rules  = reactive({
});
/** 关闭弹窗 */
const cancle = () => {
  visible.value = false;
};

const save = async () => {
  if (!formRef) return
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  loading.value = true
  try {
    if (!isUpdate.value) {
      await ContractBillApi.createContractBill(form)
      message.success(t('common.createSuccess'))
    } else {
      await ContractBillApi.updateContractBill(form)
      message.success(t('common.updateSuccess'))
    }
    visible.value = false
    // 发送操作成功的事件
    emit('done')
  } finally {
    loading.value = false
  }
}

/** 弹窗打开事件 */
const handleOpen = async () => {
  loading.value = true
  try{
    if (props.data) {
      const result = await ContractBillApi.getContractBill(props.data.id)
      assignFields({...result});
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    title.value = isUpdate.value?t('action.update'): t('action.create')
  }finally {
    loading.value = false
  }
}
</script>