<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同编号" prop="contractCode">
              <el-input
                v-model.trim="queryParams.contractCode"
                placeholder="请输入合同编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同类型" prop="contractType">
              <el-select
                clearable
                v-model="queryParams.contractType"
                placeholder="请选择合同类型"
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_BILL_CONTRACT_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单编号" prop="billCode">
              <el-input
                v-model.trim="queryParams.billCode"
                placeholder="请输入账单编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="支付类型" prop="paymentType">
              <el-select
                clearable
                v-model="queryParams.paymentType"
                placeholder="请选择支付类型"
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_PAY_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="queryParams.startDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="queryParams.endDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="实收日期" prop="actualPaymentDate">
              <el-date-picker
                v-model="queryParams.actualPaymentDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="收款日期" prop="paymentDate">
              <el-date-picker
                v-model="queryParams.paymentDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="是否逾期" prop="isOverdue">
              <el-select
                clearable
                v-model="queryParams.isOverdue"
                placeholder="请选择"
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_IS_OVERDUE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                clearable
                v-model="queryParams.status"
                placeholder="请选择状态"
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_PAY_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="同步状态" prop="synStatus">
              <el-select
                clearable
                v-model="queryParams.synStatus"
                placeholder="请选择同步状态"
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_PAY_SYN_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:contract-bill:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #contractType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_BILL_CONTRACT_TYPE"
            :model-value="row.contractType"
          />
        </template>
        <template #paymentType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_PAY_TYPE"
            :model-value="row.paymentType"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_PAY_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #synStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_PAY_SYN_STATUS"
            :model-value="row.synStatus"
          />
        </template>
        <template #isOverdue="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_IS_OVERDUE"
            :model-value="row.isOverdue"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:contract-bill:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:contract-bill:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:contract-bill:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter, dateFormatter2 } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as ContractBillApi from '@/api/lease/contractbill';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref } from 'vue';

  /** 合同账单 列表 */
  defineOptions({ name: 'ContractBillIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    contractId: undefined,
    contractCode: undefined,
    planId: undefined,
    contractType: undefined,
    billCode: undefined,
    paymentType: undefined,
    costType: undefined,
    startDate: [],
    endDate: [],
    actualPaymentDate: [],
    paymentDate: [],
    fixedRent: undefined,
    floatingRent: undefined,
    isOverdue: undefined,
    status: undefined,
    synStatus: undefined,
    synMessage: undefined,
    totalAmt: undefined,
    receivedTotalAmt: undefined,
    refundTotalAmt: undefined,
    refundableTotalAmt: undefined,
    extend: undefined,
    remark: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'contractCode',
      label: '合同编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contractType',
      label: '合同类型',
      align: 'center',
      minWidth: 110,
      slot: 'contractType'
    },
    {
      prop: 'billCode',
      label: '账单编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'paymentType',
      label: '支付类型',
      align: 'center',
      minWidth: 110,
      slot: 'paymentType'
    },
    {
      prop: 'costType',
      label: '费用类型',
      align: 'center',
      minWidth: 110,
      slot: 'costType'
    },
    {
      prop: 'startDate',
      label: '开始时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'endDate',
      label: '结束时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'actualPaymentDate',
      label: '实收日期',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'paymentDate',
      label: '收款日期',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'fixedRent',
      label: '固定租金',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'floatingRent',
      label: '浮动租金',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'isOverdue',
      label: '是否逾期',
      align: 'center',
      minWidth: 110,
      slot: 'isOverdue'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'synStatus',
      label: '同步状态',
      align: 'center',
      minWidth: 110,
      slot: 'synStatus'
    },
    {
      prop: 'synMessage',
      label: '同步外部信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'totalAmt',
      label: '应收总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'receivedTotalAmt',
      label: '已收总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'refundTotalAmt',
      label: '退款总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'refundableTotalAmt',
      label: '应退总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'extend',
      label: '扩展信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ContractBillApi.getContractBillPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await ContractBillApi.deleteContractBill(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await ContractBillApi.exportContractBill(queryParams);
      download.excel(data, '合同账单.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
