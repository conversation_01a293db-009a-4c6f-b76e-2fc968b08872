<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单编码" prop="propertyBillCode">
              <el-input
                v-model.trim="queryParams.propertyBillCode"
                placeholder="请输入账单编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                v-model.trim="queryParams.customerName"
                placeholder="请输入客户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="业主信息" prop="ownerInfo">
              <el-input
                v-model.trim="queryParams.ownerInfo"
                placeholder="请输入业主信息"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="抄表类型" prop="meterReadingType">
              <el-select
                clearable
                v-model="queryParams.meterReadingType"
                placeholder=""
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_METER_READING_TYPE
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="抄表日期" prop="meterReadingDate">
              <el-date-picker
                v-model="queryParams.meterReadingDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <!-- <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:property-bill:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button> -->
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:property-bill:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #synStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_SYN_STATUS"
            :model-value="row.synStatus"
          />
        </template>
        <template #meterReadingType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_METER_READING_TYPE"
            :model-value="row.meterReadingType"
          />
        </template>
        <template #action="{ row }">
          <!-- <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:property-bill:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:property-bill:delete']"
          /> -->
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:property-bill:delete']"
          >
            删除
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:property-bill:update']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="synBatch(row)"
            v-permission="['lease:property-bill:update']"
          >
            同步
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <PropertyBillForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as PropertyBillApi from '@/api/lease/propertybill';
  import PropertyBillForm from './PropertyBillForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { EleMessage } from 'ele-admin-plus/es';
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';

  /** 物业账单 列表 */
  defineOptions({ name: 'PropertyBillIndex' });

  /** 表格选中数据 */
  const selections = ref([]);

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    propertyBillCode: undefined,
    roomId: undefined,
    roomCode: undefined,
    roomName: undefined,
    ownerInfo: undefined,
    meterReadingType: undefined,
    magnification: undefined,
    lastMeterBottom: undefined,
    lastAttributedMonth: undefined,
    lastMeterReadingDate: [],
    meterBottom: undefined,
    attributedMonth: undefined,
    meterReadingDate: [],
    synStatus: undefined,
    synMessage: undefined,
    externalKey: undefined,
    synTime: [],
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'propertyBillCode',
      label: '账单编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'meterReadingType',
      label: '抄表类型',
      align: 'center',
      minWidth: 110,
      slot: 'meterReadingType'
    },
    {
      prop: 'customerName',
      label: '客户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'thisDosage',
      label: '本次用量',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'paymentAmount',
      label: '金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'synStatus',
      label: '同步状态',
      align: 'center',
      minWidth: 110,
      slot: 'synStatus'
    },
    {
      prop: 'synMessage',
      label: '同步信息',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return PropertyBillApi.getPropertyBillPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await PropertyBillApi.deletePropertyBill(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await PropertyBillApi.exportPropertyBill(queryParams);
      download.excel(data, '物业账单.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  const synBatch = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查账单状态是否为待同步或者失败状态
    const allowedStatuses = [1, 5];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.synStatus)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有待同步或者失败状态的数据项才能提交');
      return;
    }
    ElMessageBox.confirm(
      `是否确认提交账单编号为"${rows.map((d) => d.propertyBillCode).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        PropertyBillApi.synContractBill({ ids: rows.map((d) => d.id) })
          .then(() => {
            loading.close();
            EleMessage.success('提交成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>
