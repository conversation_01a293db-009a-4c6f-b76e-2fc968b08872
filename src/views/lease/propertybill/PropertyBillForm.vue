<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="账单编码" prop="propertyBillCode">
        <el-input
          v-model="form.propertyBillCode"
          placeholder="请输入账单编码"
        />
      </el-form-item>
      <el-form-item label="房间编码" prop="roomCode">
        <el-input v-model="form.roomCode" placeholder="请输入房间编码" />
      </el-form-item>
      <el-form-item label="房间描述" prop="roomName">
        <el-input v-model="form.roomName" placeholder="请输入房间描述" />
      </el-form-item>
      <el-form-item label="业主信息" prop="ownerInfo">
        <el-input v-model="form.ownerInfo" placeholder="请输入业主信息" />
      </el-form-item>
      <el-form-item label="抄表类型" prop="meterReadingType">
        <el-select
          clearable
          v-model="form.meterReadingType"
          placeholder=""
          class="ele-fluid"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CO_METER_READING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="倍率" prop="magnification">
        <el-input v-model="form.magnification" placeholder="请输入倍率" />
      </el-form-item>
      <el-form-item label="上次表底" prop="lastMeterBottom">
        <el-input v-model="form.lastMeterBottom" placeholder="请输入上次表底" />
      </el-form-item>
      <el-form-item label="上次归属月份" prop="lastAttributedMonth">
        <el-input
          v-model="form.lastAttributedMonth"
          placeholder="请输入上次归属月份"
        />
      </el-form-item>
      <el-form-item label="上次抄表日期" prop="lastMeterReadingDate">
        <el-date-picker
          v-model="form.lastMeterReadingDate"
          type="date"
          value-format="x"
          placeholder="选择上次抄表日期"
        />
      </el-form-item>
      <el-form-item label="本次表底" prop="meterBottom">
        <el-input v-model="form.meterBottom" placeholder="请输入本次表底" />
      </el-form-item>
      <el-form-item label="本次归属月份" prop="attributedMonth">
        <el-input
          v-model="form.attributedMonth"
          placeholder="请输入本次归属月份"
        />
      </el-form-item>
      <el-form-item label="本次抄表日期" prop="meterReadingDate">
        <el-date-picker
          v-model="form.meterReadingDate"
          type="date"
          value-format="x"
          placeholder="选择本次抄表日期"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PropertyBillApi from '@/api/lease/propertybill';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 物业账单 表单 */
  defineOptions({ name: 'PropertyBillForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    propertyBillCode: undefined,
    roomId: undefined,
    roomCode: undefined,
    roomName: undefined,
    ownerInfo: undefined,
    meterReadingType: undefined,
    magnification: undefined,
    lastMeterBottom: undefined,
    lastAttributedMonth: undefined,
    lastMeterReadingDate: undefined,
    meterBottom: undefined,
    attributedMonth: undefined,
    meterReadingDate: undefined,
    synStatus: undefined,
    synMessage: undefined,
    externalKey: undefined,
    synTime: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({});
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await PropertyBillApi.createPropertyBill(form);
        message.success(t('common.createSuccess'));
      } else {
        await PropertyBillApi.updatePropertyBill(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await PropertyBillApi.getPropertyBill(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
