<template>
  <!-- 搜索表单 -->
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="房间号" prop="roomNo">
            <el-input
              v-model.trim="queryParams.roomNo"
              placeholder="请输入房间号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="房间编码" prop="roomNum">
            <el-input
              v-model.trim="queryParams.roomNum"
              placeholder="请输入房间编码"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button :icon="Search" type="primary" @click="reload"
              >查询</el-button
            >
            <el-button :icon="Refresh" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 表格 -->
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
    >
      <template #houseStatus="{ row }">
        <dict-data
          type="tag"
          :code="DICT_TYPE.CO_LONG_HOUSE_STATUS"
          :model-value="row.houseStatus"
        />
      </template>
      <template #spaceType="{ row }">
        <dict-data
          type="tag"
          :code="DICT_TYPE.CO_LONG_HOUSE_TYPE"
          :model-value="row.spaceType"
        />
      </template>
      <template #action="{ row }">
        <el-link
          :underline="false"
          type="primary"
          @click="openEdit(row)"
          v-permission="['lease:long-house:update']"
        >
          修改
        </el-link>
      </template>
    </ele-pro-table>
  </ele-card>
  <LongHouseForm v-model="showEdit" :data="current" @done="reload" />
</template>

<script setup lang="ts">
  import { Search, Refresh } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import * as LongHouseApi from '@/api/lease/longhouse';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import LongHouseForm from './LongHouseForm.vue';

  /** 长租房管理 列表 */
  defineOptions({ name: 'LongHouseIndex' });
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    id: void 0,
    buildingInfo: undefined,
    floorInfo: undefined,
    roomNo: undefined,
    roomNum: undefined,
    area: undefined,
    price: undefined,
    houseStatus: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 当前编辑数据 */
  const current = ref(null);
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'buildingInfo',
      label: '楼宇',
      align: 'center',
      minWidth: 90
    },
    {
      prop: 'floorInfo',
      label: '楼层',
      align: 'center',
      minWidth: 90
    },
    {
      prop: 'roomNo',
      label: '房间号',
      align: 'center',
      minWidth: 140
    },
    {
      prop: 'roomNum',
      label: '房间编码',
      align: 'center',
      minWidth: 140
    },
    {
      prop: 'spaceType',
      label: '房间类型',
      align: 'center',
      minWidth: 80,
      slot: 'spaceType'
    },
    {
      prop: 'area',
      label: '面积',
      align: 'center',
      minWidth: 90
    },
    {
      prop: 'price',
      label: '价格',
      align: 'center',
      minWidth: 90
    },
    {
      prop: 'houseStatus',
      label: '房屋状态',
      align: 'center',
      minWidth: 110,
      slot: 'houseStatus'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 210,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 80,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return LongHouseApi.getLongHousePage({
      ...where,
      houseStatus: 1,
      ...filters,
      ...pages
    });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
</script>
