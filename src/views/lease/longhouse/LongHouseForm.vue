<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="楼宇" prop="buildingInfo">
        <el-input
          v-model="form.buildingInfo"
          disabled
          placeholder="请输入楼宇"
        />
      </el-form-item>
      <el-form-item label="楼层" prop="floorInfo">
        <el-input v-model="form.floorInfo" disabled placeholder="请输入楼层" />
      </el-form-item>
      <el-form-item label="房间号" prop="roomNo">
        <el-input v-model="form.roomNo" disabled placeholder="请输入房间号" />
      </el-form-item>
      <el-form-item label="房间编码" prop="roomNum">
        <el-input
          v-model="form.roomNum"
          disabled
          placeholder="请输入房间编码"
        />
      </el-form-item>
      <el-form-item label="面积(平方)" prop="area">
        <el-input v-model="form.area" disabled placeholder="请输入面积" />
      </el-form-item>
      <el-form-item label="价格(元)" prop="price">
        <el-input v-model="form.price" placeholder="请输入价格" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as LongHouseApi from '@/api/lease/longhouse';
  import { useFormData } from '@/utils/use-form-data';

  /** 长租房管理 表单 */
  defineOptions({ name: 'LongHouseForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    buildingInfo: undefined,
    floorInfo: undefined,
    roomNo: undefined,
    roomNum: undefined,
    area: undefined,
    price: undefined,
    houseStatus: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({});
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await LongHouseApi.createLongHouse(form);
        message.success(t('common.createSuccess'));
      } else {
        await LongHouseApi.updateLongHouse(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await LongHouseApi.getLongHouse(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
