<template>
  <!-- 搜索表单 -->
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="房间号" prop="roomNo">
            <el-input
              v-model.trim="queryParams.roomNo"
              placeholder="请输入房间号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button :icon="Search" type="primary" @click="reload"
              >查询</el-button
            >
            <el-button :icon="Refresh" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 表格 -->
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
    >
      <template #houseStatus="{ row }">
        <dict-data
          type="tag"
          :code="DICT_TYPE.CO_LONG_HOUSE_STATUS"
          :model-value="row.houseStatus"
        />
      </template>
      <template #spaceType="{ row }">
        <dict-data
          type="tag"
          :code="DICT_TYPE.CO_LONG_HOUSE_TYPE"
          :model-value="row.spaceType"
        />
      </template>
    </ele-pro-table>
  </ele-card>
  <LongHouseForm v-model="showEdit" :data="current" @done="reload" />
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as LongHouseApi from '@/api/lease/longhouse';
  import LongHouseForm from './LongHouseForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  /** 长租房管理 列表 */
  defineOptions({ name: 'LongHouseIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    buildingInfo: undefined,
    floorInfo: undefined,
    roomNo: undefined,
    roomNum: undefined,
    area: undefined,
    price: undefined,
    houseStatus: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'buildingInfo',
      label: '楼宇',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'floorInfo',
      label: '楼层',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'roomNo',
      label: '房间号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'roomNum',
      label: '房间编码',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'spaceType',
      label: '房间类型',
      align: 'center',
      minWidth: 80,
      slot: 'spaceType'
    },
    {
      prop: 'area',
      label: '面积',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'price',
      label: '价格',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'houseStatus',
      label: '房屋状态',
      align: 'center',
      minWidth: 110,
      slot: 'houseStatus'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  const props = defineProps<{
    type: number;
  }>();
  /** 当前编辑数据 */
  const current = ref(null);
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    debugger;
    return LongHouseApi.getLongHousePage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  const getNoticeListExpose = async (currentTabIndex) => {
    debugger;
    var status;
    status = currentTabIndex;
    const result = await LongHouseApi.getLongHousePage({
      ...where,
      status: status,
      ...filters,
      ...pages
    });
    datasource.value = result.list;
  };
</script>
