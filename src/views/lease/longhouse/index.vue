<!-- 最新动态 -->
<template>
  <ele-page flex-table>
    <ele-card flex-table>
      <ele-tabs
        :items="[
          { name: 'to_be_rented_out', label: '待出租' },
          { name: 'waiting_for_check_in', label: '待入住' },
          { name: 'renting_out', label: '出租中' },
          { name: 'resignation_in_rogress', label: '退租中' },
          { name: 'completed', label: '已完成' },
          { name: 'all', label: '全部' }
        ]"
        v-model="active"
        type="default"
        class="longhouse-tabs"
      >
        <template #to_be_rented_out>
          <ToBeRentedOutList :type="1" ref="toBeRentedOutRef" />
        </template>
        <template #waiting_for_check_in>
          <WaitingForCheckInList :type="2" ref="waitingForCheckInRef" />
        </template>
        <template #renting_out>
          <RentingOutList :type="3" ref="rentingOutRef" />
        </template>
        <template #resignation_in_rogress>
          <ResignationInRogressList :type="4" ref="resignationInRogressRef" />
        </template>
        <template #completed>
          <CompletedList :type="5" ref="completedRef" />
        </template>
        <template #all>
          <AllList :type="6" ref="allRef" />
        </template>
      </ele-tabs>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import ToBeRentedOutList from './to-be-rented-out-list.vue';
  import WaitingForCheckInList from './waiting-for-check-in-list.vue';
  import RentingOutList from './renting-out-list.vue';
  import ResignationInRogressList from './resignation-in-rogress-list.vue';
  import CompletedList from './completed-list.vue';
  import AllList from './all-list.vue';
  defineProps({
    title: String
  });

  const toBeRentedOutRef = ref(null);
  const waitingForCheckInRef = ref(null);
  const active = ref('to_be_rented_out');
</script>

<style lang="scss" scoped>
  div.longhouse-tabs.ele-tabs :deep(.el-tabs__header) {
    --ele-tab-padding: 0;
    --ele-tab-height: 46px;
    --ele-tab-font-size: 16px;
    --ele-tab-simple-hover-color: var(--el-color-primary);
    --ele-tab-simple-hover-bg: none;
    --ele-tab-simple-active-bg: none;
    --ele-tab-simple-active-weight: normal;

    .el-tabs__item {
      & + .el-tabs__item {
        margin-left: 40px;
      }

      &.is-active::after {
        height: 3px;
        width: 20px !important;
        left: 50%;
        transform: translateX(-50%);
        display: block;
      }
    }
  }
</style>
