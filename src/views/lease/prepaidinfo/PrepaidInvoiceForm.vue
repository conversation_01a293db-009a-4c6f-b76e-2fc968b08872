<template>
  <ele-modal
    form
    :width="880"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <ele-card header="客户信息">
        <el-row :gutter="8">
          <el-col :lg="12" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerId">
              <el-select
                v-model="form.customerId"
                placeholder="请选择"
                @change="handleCustomerChange"
              >
                <el-option
                  v-for="item in customerSimpleList"
                  :key="item.id"
                  :label="item.customerName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="开票金额" prop="invoiceAmount">
              <el-input
                class="input-200"
                disabled
                v-model="form.invoiceAmount"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
      <ele-card header="扣款信息">
        <ele-pro-table
          ref="tableRef"
          row-key="id"
          :columns="columns"
          :datasource="datasource"
          :show-overflow-tooltip="true"
          v-model:selections="selections"
          highlight-current-row
          :pagination="false"
          :toolbar="false"
          :empty-props="false"
          :disable="(row) => row.status === 3 && row.isInvoice === 0"
        >
          <template #isInvoice="{ row }">
            <dict-data
              type="tag"
              :code="DICT_TYPE.CO_PREPAID_ORDER_IS_INVOICE"
              :model-value="row.isInvoice"
            />
          </template>
        </ele-pro-table>
      </ele-card>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >确认</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PrepaidInfoApi from '@/api/lease/prepaidinfo';
  import * as PrepaidOrderApi from '@/api/lease/prepaidorder';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { useFormData } from '@/utils/use-form-data';
  import { watch } from 'vue'; // 引入 watch 函数
  import { EleMessage } from 'ele-admin-plus/es';

  /** 预存金信息 表单 */
  defineOptions({ name: 'PrepaidInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  // 客户信息列表
  const customerSimpleList = ref([]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ref([]);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    customerId: undefined,
    accountCode: undefined,
    socialCreditCode: undefined,
    amount: undefined,
    lockAmount: 0,
    rechargeAmount: undefined,
    deductAmount: undefined,
    refundAmount: undefined,
    extend: undefined,
    remark: undefined,
    billStatus: undefined,
    customerName: undefined,
    contactName: undefined,
    contactNumber: undefined,
    invoiceAmount: 0
  });

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'tradeTime',
      label: '扣款时间',
      minWidth: 100,
      formatter: dateFormatter
    },
    {
      prop: 'orderNo',
      label: '账单编码',
      minWidth: 110
    },
    {
      prop: 'actualAmount',
      label: '扣款金额',
      align: 'center',
      width: 180
    },
    {
      prop: 'isInvoice',
      label: '开票状态',
      align: 'center',
      width: 180,
      slot: 'isInvoice'
    }
  ]);

  /** 表单验证规则 */
  const rules = reactive({});
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    const prepaidOrderIds = selections.value.map((d) => d.id);
    try {
      await PrepaidInfoApi.prepaidOrderToInvoiceResponse({
        prepaidOrderIds: prepaidOrderIds
      });
      loading.value = false;
      EleMessage.success('提交成功');
    } finally {
      loading.value = false;
    }
  };

  // 监听 selections 变化，计算开票金额
  watch(selections, (newSelections) => {
    //form.invoiceAmount等于勾选行的实际金额之和，如果行状态为已开票，则不进行计算
    form.invoiceAmount = newSelections.reduce((sum, row) => {
      if (row.isInvoice === 0) {
        return sum + row.actualAmount;
      } else {
        return sum;
      }
    }, 0);
  });

  // 处理客户选择变化
  const handleCustomerChange = (customerId) => {
    const selectedCustomer = customerSimpleList.value.find(
      (item) => item.id === customerId
    );
    if (selectedCustomer) {
      form.contactName = selectedCustomer.contactName;
      form.contactNumber = selectedCustomer.contactNumber;
    } else {
      form.contactName = undefined;
      form.contactNumber = undefined;
    }
    // 重置 invoiceAmount 为 0 或其他默认值
    form.invoiceAmount = 0;
    if (selectedCustomer.prepaidId) {
      query(selectedCustomer.prepaidId);
    } else {
      datasource.value = [];
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    // 加载公告类型列表
    customerSimpleList.value = await PrepaidInfoApi.getCustomerInfoSimple();
    try {
      if (props.data) {
        const result = await PrepaidInfoApi.getPrepaidInfo(props.data.id);
        assignFields({ ...result });
        query(props.data.id);
      } else {
        resetFields();
      }
      title.value = '开票';
    } finally {
      loading.value = false;
    }
  };

  /** 查询 */
  const query = async (id) => {
    const result = await PrepaidOrderApi.getPrepaidOrderList({
      prepaidId: id,
      status: 3
    });
    datasource.value = result;
  };
</script>
