<template>
  <ele-modal
    form
    :width="1080"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <ele-card header="账户信息">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户编号" prop="accountCode">
              <el-input class="input-200" disabled v-model="form.accountCode" />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                class="input-200"
                disabled
                v-model="form.customerName"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户余额" prop="lockAmount">
              <el-input class="input-200" disabled v-model="form.lockAmount" />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
      <ele-card header="提醒信息">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="提醒金额" prop="remindAmount">
              <el-input-number
                class="input-200"
                v-model="form.prepaidRemindRespVO.remindAmount"
                :min="1"
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="提醒频率(小时)" prop="remindFrequency">
              <el-input-number
                class="input-200"
                v-model="form.prepaidRemindRespVO.remindFrequency"
                :min="1"
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                type="datetime"
                v-model="form.prepaidRemindRespVO.startTime"
                :disabled-date="disabledDate"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择开始时间"
                class="ele-fluid"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="最大提醒次数" prop="remindBigTimes">
              <el-input-number
                class="input-200"
                v-model="form.prepaidRemindRespVO.remindBigTimes"
                :min="1"
                :controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div style="overflow: auto">
          <ele-table style="min-width: 580px; table-layout: fixed">
            <colgroup>
              <col width="60px" />
              <col />
              <col />
              <col />
              <col width="100px" />
            </colgroup>
            <thead>
              <tr>
                <th style="position: sticky; left: 0; z-index: 98"></th>
                <th>对象</th>
                <th>人员</th>
                <th>提醒方式</th>
                <th
                  :style="{
                    textAlign: 'center',
                    position: 'sticky',
                    right: 0,
                    zIndex: 98
                  }"
                >
                  操作
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(row, index) in form.prepaidRemindLineRespVOS"
                :key="row.key"
              >
                <td
                  :style="{
                    textAlign: 'center',
                    position: 'sticky',
                    left: 0,
                    zIndex: 98
                  }"
                >
                  {{ index + 1 }}
                </td>
                <td>
                  <el-form-item
                    label-width="0px"
                    :prop="
                      'prepaidRemindLineRespVOS.' + index + '.remindObject'
                    "
                    :rules="{
                      required: true,
                      message: '请选择提醒对象',
                      type: 'string',
                      trigger: 'blur'
                    }"
                    class="form-error-popper"
                    style="margin-bottom: 0"
                  >
                    <el-select
                      clearable
                      v-model="row.remindObject"
                      placeholder="请选择提醒方式"
                      class="ele-fluid"
                    >
                      <el-option
                        v-for="dict in getIntDictOptions(
                          DICT_TYPE.CO_PREPAID_REMIND_OBJECT
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item
                    label-width="0px"
                    :prop="
                      'prepaidRemindLineRespVOS.' + index + '.remindUserId'
                    "
                    :rules="{
                      required: true,
                      message: '请选择提醒人',
                      type: 'string',
                      trigger: 'blur'
                    }"
                    class="form-error-popper"
                    style="margin-bottom: 0"
                  >
                    <!-- <ele-table-select
                      filterable
                      clearable
                      placeholder="请选择"
                      value-key="id"
                      v-model:model-value="row.remindUserId"
                      label-key="nickname"
                      v-model="selectedValue2"
                      :table-props="tableProps2"
                      :popper-width="580"
                      @filterChange="onFilterChange"
                      @visibleChange="onVisibleChange"
                    >
                      <template #depts="{ row }">
                        <el-tag
                          v-for="item in row.dept"
                          :key="item.deptId"
                          size="small"
                          :disable-transitions="true"
                        >
                          {{ item.deptName }}
                        </el-tag>
                      </template>
                    </ele-table-select> -->
                    <!-- <el-input
                      clearable
                      v-model="row.remindUserId"
                      placeholder="请选择提醒人"
                    /> -->
                    <el-select v-model="row.remindUserId" placeholder="请选择">
                      <el-option
                        v-for="item in customerSimpleList"
                        :key="item.id"
                        :label="item.nickname"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item
                    label-width="0px"
                    :prop="'prepaidRemindLineRespVOS.' + index + '.remindType'"
                    :rules="{
                      required: true,
                      message: '请选择提醒方式',
                      type: 'string',
                      trigger: 'change'
                    }"
                    class="form-error-popper"
                    style="margin-bottom: 0"
                  >
                    <el-select
                      clearable
                      v-model="row.remindType"
                      placeholder="请选择提醒方式"
                      class="ele-fluid"
                    >
                      <el-option
                        v-for="dict in getIntDictOptions(
                          DICT_TYPE.CO_PREPAID_MESSAGE_TYPE
                        )"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td
                  :style="{
                    textAlign: 'center',
                    position: 'sticky',
                    right: 0,
                    zIndex: 98
                  }"
                >
                  <el-link
                    type="danger"
                    :underline="false"
                    @click="removeRemindLine(row, index)"
                  >
                    删除
                  </el-link>
                </td>
              </tr>
              <tr
                v-if="
                  !form.prepaidRemindLineRespVOS ||
                  !form.prepaidRemindLineRespVOS.length
                "
              >
                <td colspan="5" style="text-align: center">
                  <ele-text style="padding: 4px 0" type="secondary">
                    暂无数据
                  </ele-text>
                </td>
              </tr>
            </tbody>
          </ele-table>
        </div>
        <el-button
          :icon="PlusOutlined"
          style="margin-top: 16px; width: 100%"
          @click="addRemindLine"
        >
          <span style="padding-top: 1px">新增成员</span>
        </el-button>
      </ele-card>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >确认</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PrepaidInfoApi from '@/api/lease/prepaidinfo';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { useFormData } from '@/utils/use-form-data';
  import { watch } from 'vue'; // 引入 watch 函数
  import { EleMessage } from 'ele-admin-plus/es';
  import { PlusOutlined } from '@/components/icons';
  import { getSimpleUserList } from '@/api/system/user';

  /** 预存金信息 表单 */
  defineOptions({ name: 'PrepaidInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  // 客户信息列表
  const customerSimpleList = ref([]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ref([]);

  /** 表格实例 */
  const tableRef = ref(null);

  const allData = ref([]);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    customerId: undefined,
    accountCode: undefined,
    socialCreditCode: undefined,
    amount: undefined,
    lockAmount: 0,
    rechargeAmount: undefined,
    deductAmount: undefined,
    refundAmount: undefined,
    extend: undefined,
    remark: undefined,
    billStatus: undefined,
    customerName: undefined,
    contactName: undefined,
    contactNumber: undefined,
    invoiceAmount: 0,
    prepaidRemindRespVO: {
      id: undefined,
      prepaidId: undefined,
      accountCode: undefined,
      remindAmount: undefined,
      remindFrequency: undefined,
      startTime: undefined,
      remindBigTimes: undefined,
      atributeVarchar1: undefined,
      atributeVarchar2: undefined,
      atributeVarchar3: undefined,
      atributeVarchar4: undefined,
      atributeVarchar5: undefined
    },
    prepaidRemindLineRespVOS: [
      {
        remindObject: '',
        remindUserId: '',
        remindTserName: '',
        remindType: '',
        remindPhone: '',
        remindEmail: ''
      }
    ]
  });

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'tradeTime',
      label: '扣款时间',
      minWidth: 100,
      formatter: dateFormatter
    },
    {
      prop: 'orderNo',
      label: '账单编码',
      minWidth: 110
    },
    {
      prop: 'actualAmount',
      label: '扣款金额',
      align: 'center',
      width: 180
    },
    {
      prop: 'isInvoice',
      label: '开票状态',
      align: 'center',
      width: 180,
      slot: 'isInvoice'
    }
  ]);

  /** 表单验证规则 */
  const rules = reactive({
    remindAmount: [
      { required: true, message: '请输入提醒金额', trigger: 'blur' },
      { type: 'number', min: 1, message: '提醒金额必须大于0', trigger: 'blur' }
    ],
    remindFrequency: [
      { required: true, message: '请输入提醒频率', trigger: 'blur' },
      { type: 'number', min: 1, message: '提醒频率必须大于0', trigger: 'blur' }
    ],
    remindBigTimes: [
      { required: true, message: '请输入最大提醒次数', trigger: 'blur' },
      {
        type: 'number',
        min: 1,
        message: '最大提醒次数必须大于0',
        trigger: 'blur'
      }
    ],
    startTime: [
      { required: true, message: '请选择开始时间', trigger: 'change' }
    ]
  });

  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    try {
      await PrepaidInfoApi.updatePrepaidRemindDetail({ ...form });
      loading.value = false;
      EleMessage.success('保存成功');
    } finally {
      loading.value = false;
    }
  };

  // 监听 selections 变化，计算开票金额
  watch(selections, (newSelections) => {
    form.invoiceAmount = newSelections.reduce(
      (sum, row) => sum + row.actualAmount,
      0
    );
  });

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    /** 查询表格数据 */
    // getSimpleUserList().then((data) => {
    //   tableProps2.datasource = data;
    //   allData.value = data;
    // });
    // 加载公告类型列表
    customerSimpleList.value = await getSimpleUserList();
    try {
      if (props.data) {
        const result = await PrepaidInfoApi.getPrepaidRemindDetail(
          props.data.id
        );
        assignFields({ ...result });
      } else {
        resetFields();
      }
      title.value = '开票';
    } finally {
      loading.value = false;
    }
  };

  /** 删除 */
  const removeRemindLine = (_row, index) => {
    form.prepaidRemindLineRespVOS.splice(index, 1);
  };

  /** 添加 */
  const addRemindLine = () => {
    form.prepaidRemindLineRespVOS.push({
      remindObject: '',
      remindUserId: '',
      remindTserName: '',
      remindType: '',
      remindPhone: '',
      remindEmail: ''
    });
  };

  /** 禁用当前时间之前的时间 */
  const disabledDate = (time) => {
    return time.getTime() < Date.now();
  };

  /** 表格下拉选中值 */
  const selectedValue2 = ref();
  /** 表格配置 */
  const tableProps2 = reactive({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'nickname',
        label: '用户名',
        slot: 'nickname'
      },
      {
        prop: 'deptName',
        label: '部门',
        width: 80
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  /** 筛选输入框值改变事件 */
  const onFilterChange = (keyword) => {
    tableProps2.datasource = allData.filter((d) => {
      return d.nickname?.includes?.(keyword) || d.deptName?.includes?.(keyword);
    });
  };

  /** 下拉框展开状态改变事件 */
  const onVisibleChange = (visible) => {
    if (visible) {
      tableProps2.datasource = allData;
    }
  };
</script>
<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
</style>
