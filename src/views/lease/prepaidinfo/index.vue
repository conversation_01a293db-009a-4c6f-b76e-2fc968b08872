<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户编号" prop="accountCode">
              <el-input
                v-model.trim="queryParams.accountCode"
                placeholder="请输入账户编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="socialCreditCode">
              <el-input
                v-model.trim="queryParams.socialCreditCode"
                placeholder="请输入客户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:prepaid-info:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            充值
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:prepaid-info:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #billStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_PREPAID_BILL_STATUS"
            :model-value="row.billStatus"
          />
        </template>
        <template #prepaidType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_PREPAID_TYPE"
            :model-value="row.prepaidType"
          />
        </template>
        <template #action="{ row }">
          <!-- <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:prepaid-info:update']"
          >
            充值
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:prepaid-info:delete']"
          /> -->
          <el-link
            :underline="false"
            type="primary"
            @click="refundEdit(row)"
            v-permission="['lease:prepaid-info:delete']"
          >
            退款
          </el-link>
          <el-divider v-if="moreItems.length" direction="vertical" />
          <ele-dropdown
            v-if="moreItems.length"
            :items="moreItems"
            style="display: inline"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon
                :size="12"
                style="vertical-align: -1px; margin-left: 2px"
              >
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
      </ele-pro-table>
    </ele-card>
    <PrepaidInfoForm v-model="showEdit" :data="current" @done="reload" />
    <PrepaidRefundForm
      v-model="showRefundEdit"
      :data="current"
      @done="reload"
    />
    <PrepaidInvoiceForm v-model="showInvoice" :data="current" @done="reload" />
    <PrepaidMessageForm v-model="showMessage" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as PrepaidInfoApi from '@/api/lease/prepaidinfo';
  import PrepaidInfoForm from './PrepaidInfoForm.vue';
  import PrepaidRefundForm from './PrepaidRefundForm.vue';
  import PrepaidInvoiceForm from './PrepaidInvoiceForm.vue';
  import PrepaidMessageForm from './PrepaidMessageForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  import { ref, computed } from 'vue';

  /** 预存金信息 列表 */
  defineOptions({ name: 'PrepaidInfoIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    customerId: undefined,
    accountCode: undefined,
    socialCreditCode: undefined,
    amount: undefined,
    lockAmount: undefined,
    rechargeAmount: undefined,
    deductAmount: undefined,
    refundAmount: undefined,
    extend: undefined,
    remark: undefined,
    createTime: [],
    billStatus: undefined,
    customerName: undefined,
    contactName: undefined,
    contactNumber: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    //   type: 'selection',
    //   columnKey: 'selection',
    //   width: 50,
    //   align: 'center',
    //   fixed: 'left'
    // },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'accountCode',
      label: '账户编号',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'prepaidType',
      label: '账户类型',
      align: 'center',
      minWidth: 80,
      slot: 'prepaidType'
    },
    {
      prop: 'customerName',
      label: '客户名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contactName',
      label: '联系人姓名',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contactNumber',
      label: '联系人电话',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'lockAmount',
      label: '账户余额',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'rechargeAmount',
      label: '累计充值',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'deduct_amount',
      label: '累计扣款',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'refund_amount',
      label: '累计退款',
      align: 'center',
      minWidth: 80
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 100,
      formatter: dateFormatter
    },
    {
      prop: 'billStatus',
      label: '账户状态',
      align: 'center',
      minWidth: 80,
      slot: 'billStatus'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示退款弹窗 */
  const showRefundEdit = ref(false);

  /** 是否显示开票弹窗 */
  const showInvoice = ref(false);

  /** 是否显示消息提醒弹窗 */
  const showMessage = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return PrepaidInfoApi.getPrepaidInfoPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开退款弹窗 */
  const refundEdit = (row) => {
    current.value = row ?? null;
    showRefundEdit.value = true;
  };

  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await PrepaidInfoApi.deletePrepaidInfo(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await PrepaidInfoApi.exportPrepaidInfo(queryParams);
      download.excel(data, '预存金信息.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    //items.push({ title: '开票', command: 'invoice' });
    items.push({ title: '提醒设置', command: 'message' });
    items.push({ title: '删除', command: 'delete' });
    return items;
  });

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    // if (key === 'invoice') {
    //   current.value = row ?? null;
    //   showInvoice.value = true;
    // } else 
    if (key === 'message') {
      current.value = row ?? null;
      showMessage.value = true;
    } else if (key === 'delete') {
      removeBatch(row);
    }
  };
</script>
