<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="客户ID" prop="customerId">
        <el-input v-model="form.customerId" placeholder="请输入客户ID" />
      </el-form-item>
      <el-form-item label="账户编号" prop="accountCode">
        <el-input v-model="form.accountCode" placeholder="请输入账户编号" />
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="socialCreditCode">
        <el-input
          v-model="form.socialCreditCode"
          placeholder="请输入统一社会信用代码"
        />
      </el-form-item>
      <el-form-item label="余额" prop="amount">
        <el-input v-model="form.amount" placeholder="请输入余额" />
      </el-form-item>
      <el-form-item label="余额" prop="lockAmount">
        <el-input v-model="form.lockAmount" placeholder="请输入余额" />
      </el-form-item>
      <el-form-item label="充值金额" prop="rechargeAmount">
        <el-input v-model="form.rechargeAmount" placeholder="请输入充值金额" />
      </el-form-item>
      <el-form-item label="扣款金额" prop="deductAmount">
        <el-input v-model="form.deductAmount" placeholder="请输入扣款金额" />
      </el-form-item>
      <el-form-item label="退款金额" prop="refundAmount">
        <el-input v-model="form.refundAmount" placeholder="请输入退款金额" />
      </el-form-item>
      <el-form-item label="扩展信息" prop="extend">
        <el-input v-model="form.extend" placeholder="请输入扩展信息" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="账户状态" prop="billStatus">
        <el-radio-group v-model="form.billStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PrepaidInfoApi from '@/api/lease/prepaidinfo';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 预存金信息 表单 */
  defineOptions({ name: 'PrepaidInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    customerId: undefined,
    accountCode: undefined,
    socialCreditCode: undefined,
    amount: undefined,
    lockAmount: undefined,
    rechargeAmount: undefined,
    deductAmount: undefined,
    refundAmount: undefined,
    extend: undefined,
    remark: undefined,
    billStatus: undefined
  });
  /** 表单验证规则 */
  const rules = reactive({});
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await PrepaidInfoApi.createPrepaidInfo(form);
        message.success(t('common.createSuccess'));
      } else {
        await PrepaidInfoApi.updatePrepaidInfo(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    try {
      if (props.data) {
        const result = await PrepaidInfoApi.getPrepaidInfo(props.data.id);
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
