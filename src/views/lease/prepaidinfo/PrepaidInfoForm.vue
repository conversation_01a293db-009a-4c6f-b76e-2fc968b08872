<template>
  <ele-modal
    form
    :width="680"
    v-model="visible"
    :close-on-click-modal="false"
    destroy-on-close
    :title="title"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <ele-card header="客户信息">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerId">
              <el-select
                v-model="form.customerId"
                placeholder="请选择"
                @change="handleCustomerChange"
              >
                <el-option
                  v-for="item in customerSimpleList"
                  :key="item.id"
                  :label="item.customerName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="联系人" prop="contactName">
              <el-input class="input-200" disabled v-model="form.contactName" />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="联系电话" prop="contactNumber">
              <el-input
                class="input-200"
                disabled
                v-model="form.contactNumber"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户编号" prop="accountCode">
              <el-input class="input-200" disabled v-model="form.accountCode" />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户状态" prop="billStatus">
              <el-select
                clearable
                v-model="form.billStatus"
                placeholder=""
                class="ele-fluid"
                disabled
              >
                <el-option
                  v-for="dict in getStrDictOptions(
                    DICT_TYPE.CO_PREPAID_BILL_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="账户余额" prop="lockAmount">
              <el-input class="input-200" disabled v-model="form.lockAmount" />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
      <ele-card header="充值信息">
        <el-row :gutter="8">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="充值金额" prop="rechargeAmount">
              <el-input
                class="input-200"
                type="number"
                v-model="form.rechargeInfo.amount"
                placeholder="请输入充值金额"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="开始时间">
              <el-date-picker
                class="input-200"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                type="date"
                v-model="form.rechargeInfo.startTime"
                placeholder="开始时间"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="结束时间">
              <el-date-picker
                class="input-200"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                type="date"
                v-model="form.rechargeInfo.endTime"
                placeholder="结束时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </ele-card>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading"
        >保存</el-button
      >
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
  import * as PrepaidInfoApi from '@/api/lease/prepaidinfo';
  import { DICT_TYPE, getStrDictOptions } from '@/utils/dict';
  import { useFormData } from '@/utils/use-form-data';
  import dayjs from 'dayjs'; // 引入 dayjs 库

  /** 预存金信息 表单 */
  defineOptions({ name: 'PrepaidInfoForm' });

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  // 客户信息列表
  const customerSimpleList = ref([]);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    customerId: undefined,
    accountCode: undefined,
    socialCreditCode: undefined,
    amount: undefined,
    lockAmount: 0,
    rechargeAmount: undefined,
    deductAmount: undefined,
    refundAmount: undefined,
    extend: undefined,
    remark: undefined,
    billStatus: undefined,
    customerName: undefined,
    contactName: undefined,
    contactNumber: undefined,
    rechargeInfo: {
      amount: 0,
      actualAmount: 0,
      startTime: dayjs().format('YYYY/MM/DD'), // 设置默认开始时间为当前时间
      endTime: dayjs().add(1, 'year').format('YYYY/MM/DD'), // 设置默认结束时间为10年后
      type: 1,
      opType: 1,
      opSubType: 1,
      refundAmount: 0,
      deductionAmount: 0
    }
  });

  /** 表单验证规则 */
  const rules = reactive({});
  /** 关闭弹窗 */
  const cancle = () => {
    visible.value = false;
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      // 转换开始时间和结束时间为时间戳
      const submitForm = {
        ...form,
        rechargeInfo: {
          ...form.rechargeInfo,
          startTime: new Date(form.rechargeInfo.startTime).getTime(),
          endTime: new Date(form.rechargeInfo.endTime).getTime()
        }
      };

      if (!isUpdate.value) {
        await PrepaidInfoApi.createPrepaidInfoResponse(submitForm);
        message.success(t('common.createSuccess'));
      } else {
        await PrepaidInfoApi.updatePrepaidInfoResponse(submitForm);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  // 处理客户选择变化
  const handleCustomerChange = (customerId) => {
    const selectedCustomer = customerSimpleList.value.find(
      (item) => item.id === customerId
    );
    if (selectedCustomer) {
      form.contactName = selectedCustomer.contactName;
      form.contactNumber = selectedCustomer.contactNumber;
    } else {
      form.contactName = undefined;
      form.contactNumber = undefined;
    }
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    loading.value = true;
    // 加载公告类型列表
    customerSimpleList.value = await PrepaidInfoApi.getCustomerInfoSimple();
    try {
      if (props.data) {
        const result = await PrepaidInfoApi.getPrepaidInfo(props.data.id);
        result.rechargeInfo = {
          amount: 0,
          startTime: dayjs().format('YYYY/MM/DD'), // 设置默认开始时间为当前时间
          endTime: dayjs().add(10, 'year').format('YYYY/MM/DD') // 设置默认结束时间为10年后
        };
        assignFields({ ...result });
        isUpdate.value = true;
      } else {
        resetFields();
        isUpdate.value = false;
      }
      title.value = isUpdate.value ? t('action.update') : t('action.create');
    } finally {
      loading.value = false;
    }
  };
</script>
