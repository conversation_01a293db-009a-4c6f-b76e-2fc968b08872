<template>
  <ele-page plain hide-footer :multi-card="false">
    <ele-page>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="130px"
        inline
        @submit.prevent=""
      >
        <ele-card header="合同到期提醒">
          <el-form-item label="租赁日期到期前" prop="description">
            <div class="form-flex">
              <el-input-number
                controls-position="right"
                class="wh-150"
              ></el-input-number>
              <el-select clearable placeholder="请选择" class="wh-150">
                <el-option value="private" label="天" />
                <el-option value="public" label="月" /> </el-select
            ></div>
          </el-form-item>
          <el-form-item label="通知频率" prop="description">
            <div class="form-flex">
              <el-select clearable class="wh-200" placeholder="请选择">
                <el-option value="private" label="到期当前时间" />
                <el-option value="public" label="到期当前当天与到期当天" />
              </el-select>
              <el-time-picker
                v-model="value1"
                class="wh-150"
                placeholder="任意时间点"
              >
              </el-time-picker>
            </div>
          </el-form-item>
          <el-form-item label="通知人" prop="description">
            <el-select clearable class="wh-300" placeholder="请选择通知人">
              <el-option value="private" label="到期当前时间" />
              <el-option value="public" label="到期当前当天与到期当天" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知方式" prop="description">
            <el-checkbox>站内信</el-checkbox>
          </el-form-item>
        </ele-card>
        <ele-card header="收款提醒">
          <el-form-item label="每期收款日前" prop="description">
            <div class="form-flex">
              <el-input-number
                controls-position="right"
                class="wh-150"
              ></el-input-number>
              <el-select clearable placeholder="请选择" class="wh-150">
                <el-option value="private" label="天" />
                <el-option value="public" label="月" /> </el-select
            ></div>
          </el-form-item>
          <el-form-item label="通知频率" prop="description">
            <div class="form-flex">
              <el-select clearable class="wh-200" placeholder="请选择">
                <el-option value="private" label="到期当前时间" />
                <el-option value="public" label="到期当前当天与到期当天" />
              </el-select>
              <el-time-picker
                v-model="value1"
                class="wh-150"
                placeholder="任意时间点"
              >
              </el-time-picker>
            </div>
          </el-form-item>
          <el-form-item label="通知人" prop="description">
            <el-select clearable class="wh-300" placeholder="请选择通知人">
              <el-option value="private" label="到期当前时间" />
              <el-option value="public" label="到期当前当天与到期当天" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知方式" prop="description">
            <el-checkbox>站内信</el-checkbox>
          </el-form-item>
        </ele-card>
        <ele-card header="付款提醒">
          <el-form-item label="每期付款日前" prop="description">
            <div class="form-flex">
              <el-input-number
                controls-position="right"
                class="wh-150"
              ></el-input-number>
              <el-select clearable placeholder="请选择" class="wh-150">
                <el-option value="private" label="天" />
                <el-option value="public" label="月" /> </el-select
            ></div>
          </el-form-item>
          <el-form-item label="通知频率" prop="description">
            <div class="form-flex">
              <el-select clearable class="wh-200" placeholder="请选择">
                <el-option value="private" label="到期当前时间" />
                <el-option value="public" label="到期当前当天与到期当天" />
              </el-select>
              <el-time-picker
                v-model="value1"
                class="wh-150"
                placeholder="任意时间点"
              >
              </el-time-picker>
            </div>
          </el-form-item>
          <el-form-item label="通知人" prop="description">
            <el-select clearable class="wh-300" placeholder="请选择通知人">
              <el-option value="private" label="到期当前时间" />
              <el-option value="public" label="到期当前当天与到期当天" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知方式" prop="description">
            <el-checkbox>站内信</el-checkbox>
          </el-form-item>
        </ele-card>
      </el-form>
    </ele-page>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button type="primary" :loading="loading" @click="submit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined, PlusOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import type { UserItem } from '@/api/example/model';
  import { listAddedUsers } from '@/api/example';

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
</script>

<script lang="ts">
  export default {
    name: 'FormAdvanced'
  };
</script>

<style lang="scss" scoped>
  .form-flex {
    display: flex;
    align-items: center;
  }
  .wh-150 {
    width: 150px;
    margin-right: 10px;
  }
  .wh-200 {
    width: 200px;
    margin-right: 10px;
  }
  .wh-300 {
    width: 310px;
  }
</style>
