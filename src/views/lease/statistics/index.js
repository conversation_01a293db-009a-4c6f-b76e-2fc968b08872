import { defineComponent, reactive, toRefs, ref } from 'vue';
import { useRouter } from 'vue-router';
export default defineComponent({
  setup() {
    //获取表单form元素
    const ruleForm = ref(null);
    const router = useRouter();
    const _that = reactive({
      formInfoOne: { type: 'month', building: '', houseType: 2 },
      formInfoTwo: { type: 'month', building: '', houseType: 2 },
      formInfoThree: { type: 'month', building: '' },
      formInfoFour: { type: 'month', building: '', houseType: 2 },
      timeTypeList: [
        { label: '按月', value: 'month' },
        { label: '按年', value: 'year' },
        { label: '按周', value: 'week' },
        { label: '自定义', value: 'daterange' }
      ]
    });
    return {
      ...toRefs(_that)
    };
  }
});
