<template>
  <ele-page flex-table>
    <!--租赁面积-->
    <ele-card :body-style="{ paddingTop: '8px' }">
      <div class="statis-header">
        <div class="header-left">租赁面积</div>
        <div class="header-right">
          <el-form :inline="true">
            <el-form-item>
              <el-select
                v-model="formInfoOne.type"
                class="input-120"
                placeholder="请选择"
                @change="formInfoOne.dateTime = ''"
              >
                <el-option
                  v-for="item of timeTypeList"
                  :key="item"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="formInfoOne.dateTime"
                :type="formInfoOne.type"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="formInfoOne.building"
                class="input-180"
                placeholder="请选择"
              >
                <el-option label="1#产业大楼" :value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-radio-group v-model="formInfoOne.houseType">
                <el-radio-button label="租入面积" :value="1" />
                <el-radio-button label="租出面积" :value="2" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </ele-card>
    <!--出租情况-->
    <ele-card :body-style="{ paddingTop: '8px' }">
      <div class="statis-header">
        <div class="header-left">出租情况</div>
        <div class="header-right">
          <el-form :inline="true">
            <el-form-item>
              <el-select
                v-model="formInfoTwo.type"
                class="input-120"
                placeholder="请选择"
                @change="formInfoTwo.dateTime = ''"
              >
                <el-option
                  v-for="item of timeTypeList"
                  :key="item"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="formInfoTwo.dateTime"
                :type="formInfoTwo.type"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="formInfoTwo.building"
                class="input-180"
                placeholder="请选择"
              >
                <el-option label="1#产业大楼" :value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-radio-group v-model="formInfoTwo.houseType">
                <el-radio-button label="出租率" :value="1" />
                <el-radio-button label="空置率" :value="2" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </ele-card>
    <!--出租分析--->
    <ele-card :body-style="{ paddingTop: '8px' }">
      <div class="statis-header">
        <div class="header-left">出租分析</div>
        <div class="header-right">
          <el-form :inline="true">
            <el-form-item>
              <el-select
                v-model="formInfoThree.type"
                class="input-120"
                placeholder="请选择"
                @change="formInfoThree.dateTime = ''"
              >
                <el-option
                  v-for="item of timeTypeList"
                  :key="item"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="formInfoThree.dateTime"
                :type="formInfoThree.type"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="formInfoThree.building"
                class="input-180"
                placeholder="请选择"
              >
                <el-option label="1#产业大楼" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </ele-card>
    <!--退租面积-->
    <ele-card :body-style="{ paddingTop: '8px' }">
      <div class="statis-header">
        <div class="header-left">退租面积</div>
        <div class="header-right">
          <el-form :inline="true">
            <el-form-item>
              <el-select
                v-model="formInfoFour.type"
                class="input-120"
                placeholder="请选择"
                @change="formInfoFour.dateTime = ''"
              >
                <el-option
                  v-for="item of timeTypeList"
                  :key="item"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                v-model="formInfoFour.dateTime"
                :type="formInfoFour.type"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="formInfoFour.building"
                class="input-180"
                placeholder="请选择"
              >
                <el-option label="1#产业大楼" :value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-radio-group v-model="formInfoFour.houseType">
                <el-radio-button label="租入面积" :value="1" />
                <el-radio-button label="租出面积" :value="2" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </ele-card>
  </ele-page>
</template>
<script src="./index.js"></script>
<style lang="scss" scoped>
  @import url('./index.scss');
</style>
