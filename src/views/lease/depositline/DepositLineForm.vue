<template>
  <ele-modal
      form
      :width="680"
      v-model="visible"
      :close-on-click-modal="false"
      destroy-on-close
      :title="title"
      @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="押金id" prop="depositId">
        <el-input v-model="form.depositId" placeholder="请输入押金id" />
      </el-form-item>
      <el-form-item label="操作类型 1-收款 2-扣款 3-退款" prop="operaType">
        <el-select v-model="form.operaType" placeholder="请选择操作类型 1-收款 2-扣款 3-退款">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作金额" prop="operaAmount">
        <el-input v-model="form.operaAmount" placeholder="请输入操作金额" />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          type="date"
          value-format="x"
          placeholder="选择开始时间"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="date"
          value-format="x"
          placeholder="选择结束时间"
        />
      </el-form-item>
      <el-form-item label="收款方式 1-银行转账 2-现金 3-支票 4-汇票 5-其他" prop="receiveType">
        <el-select v-model="form.receiveType" placeholder="请选择收款方式 1-银行转账 2-现金 3-支票 4-汇票 5-其他">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="收款账号" prop="receiveAccount">
        <el-input v-model="form.receiveAccount" placeholder="请输入收款账号" />
      </el-form-item>
      <el-form-item label="收款时间" prop="receiveDate">
        <el-date-picker
          v-model="form.receiveDate"
          type="date"
          value-format="x"
          placeholder="选择收款时间"
        />
      </el-form-item>
      <el-form-item label="退款方式 1-银行转账 2-现金" prop="refundType">
        <el-select v-model="form.refundType" placeholder="请选择退款方式 1-银行转账 2-现金">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="退款账号" prop="refundAccount">
        <el-input v-model="form.refundAccount" placeholder="请输入退款账号" />
      </el-form-item>
      <el-form-item label="退款时间" prop="refundDate">
        <el-date-picker
          v-model="form.refundDate"
          type="date"
          value-format="x"
          placeholder="选择退款时间"
        />
      </el-form-item>
      <el-form-item label="附件信息" prop="actFile">
        <UploadFile v-model="form.actFile" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="备用字段1" prop="atributeVarchar1">
        <el-input v-model="form.atributeVarchar1" placeholder="请输入备用字段1" />
      </el-form-item>
      <el-form-item label="备用字段2" prop="atributeVarchar2">
        <el-input v-model="form.atributeVarchar2" placeholder="请输入备用字段2" />
      </el-form-item>
      <el-form-item label="备用字段3" prop="atributeVarchar3">
        <el-input v-model="form.atributeVarchar3" placeholder="请输入备用字段3" />
      </el-form-item>
      <el-form-item label="备用字段4" prop="atributeVarchar4">
        <el-input v-model="form.atributeVarchar4" placeholder="请输入备用字段4" />
      </el-form-item>
      <el-form-item label="备用字段5" prop="atributeVarchar5">
        <el-input v-model="form.atributeVarchar5" placeholder="请输入备用字段5" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading">保存</el-button>
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
import * as DepositLineApi from '@/api/lease/depositline'
import TinymceEditor from '@/components/TinymceEditor/index.vue';
import { useFormData } from '@/utils/use-form-data';

/** 押金行信息 表单 */
defineOptions({ name: 'DepositLineForm' })

const props = defineProps({
  /** 修改回显的数据 */
  data: Object
});

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const emit = defineEmits(['done']);

const title = ref('') // 弹窗的标题
/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean });

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
                    id: undefined,
                    depositId: undefined,
                    operaType: undefined,
                    operaAmount: undefined,
                    startTime: undefined,
                    endTime: undefined,
                    receiveType: undefined,
                    receiveAccount: undefined,
                    receiveDate: undefined,
                    refundType: undefined,
                    refundAccount: undefined,
                    refundDate: undefined,
                    actFile: undefined,
                    remark: undefined,
                    atributeVarchar1: undefined,
                    atributeVarchar2: undefined,
                    atributeVarchar3: undefined,
                    atributeVarchar4: undefined,
                    atributeVarchar5: undefined,
});
/** 表单验证规则 */
const rules  = reactive({
                depositId: [{ required: true, message: '押金id不能为空', trigger: 'blur' }],
                operaType: [{ required: true, message: '操作类型 1-收款 2-扣款 3-退款不能为空', trigger: 'change' }],
                operaAmount: [{ required: true, message: '操作金额不能为空', trigger: 'blur' }],
});
/** 关闭弹窗 */
const cancle = () => {
  visible.value = false;
};

const save = async () => {
  if (!formRef) return
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  loading.value = true
  try {
    if (!isUpdate.value) {
      await DepositLineApi.createDepositLine(form)
      message.success(t('common.createSuccess'))
    } else {
      await DepositLineApi.updateDepositLine(form)
      message.success(t('common.updateSuccess'))
    }
    visible.value = false
    // 发送操作成功的事件
    emit('done')
  } finally {
    loading.value = false
  }
}

/** 弹窗打开事件 */
const handleOpen = async () => {
  loading.value = true
  try{
    if (props.data) {
      const result = await DepositLineApi.getDepositLine(props.data.id)
      assignFields({...result});
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    title.value = isUpdate.value?t('action.update'): t('action.create')
  }finally {
    loading.value = false
  }
}
</script>