<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="押金id" prop="depositId">
                    <el-input
                            v-model.trim="queryParams.depositId"
                            placeholder="请输入押金id"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="操作类型 1-收款 2-扣款 3-退款" prop="operaType">
                    <el-select
                            v-model="queryParams.operaType"
                            placeholder="请选择操作类型 1-收款 2-扣款 3-退款"
                            clearable
                    >
                        <el-option label="请选择字典生成" value="" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="操作金额" prop="operaAmount">
                    <el-input
                            v-model.trim="queryParams.operaAmount"
                            placeholder="请输入操作金额"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                  <el-col :lg="6" :md="12" :sm="12" :xs="24">
                    <el-form-item label="开始时间" prop="startTime">
                      <el-date-picker
                              v-model="queryParams.startTime"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                              class="!w-220px"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :lg="6" :md="12" :sm="12" :xs="24">
                    <el-form-item label="结束时间" prop="endTime">
                      <el-date-picker
                              v-model="queryParams.endTime"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                              class="!w-220px"
                      />
                    </el-form-item>
                  </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="收款方式 1-银行转账 2-现金 3-支票 4-汇票 5-其他" prop="receiveType">
                    <el-select
                            v-model="queryParams.receiveType"
                            placeholder="请选择收款方式 1-银行转账 2-现金 3-支票 4-汇票 5-其他"
                            clearable
                    >
                        <el-option label="请选择字典生成" value="" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="收款账号" prop="receiveAccount">
                    <el-input
                            v-model.trim="queryParams.receiveAccount"
                            placeholder="请输入收款账号"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                  <el-col :lg="6" :md="12" :sm="12" :xs="24">
                    <el-form-item label="收款时间" prop="receiveDate">
                      <el-date-picker
                              v-model="queryParams.receiveDate"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                              class="!w-220px"
                      />
                    </el-form-item>
                  </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="退款方式 1-银行转账 2-现金" prop="refundType">
                    <el-select
                            v-model="queryParams.refundType"
                            placeholder="请选择退款方式 1-银行转账 2-现金"
                            clearable
                    >
                        <el-option label="请选择字典生成" value="" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="退款账号" prop="refundAccount">
                    <el-input
                            v-model.trim="queryParams.refundAccount"
                            placeholder="请输入退款账号"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                  <el-col :lg="6" :md="12" :sm="12" :xs="24">
                    <el-form-item label="退款时间" prop="refundDate">
                      <el-date-picker
                              v-model="queryParams.refundDate"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                              class="!w-220px"
                      />
                    </el-form-item>
                  </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                            v-model.trim="queryParams.remark"
                            placeholder="请输入备注"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段1" prop="atributeVarchar1">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar1"
                            placeholder="请输入备用字段1"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段2" prop="atributeVarchar2">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar2"
                            placeholder="请输入备用字段2"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段3" prop="atributeVarchar3">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar3"
                            placeholder="请输入备用字段3"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段4" prop="atributeVarchar4">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar4"
                            placeholder="请输入备用字段4"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :lg="6" :md="12" :sm="12" :xs="24">
                  <el-form-item label="备用字段5" prop="atributeVarchar5">
                    <el-input
                            v-model.trim="queryParams.atributeVarchar5"
                            placeholder="请输入备用字段5"
                            clearable
                    />
                  </el-form-item>
                </el-col>
                  <el-col :lg="6" :md="12" :sm="12" :xs="24">
                    <el-form-item label="创建时间" prop="createTime">
                      <el-date-picker
                              v-model="queryParams.createTime"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                              class="!w-220px"
                      />
                    </el-form-item>
                  </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
              >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
              ref="tableRef"
              row-key="id"
              :columns="columns"
              :datasource="datasource"
              :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
                  type="primary"
                  class="ele-btn-icon"
                  v-permission="['lease:deposit-line:create']"
                  :icon="Plus"
                  @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
                  class="ele-btn-icon"
                  v-permission="['lease:deposit-line:export']"
                  :icon="DownloadOutlined"
                  @click="exportData"
                  :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link
                  :underline="false"
                  type="primary"
                  @click="openEdit(row)"
                  v-permission="['lease:deposit-line:update']"
          >
            编辑
          </el-link>
          <el-divider
                  direction="vertical"
                  v-permission="['lease:deposit-line:delete']"
          />
          <el-link
                  :underline="false"
                  type="primary"
                  @click="removeBatch(row)"
                  v-permission="['lease:deposit-line:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <DepositLineForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
import { dateFormatter } from '@/utils/formatTime';
import { useMessage } from '@/hooks/web/useMessage';
import download from '@/utils/download'
import * as DepositLineApi  from '@/api/lease/depositline'
import DepositLineForm from './DepositLineForm.vue'
import { useFormData } from '@/utils/use-form-data';

/** 押金行信息 列表 */
defineOptions({ name: 'DepositLineIndex' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
  /** 表单数据 */
const [queryParams, resetFields] = useFormData({
            depositId: undefined,
            operaType: undefined,
            operaAmount: undefined,
            startTime: [],
            endTime: [],
            receiveType: undefined,
            receiveAccount: undefined,
            receiveDate: [],
            refundType: undefined,
            refundAccount: undefined,
            refundDate: [],
            actFile: undefined,
            remark: undefined,
            atributeVarchar1: undefined,
            atributeVarchar2: undefined,
            atributeVarchar3: undefined,
            atributeVarchar4: undefined,
            atributeVarchar5: undefined,
            createTime: [],
});
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
        {
          prop: 'id',
          label: 'id',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'depositId',
          label: '押金id',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'operaType',
          label: '操作类型 1-收款 2-扣款 3-退款',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'operaAmount',
          label: '操作金额',
          align: 'center',
          minWidth: 110,
        },
        {
          prop:  'startTime',
          label: '开始时间',
          align: 'center',
          minWidth: 130,
          formatter: dateFormatter
        },
        {
          prop:  'endTime',
          label: '结束时间',
          align: 'center',
          minWidth: 130,
          formatter: dateFormatter
        },
        {
          prop: 'receiveType',
          label: '收款方式 1-银行转账 2-现金 3-支票 4-汇票 5-其他',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'receiveAccount',
          label: '收款账号',
          align: 'center',
          minWidth: 110,
        },
        {
          prop:  'receiveDate',
          label: '收款时间',
          align: 'center',
          minWidth: 130,
          formatter: dateFormatter
        },
        {
          prop: 'refundType',
          label: '退款方式 1-银行转账 2-现金',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'refundAccount',
          label: '退款账号',
          align: 'center',
          minWidth: 110,
        },
        {
          prop:  'refundDate',
          label: '退款时间',
          align: 'center',
          minWidth: 130,
          formatter: dateFormatter
        },
        {
          prop: 'actFile',
          label: '附件信息',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'remark',
          label: '备注',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar1',
          label: '备用字段1',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar2',
          label: '备用字段2',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar3',
          label: '备用字段3',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar4',
          label: '备用字段4',
          align: 'center',
          minWidth: 110,
        },
        {
          prop: 'atributeVarchar5',
          label: '备用字段5',
          align: 'center',
          minWidth: 110,
        },
        {
          prop:  'createTime',
          label: '创建时间',
          align: 'center',
          minWidth: 130,
          formatter: dateFormatter
        },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false) // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return DepositLineApi.getDepositLinePage({ ...where, ...filters, ...pages });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await DepositLineApi.deleteDepositLine(row.id)
      message.success(t('common.delSuccess'))
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm()
      // 发起导出
      exportLoading.value = true
      const data = await DepositLineApi.exportDepositLine(queryParams)
      download.excel(data, '押金行信息.xls')
    } catch {
    } finally {
      exportLoading.value = false
    }
  };
</script>