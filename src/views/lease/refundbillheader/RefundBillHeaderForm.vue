<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 账单维护 </span>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="140px"
    @submit.prevent=""
  >
    <ele-card header="头信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="退款编号" prop="refundCode">
            <el-input
              maxlength="40"
              disabled
              clearable
              v-model="form.refundCode"
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.customerName"
              placeholder="根据行信息自动获取"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="应退总金额" prop="refundableTotalAmt">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.refundableTotalAmt"
              placeholder="根据行信息自动计算"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="实退总金额" prop="refundTotalAmt">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.refundTotalAmt"
              placeholder="根据行信息自动计算"
            />
          </el-form-item>
        </el-col> -->
      </el-row>
    </ele-card>
    <ele-card style="min-height: 350px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
        <el-tab-pane label="账单信息" name="0">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getRefundDel(1, '')">
                添加
              </el-button>
              <!-- <el-button type="primary" @click="importData"> 导入 </el-button> -->
            </div>
            <el-table
              :data="form.refundBillLineList"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="账单编号">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`refundBillLineList.${$index}.billNo`"
                  >
                    <el-input disabled v-model="row.billNo"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="账单开始日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`refundBillLineList.${$index}.startDate`"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.startDate"
                      disabled
                      value-format="x"
                      placeholder="账单开始日期"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="账单结束日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`refundBillLineList.${$index}.endDate`"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.endDate"
                      disabled
                      value-format="x"
                      placeholder="账单结束日期"
                      class="ele-fluid"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="可退租金(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`refundBillLineList.${$index}.billAmt`"
                  >
                    <el-input-number
                      :min="0"
                      disabled
                      :precision="2"
                      v-model="row.billAmt"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="退款金额(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`refundBillLineList.${$index}.refundableTotalAmt`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入退款金额',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      :min="0"
                      :precision="2"
                      v-model="row.refundableTotalAmt"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getRefundDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </el-form>
  <!-- 底部工具栏 -->
  <ele-bottom-bar>
    <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
      <span>{{ validMsg }}</span>
    </ele-text>
    <template #extra>
      <el-button type="primary" :loading="loading" class="ml-2" @click="submit"
        >保存</el-button
      >
    </template>
  </ele-bottom-bar>
  <ContractBillForm v-model="showContractBill" @done="recordHandle" />
</template>

<script lang="ts" setup>
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import ContractBillForm from './ContractBillForm.vue';
  import { formatDate2 } from '@/utils/formatTime';
  import { getCustomerPage } from '@/api/lease/attract/index';
  import { updateFile, getGeneratePlan } from '@/api/lease/contract';
  import * as RefundBillHeaderApi from '@/api/lease/refundbillheader';
  import { getLongHouseLov } from '@/api/lease/longhouse/index';
  import { is } from 'bpmn-js/lib/util/ModelUtil';
  const activeName = ref('0');
  const props = defineProps({
    data: Object
  });
  /** 加载状态 */
  const loading = ref(false);
  const emit = defineEmits(['done']);
  const isUpdate = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);
  /** 是否显示合同账单弹窗 */
  const showContractBill = ref(false);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    refundCode: undefined,
    customerId: undefined,
    customerName: undefined,
    contractType: undefined,
    refundTotalAmt: undefined,
    refundableTotalAmt: undefined,
    status: undefined,
    remark: undefined,
    refundBillLineList: []
  });

  const importData = () => {
    showImport.value = true;
  };

  /** 表单验证失败提示信息 */
  const validMsg = ref('');

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      try {
        loading.value = true;
        validMsg.value = '';
        loading.value = true;
        let res = null;
        let params = {
          ...form
        };
        if (!isUpdate.value) {
          res = await  RefundBillHeaderApi.createRefundBillHeader(params);
        } else {
          res = await RefundBillHeaderApi.updateRefundBillHeader(params);
        }
        if (res) {
          EleMessage.success('提交成功');
          cancle();
        }
      } finally {
        loading.value = false;
      }
    });
  };
  //新增退款信息
  const getRefundDel = (type, index) => {
    if (type == 1) {
      showContractBill.value = true;
    } else {
      form.refundBillLineList.splice(index, 1);
    }
  };
  const initFormInfo = async () => {
    resetFields();
    await loadData();
  };
  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      isUpdate.value = true;
      loading.value = true;
      try {
        // 获取最新数据
        const response = await  RefundBillHeaderApi.getRefundBillHeaderDetail(props.data.id);
        if (response) {
          assignFields(response);
          //定义一个数组
          form.dateTime = [];
          form.dateTime.push(response.startDate);
          form.dateTime.push(response.endDate);
        }
      } catch (error) {
        console.error('获取合同详情失败', error);
      } finally {
        loading.value = false;
      }
    } else {
      isUpdate.value = false;
    }
  };
  defineExpose({ initFormInfo });

  /** 关闭弹窗 */
  const recordHandle = (data) => {
    if (data) {
      for (let i = 0; i < data.length; i++) {
        // 判断 form.refundBillLineList 是否已存在相同的 sourceId
        const isExist = form.refundBillLineList.some(
          (item) => item.sourceId === data[i].sourceId
        );
        if (!isExist) {
          form.refundBillLineList.push({
            sourceId: data[i].sourceId,
            billNo: data[i].billNo,
            startDate: data[i].startDate,
            endDate: data[i].endDate,
            billAmt: data[i].billAmt,
            refundableTotalAmt: data[i].refundableTotalAmt
          });
        }
      }
    }
  };

</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>
