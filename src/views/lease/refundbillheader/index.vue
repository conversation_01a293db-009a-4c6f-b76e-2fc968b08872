<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card v-if="showStep == 1" :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="退款编号" prop="refundCode">
              <el-input
                v-model.trim="queryParams.refundCode"
                placeholder="请输入退款编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="客户名称" prop="customerName">
              <el-input
                v-model.trim="queryParams.customerName"
                placeholder="请输入客户名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态" prop="status">
              <el-select
                clearable
                v-model="queryParams.status"
                placeholder=""
                class="ele-fluid"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_REFUND_BILL_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card
      v-if="showStep == 1"
      flex-table
      :body-style="{ paddingTop: '8px' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:refund-bill-header:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:refund-bill-header:update']"
            :icon="Plus"
            @click="submitBatch()"
          >
            提交
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:refund-bill-header:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_REFUND_BILL_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #contractType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_REFUND_BILL_TYPE"
            :model-value="row.contractType"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:refund-bill-header:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:refund-bill-header:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:refund-bill-header:delete']"
          >
            删除
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:refund-bill-header:update']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="synBatch(row)"
            v-permission="['lease:refund-bill-header:update']"
          >
            同步
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <RefundBillHeaderForm
      ref="refundBillHeaderRef"
      v-if="showStep == 2"
      :data="current"
      @done="reload"
    />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as RefundBillHeaderApi from '@/api/lease/refundbillheader';
  import RefundBillHeaderForm from './RefundBillHeaderForm.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { EleMessage } from 'ele-admin-plus/es';
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';

  /** 退款账单管理 列表 */
  defineOptions({ name: 'RefundBillHeaderIndex' });

  /** 表格选中数据 */
  const selections = ref([]);
  /** 提交状态 */
  const loading = ref(false);

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  const showStep = ref(1); // 当前步骤
  const refundBillHeaderRef = ref(null);
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    refundCode: undefined,
    customerId: undefined,
    customerName: undefined,
    contractType: undefined,
    refundTotalAmt: undefined,
    refundableTotalAmt: undefined,
    status: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: [],
    externalCustomerId: undefined,
    synTime: [],
    synStatus: undefined,
    synMessage: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'refundCode',
      label: '退款编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'customerName',
      label: '客户',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contractType',
      label: '退款类型',
      align: 'center',
      minWidth: 110,
      slot: 'contractType'
    },
    {
      prop: 'refundTotalAmt',
      label: '退款总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'refundableTotalAmt',
      label: '应退总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      minWidth: 110,
      slot: 'status'
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return RefundBillHeaderApi.getRefundBillHeaderPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    showStep.value = 1;
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    refundBillHeaderRef.value?.initFormInfo(current);
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await RefundBillHeaderApi.deleteRefundBillHeader(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data =
        await RefundBillHeaderApi.exportRefundBillHeader(queryParams);
      download.excel(data, '退款账单管理.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };

  /** 批量提交 */
  const submitBatch = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }

    // 检查退款账单状态是否为新建
    const allowedStatuses = [1];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有新建的数据项才能提交');
      return;
    }

    loading.value = true;
    const batchIds = selections.value.map((d) => d.id);
    try {
      await RefundBillHeaderApi.submitRefundBill({
        type: 'submit',
        batchIds: batchIds
      });
      loading.value = false;
      EleMessage.success('提交成功');
      reload();
    } finally {
      loading.value = false;
    }
  };

  const synBatch = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查账单状态是否为待同步或者失败状态
    const allowedStatuses = [1, 5];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.synStatus)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有待同步或者失败状态的数据项才能提交');
      return;
    }
    ElMessageBox.confirm(
      `是否确认提交账单编号为"${rows.map((d) => d.refundCode).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        RefundBillHeaderApi.synContractBill({ ids: rows.map((d) => d.id) })
          .then(() => {
            loading.close();
            EleMessage.success('提交成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>
