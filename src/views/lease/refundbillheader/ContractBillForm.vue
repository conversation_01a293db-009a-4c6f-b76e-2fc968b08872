<template>
  <ele-modal
    title="选择账单"
    :body-style="{ padding: '4px 16px 8px 16px' }"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <contract-bill-search @search="reload" />
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      :pagination="false"
      :toolbar="false"
      :empty-props="false"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import * as ContractBillApi from '@/api/lease/contractbill';
  import { dateFormatter, dateFormatter2 } from '@/utils/formatTime';
  import ContractBillSearch from './contract-bill-search.vue';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 用户 */
    data: Object
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'contractCode',
      label: '合同编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'billCode',
      label: '账单编号',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'paymentType',
      label: '支付类型',
      align: 'center',
      minWidth: 110,
      slot: 'paymentType'
    },
    {
      prop: 'startDate',
      label: '开始时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'endDate',
      label: '结束时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'paymentDate',
      label: '收款日期',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter2
    },
    {
      prop: 'fixedRent',
      label: '固定租金',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'floatingRent',
      label: '浮动租金',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'receivedTotalAmt',
      label: '已收总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'refundTotalAmt',
      label: '退款总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 提交状态 */
  const loading = ref(false);

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ContractBillApi.getContractBillCanRefundPage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 保存编辑 */
  const save = async () => {
    loading.value = true;
    // 对 selections 中的数据进行转换
    const contractBills = selections.value.map((item) => ({
      sourceId: item.id,
      billNo: item.billCode,
      startDate: item.startDate,
      endDate: item.endDate,
      billAmt: item.canRefundTotalAmt,
      refundableTotalAmt: item.canRefundTotalAmt
    }));
    try {
      // 发送操作成功的事件
      emit('done', contractBills);
      loading.value = false;
      updateModelValue(false);
    } finally {
      loading.value = false;
    }
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue && props.data) {
        reload();
      } else {
        selections.value = [];
      }
    }
  );
</script>
