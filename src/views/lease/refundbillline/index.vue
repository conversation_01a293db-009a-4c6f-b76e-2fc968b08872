<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="退款头id" prop="headerId">
              <el-input
                v-model.trim="queryParams.headerId"
                placeholder="请输入退款头id"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="来源id" prop="sourceId">
              <el-input
                v-model.trim="queryParams.sourceId"
                placeholder="请输入来源id"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item
              label="退款类型，1-合同账单，2-押金，3-其他"
              prop="contractType"
            >
              <el-select
                v-model="queryParams.contractType"
                placeholder="请选择退款类型，1-合同账单，2-押金，3-其他"
                clearable
              >
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="账单金额" prop="billAmt">
              <el-input
                v-model.trim="queryParams.billAmt"
                placeholder="请输入账单金额"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="退款总金额" prop="refundTotalAmt">
              <el-input
                v-model.trim="queryParams.refundTotalAmt"
                placeholder="请输入退款总金额"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="应退总金额" prop="refundableTotalAmt">
              <el-input
                v-model.trim="queryParams.refundableTotalAmt"
                placeholder="请输入应退总金额"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item
              label="退款状态, 1-新建; 2-退款中; 3-完成; 4-作废;"
              prop="status"
            >
              <el-select
                v-model="queryParams.status"
                placeholder="请选择退款状态, 1-新建; 2-退款中; 3-完成; 4-作废;"
                clearable
              >
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item
              label="账单状态, 1-需处理，2-无需处理;"
              prop="billStatus"
            >
              <el-select
                v-model="queryParams.billStatus"
                placeholder="请选择账单状态, 1-需处理，2-无需处理;"
                clearable
              >
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model.trim="queryParams.remark"
                placeholder="请输入备注"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="备用字段1" prop="atributeVarchar1">
              <el-input
                v-model.trim="queryParams.atributeVarchar1"
                placeholder="请输入备用字段1"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="备用字段2" prop="atributeVarchar2">
              <el-input
                v-model.trim="queryParams.atributeVarchar2"
                placeholder="请输入备用字段2"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="备用字段3" prop="atributeVarchar3">
              <el-input
                v-model.trim="queryParams.atributeVarchar3"
                placeholder="请输入备用字段3"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="备用字段4" prop="atributeVarchar4">
              <el-input
                v-model.trim="queryParams.atributeVarchar4"
                placeholder="请输入备用字段4"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="备用字段5" prop="atributeVarchar5">
              <el-input
                v-model.trim="queryParams.atributeVarchar5"
                placeholder="请输入备用字段5"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="外部系统客户id" prop="externalCustomerId">
              <el-input
                v-model.trim="queryParams.externalCustomerId"
                placeholder="请输入外部系统客户id"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="同步外部系统时间" prop="synTime">
              <el-date-picker
                v-model="queryParams.synTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item
              label="同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败"
              prop="synStatus"
            >
              <el-select
                v-model="queryParams.synStatus"
                placeholder="请选择同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败"
                clearable
              >
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="同步外部信息" prop="synMessage">
              <el-input
                v-model.trim="queryParams.synMessage"
                placeholder="请输入同步外部信息"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            v-permission="['lease:refund-bill-line:create']"
            :icon="Plus"
            @click="openEdit(null)"
          >
            新增
          </el-button>
          <el-button
            class="ele-btn-icon"
            v-permission="['lease:refund-bill-line:export']"
            :icon="DownloadOutlined"
            @click="exportData"
            :loading="exportLoading"
          >
            导出
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openEdit(row)"
            v-permission="['lease:refund-bill-line:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['lease:refund-bill-line:delete']"
          />
          <el-link
            :underline="false"
            type="primary"
            @click="removeBatch(row)"
            v-permission="['lease:refund-bill-line:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <RefundBillLineForm v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup lang="ts">
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DownloadOutlined } from '@/components/icons';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import download from '@/utils/download';
  import * as RefundBillLineApi from '@/api/lease/refundbillline';
  import RefundBillLineForm from './RefundBillLineForm.vue';
  import { useFormData } from '@/utils/use-form-data';

  /** 退款账单行信息 列表 */
  defineOptions({ name: 'RefundBillLineIndex' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    headerId: undefined,
    sourceId: undefined,
    contractType: undefined,
    billAmt: undefined,
    refundTotalAmt: undefined,
    refundableTotalAmt: undefined,
    status: undefined,
    billStatus: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    createTime: [],
    externalCustomerId: undefined,
    synTime: [],
    synStatus: undefined,
    synMessage: undefined
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'id',
      label: 'id',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'headerId',
      label: '退款头id',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'sourceId',
      label: '来源id',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'contractType',
      label: '退款类型，1-合同账单，2-押金，3-其他',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'billAmt',
      label: '账单金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'refundTotalAmt',
      label: '退款总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'refundableTotalAmt',
      label: '应退总金额',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '退款状态, 1-新建; 2-退款中; 3-完成; 4-作废;',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'billStatus',
      label: '账单状态, 1-需处理，2-无需处理;',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'remark',
      label: '备注',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'atributeVarchar1',
      label: '备用字段1',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'atributeVarchar2',
      label: '备用字段2',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'atributeVarchar3',
      label: '备用字段3',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'atributeVarchar4',
      label: '备用字段4',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'atributeVarchar5',
      label: '备用字段5',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'externalCustomerId',
      label: '外部系统客户id',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'synTime',
      label: '同步外部系统时间',
      align: 'center',
      minWidth: 130,
      formatter: dateFormatter
    },
    {
      prop: 'synStatus',
      label: '同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'synMessage',
      label: '同步外部信息',
      align: 'center',
      minWidth: 110
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);
  /** 当前编辑数据 */
  const current = ref(null);
  const exportLoading = ref(false); // 导出的加载中
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return RefundBillLineApi.getRefundBillLinePage({
      ...where,
      ...filters,
      ...pages
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const removeBatch = async (row) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await RefundBillLineApi.deleteRefundBillLine(row.id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      const data = await RefundBillLineApi.exportRefundBillLine(queryParams);
      download.excel(data, '退款账单行信息.xls');
    } catch {
    } finally {
      exportLoading.value = false;
    }
  };
</script>
