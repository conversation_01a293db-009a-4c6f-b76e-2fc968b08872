<template>
  <ele-modal
      form
      :width="680"
      v-model="visible"
      :close-on-click-modal="false"
      destroy-on-close
      :title="title"
      @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      v-loading="loading"
    >
      <el-form-item label="退款头id" prop="headerId">
        <el-input v-model="form.headerId" placeholder="请输入退款头id" />
      </el-form-item>
      <el-form-item label="来源id" prop="sourceId">
        <el-input v-model="form.sourceId" placeholder="请输入来源id" />
      </el-form-item>
      <el-form-item label="退款类型，1-合同账单，2-押金，3-其他" prop="contractType">
        <el-select v-model="form.contractType" placeholder="请选择退款类型，1-合同账单，2-押金，3-其他">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="账单金额" prop="billAmt">
        <el-input v-model="form.billAmt" placeholder="请输入账单金额" />
      </el-form-item>
      <el-form-item label="退款总金额" prop="refundTotalAmt">
        <el-input v-model="form.refundTotalAmt" placeholder="请输入退款总金额" />
      </el-form-item>
      <el-form-item label="应退总金额" prop="refundableTotalAmt">
        <el-input v-model="form.refundableTotalAmt" placeholder="请输入应退总金额" />
      </el-form-item>
      <el-form-item label="退款状态, 1-新建; 2-退款中; 3-完成; 4-作废;" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="账单状态, 1-需处理，2-无需处理;" prop="billStatus">
        <el-radio-group v-model="form.billStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="备用字段1" prop="atributeVarchar1">
        <el-input v-model="form.atributeVarchar1" placeholder="请输入备用字段1" />
      </el-form-item>
      <el-form-item label="备用字段2" prop="atributeVarchar2">
        <el-input v-model="form.atributeVarchar2" placeholder="请输入备用字段2" />
      </el-form-item>
      <el-form-item label="备用字段3" prop="atributeVarchar3">
        <el-input v-model="form.atributeVarchar3" placeholder="请输入备用字段3" />
      </el-form-item>
      <el-form-item label="备用字段4" prop="atributeVarchar4">
        <el-input v-model="form.atributeVarchar4" placeholder="请输入备用字段4" />
      </el-form-item>
      <el-form-item label="备用字段5" prop="atributeVarchar5">
        <el-input v-model="form.atributeVarchar5" placeholder="请输入备用字段5" />
      </el-form-item>
      <el-form-item label="外部系统客户id" prop="externalCustomerId">
        <el-input v-model="form.externalCustomerId" placeholder="请输入外部系统客户id" />
      </el-form-item>
      <el-form-item label="同步外部系统时间" prop="synTime">
        <el-date-picker
          v-model="form.synTime"
          type="date"
          value-format="x"
          placeholder="选择同步外部系统时间"
        />
      </el-form-item>
      <el-form-item label="同步外部系统-状态 1-待同步，2-同步中，3-已同步，4-成功，5-失败" prop="synStatus">
        <el-radio-group v-model="form.synStatus">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="同步外部信息" prop="synMessage">
        <el-input v-model="form.synMessage" placeholder="请输入同步外部信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="cancle">取 消</el-button>
      <el-button @click="save" type="primary" :loading="loading">保存</el-button>
    </template>
  </ele-modal>
</template>
<script setup lang="ts">
import * as RefundBillLineApi from '@/api/lease/refundbillline'
import TinymceEditor from '@/components/TinymceEditor/index.vue';
import { useFormData } from '@/utils/use-form-data';

/** 退款账单行信息 表单 */
defineOptions({ name: 'RefundBillLineForm' })

const props = defineProps({
  /** 修改回显的数据 */
  data: Object
});

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const emit = defineEmits(['done']);

const title = ref('') // 弹窗的标题
/** 弹窗是否打开 */
const visible = defineModel({ type: Boolean });

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
                    id: undefined,
                    headerId: undefined,
                    sourceId: undefined,
                    contractType: undefined,
                    billAmt: undefined,
                    refundTotalAmt: undefined,
                    refundableTotalAmt: undefined,
                    status: undefined,
                    billStatus: undefined,
                    remark: undefined,
                    atributeVarchar1: undefined,
                    atributeVarchar2: undefined,
                    atributeVarchar3: undefined,
                    atributeVarchar4: undefined,
                    atributeVarchar5: undefined,
                    externalCustomerId: undefined,
                    synTime: undefined,
                    synStatus: undefined,
                    synMessage: undefined,
});
/** 表单验证规则 */
const rules  = reactive({
});
/** 关闭弹窗 */
const cancle = () => {
  visible.value = false;
};

const save = async () => {
  if (!formRef) return
  const valid = await formRef.value.validate();
  if (!valid) return;
  // 提交请求
  loading.value = true
  try {
    if (!isUpdate.value) {
      await RefundBillLineApi.createRefundBillLine(form)
      message.success(t('common.createSuccess'))
    } else {
      await RefundBillLineApi.updateRefundBillLine(form)
      message.success(t('common.updateSuccess'))
    }
    visible.value = false
    // 发送操作成功的事件
    emit('done')
  } finally {
    loading.value = false
  }
}

/** 弹窗打开事件 */
const handleOpen = async () => {
  loading.value = true
  try{
    if (props.data) {
      const result = await RefundBillLineApi.getRefundBillLine(props.data.id)
      assignFields({...result});
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    title.value = isUpdate.value?t('action.update'): t('action.create')
  }finally {
    loading.value = false
  }
}
</script>