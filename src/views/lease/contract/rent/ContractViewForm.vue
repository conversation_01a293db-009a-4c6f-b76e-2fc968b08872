<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 合同详细 </span>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    disabled
    label-width="140px"
    @submit.prevent=""
  >
    <ele-card style="min-height: 350px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
        <el-tab-pane label="合同明细" name="0">
          <ele-card v-loadding="loading" header="基础信息">
            <el-row :gutter="16">
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="合同编号" prop="code">
                  <el-input
                    maxlength="40"
                    disabled
                    clearable
                    v-model="form.code"
                    placeholder="自动生成"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="合同名称" prop="name">
                  <el-input
                    maxlength="40"
                    clearable
                    v-model="form.name"
                    placeholder="请输入合同名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="是否续租" prop="isRenewal">
                  <span v-if="form.isRenewal == 0">否</span>
                  <span v-if="form.isRenewal == 1">是</span>
                  <el-link
                    style="margin-left: 10px"
                    type="primary"
                    :underline="false"
                    @click="showOldContract"
                  >
                    查看原合同
                  </el-link>
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="合同签订日期" prop="signingDate">
                  <el-date-picker
                    type="date"
                    v-model="form.signingDate"
                    value-format="x"
                    placeholder="请选择合同签订日期"
                    class="ele-fluid"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="签到渠道" prop="signingChannel">
                  <el-radio-group v-model="form.signingChannel">
                    <el-radio :value="1" label="线上" />
                    <el-radio :value="2" label="线下" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="经办人" prop="handlerName">
                  <el-input
                    maxlength="40"
                    clearable
                    disabled
                    v-model="form.handlerName"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="客户类型" prop="customerType">
                  <el-radio-group v-model="form.customerType">
                    <el-radio :value="1" label="企业" />
                    <el-radio :value="2" label="个人" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="客户名称" prop="customerName">
                  <el-input
                    clearable
                    v-model="form.customerName"
                    placeholder="请输入客户名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="所属行业" prop="industry">
                  <el-select
                    disabled
                    clearable
                    v-model="form.industry"
                    placeholder="请选择所属行业"
                    class="ele-fluid"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item
                  :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
                  prop="idNumber"
                >
                  <el-input
                    clearable
                    v-model="form.idNumber"
                    :placeholder="`${form.customerType == 1 ? '请输入纳税人识别号' : '请输入身份证号'}`"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="联系人姓名" prop="contactName">
                  <el-input
                    clearable
                    v-model="form.contactName"
                    placeholder="请输入联系人姓名"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="联系电话" prop="contactNumber">
                  <el-input
                    clearable
                    maxlength="11"
                    v-model="form.contactNumber"
                    placeholder="请输入联系电话"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="公司邮箱" prop="contactEmail">
                  <el-input
                    clearable
                    v-model="form.contactEmail"
                    placeholder="请输入公司邮箱"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="联系地址" prop="contactAddress">
                  <el-input
                    clearable
                    v-model="form.contactAddress"
                    placeholder="请输入联系地址"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </ele-card>
          <ele-card header="租赁费用">
            <el-row :gutter="16">
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="租赁期限" prop="dateTime">
                  <el-date-picker
                    v-model="form.dateTime"
                    type="daterange"
                    value-format="x"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="租金方案" prop="rentalPlan">
                  <el-radio-group v-model="form.rentalPlan">
                    <el-radio :value="1" label="固定租金（按月）" />
                    <el-radio :value="2" label="按天计算" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="月计费方式" prop="monthBillingWay">
                  <el-radio-group v-model="form.monthBillingWay">
                    <el-radio :value="2" label="整月" />
                    <el-radio :value="1" label="自然月" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="年计算方式" prop="daysOfYear">
                  <el-radio-group v-model="form.daysOfYear">
                    <el-radio :value="1" label="365天" />
                    <el-radio :value="2" label="366天" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="首期收款日期" prop="paymentFirstDate">
                  <el-date-picker
                    type="date"
                    v-model="form.paymentFirstDate"
                    value-format="x"
                    placeholder="请选择合同签订日期"
                    class="ele-fluid"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="收款截止日期" prop="paymentEndDateDays">
                  <div class="formdata-flex">
                    <el-select
                      clearable
                      v-model="form.paymentEndDateType"
                      placeholder="请选择"
                      class="ele-fluid"
                    >
                      <el-option :value="1" label="每期开始日前" />
                      <el-option :value="2" label="每期开始日后" />
                      <el-option :value="3" label="每期结束日前" />
                      <el-option :value="4" label="每期结束日后" />
                    </el-select>
                    <div>
                      <el-input
                        clearable
                        v-model="form.paymentEndDateDays"
                        placeholder="请输入天数"
                      >
                        <template #append>天</template>
                      </el-input>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="租金单价" prop="unitPrice">
                  <div class="formdata-flex">
                    <el-input
                      clearable
                      type="number"
                      v-model="form.unitPrice"
                      placeholder="请输入单价"
                      class="input-wh"
                    />
                    <el-select
                      clearable
                      v-model="form.rentalUnit"
                      placeholder="请选择"
                      class="ele-fluid"
                    >
                      <el-option :value="1" label="元/平米/天" />
                      <el-option :value="2" label="元/平米/月" />
                      <el-option :value="3" label="元/平米/年" />
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="付款周期" prop="paymentCycle">
                  <div class="formdata-flex">
                    <el-input
                      clearable
                      type="number"
                      v-model="form.paymentCycle"
                      placeholder="请输入周期"
                      class="input-wh"
                    >
                      <template #append>个月</template>
                    </el-input>
                    <el-switch
                      v-model="form.isPaymentFull"
                      :active-value="1"
                      :inactive-value="0"
                    >
                    </el-switch>
                    &nbsp;一次性付清
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="租赁保证金(元)" prop="ensureFee">
                  <el-input
                    clearable
                    v-model="form.ensureFee"
                    placeholder="请输入租赁保证金(元)"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="违约金(元)" prop="renegeFee">
                  <el-input
                    clearable
                    v-model="form.renegeFee"
                    placeholder="请输入违约金(元)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="滞纳金比例(%)" prop="penaltyAmtRatio">
                  <el-input
                    clearable
                    v-model="form.penaltyAmtRatio"
                    placeholder="请输入滞纳金比例(%)"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="滞纳金基数类型" prop="penaltyAmtType">
                  <el-radio-group v-model="form.penaltyAmtType">
                    <el-radio :value="1" label="租金" />
                    <el-radio :value="2" label="保证金" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :lg="10" :md="12" :sm="12" :xs="24">
                <el-form-item label="租赁面积" prop="rentalArea">
                  <el-input
                    clearable
                    v-model="form.rentalArea"
                    placeholder="请输入租赁面积"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </ele-card>
        </el-tab-pane>
        <el-tab-pane label="租金账单信息" name="1">
          <div style="overflow: auto">
            <div class="stat-container">
              <div class="stat-item">
                <div class="stat-label">应收总金额</div>
                <div class="stat-value">{{ form.planTotalAmt }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">实收总金额</div>
                <div class="stat-value">{{ form.planReceivedTotalAmt }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">实收/应收(%)</div>
                <div class="stat-value">{{
                  (form.planReceivedTotalAmt / form.planTotalAmt) * 100
                }}</div>
              </div>
            </div>
            <el-table
              :data="form.paymentPlanList"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column
                label="账单编号"
                prop="code"
                width="190"
                align="center"
              />
              <el-table-column
                label="租赁期间"
                :formatter="dateFormatter2"
                width="190"
                align="center"
              >
                <template #default="{ row }">
                  {{ formatDate2(row.startDate) }} ~
                  {{ formatDate2(row.endDate) }}
                </template>
              </el-table-column>
              <el-table-column
                label="收款截止日期"
                align="center"
                prop="paymentDate"
                :formatter="dateFormatter2"
                width="180"
              />
              <el-table-column
                label="应收金额"
                align="center"
                prop="totalAmt"
                width="160"
              />
              <el-table-column
                label="实收金额"
                prop="receivedTotalAmt"
                align="center"
                width="160"
              />
              <el-table-column
                label="实收日期"
                align="center"
                prop="actualPaymentDate"
                :formatter="dateFormatter2"
                width="180"
              />
              <el-table-column label="收款状态" width="120" align="center">
                <template #default="{ row }">
                  <dict-data
                    type="tag"
                    :code="DICT_TYPE.CO_CONTRACT_PAY_STATUS"
                    :model-value="row.status"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="其它账单信息" name="2">
          <div style="overflow: auto">
            <div class="stat-container">
              <div class="stat-item">
                <div class="stat-label">应收总金额</div>
                <div class="stat-value">{{ form.otherTotalAmt }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">实收总金额</div>
                <div class="stat-value">{{ form.otherReceivedTotalAmt }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">实收/应收(%)</div>
                <div class="stat-value">{{
                  (form.otherReceivedTotalAmt / form.otherTotalAmt) * 100
                }}</div>
              </div>
            </div>
            <el-table
              :data="form.paymentOtherList"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column
                label="账单编号"
                prop="code"
                width="190"
                align="center"
              />
              <el-table-column
                label="费用类型"
                prop="costType"
                width="190"
                align="center"
              />
              <el-table-column
                label="收款截止日期"
                align="center"
                prop="paymentDate"
                :formatter="dateFormatter2"
                width="180"
              />
              <el-table-column
                label="应收金额"
                align="center"
                prop="totalAmt"
                width="160"
              />
              <el-table-column
                label="实收金额"
                prop="receivedTotalAmt"
                align="center"
                width="160"
              />
              <el-table-column
                label="实收日期"
                align="center"
                prop="actualPaymentDate"
                :formatter="dateFormatter2"
                width="180"
              />
              <el-table-column label="收款状态" width="120" align="center">
                <template #default="{ row }">
                  <dict-data
                    type="tag"
                    :code="DICT_TYPE.CO_CONTRACT_PAY_STATUS"
                    :model-value="row.status"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="房产信息" name="3">
          <div style="overflow: auto">
            <div class="file-flex-box">
              <div class="flex-box">
                <h3>总面积：</h3>
                <span>{{ form.houseInfo.totalArea }}(m²)</span>
              </div>
            </div>
            <el-table
              :data="form.houseInfo.houseItemList"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="园区" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.parkInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼栋" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.buildingInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼层" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.floorInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间号" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间编码" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNum"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="面积(㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.area"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="租金递增" name="4">
          <div style="overflow: auto">
            <div class="file-flex-box">
              <el-radio-group v-model="form.rentIncrease.increaseType">
                <el-radio :value="1" label="下期账单递增" />
                <el-radio :value="2" label="当日递增" />
              </el-radio-group>
            </div>
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.rentIncrease.rentIncreaseItems"
            >
              <el-table-column
                label="递增开始时间"
                prop="date"
                :formatter="dateFormatter2"
                align="center"
              />

              <el-table-column label="递增率(%)" prop="ratio" align="center" />
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="免租期" name="5">
          <div style="overflow: auto">
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.rentalHoliday"
            >
              <el-table-column
                label="免租开始日期"
                prop="startDate"
                :formatter="dateFormatter2"
                align="center"
              />
              <el-table-column
                label="免租结束日期"
                prop="endDate"
                :formatter="dateFormatter2"
                align="center"
              />
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件信息" name="6">
          <div style="overflow: auto">
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.files"
            >
              <el-table-column label="文件名" prop="name"></el-table-column>
              <el-table-column label="大小" prop="size">
                <template #default="scope">
                  <span>{{ scope.row.size }} KB</span>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="createTime">
                <template #default="scope">
                  <span>{{ formatDate2(scope.row.size) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-link
                    type="primary"
                    :underline="false"
                    download
                    :href="scope.row.url"
                  >
                    下载
                  </el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="退租信息" name="7">
          <ele-card v-loadding="loading" header="退租信息">
            <el-row :gutter="16">
              <el-col :lg="8" :md="12" :sm="12" :xs="24">
                <el-form-item label="退租日期" prop="throwLeaseDate">
                  <el-date-picker
                    type="date"
                    v-model="form.throwLease.throwLeaseDate"
                    disabled
                    value-format="x"
                    class="ele-fluid"
                  />
                </el-form-item>
              </el-col>
              <el-col :lg="12" :md="12" :sm="12" :xs="24">
                <el-form-item label="退租原因" prop="reason">
                  <el-radio-group v-model="form.throwLease.reason" disabled>
                    <el-radio
                      v-for="dict in getIntDictOptions(
                        DICT_TYPE.CO_CONTRACT_THROWLEASE_REASON
                      )"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :lg="24" :md="12" :sm="12" :xs="24">
                <el-form-item label="退租说明" prop="explanation">
                  <el-input
                    clearable
                    disabled
                    v-model="form.throwLease.explanation"
                    maxlength="500"
                    show-word-limit
                    type="textarea"
                    :rows="5"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </ele-card>
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </el-form>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import type { FormInstance, FormRules } from 'element-plus';
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate2, dateFormatter2 } from '@/utils/formatTime';

  import { getContractDetial } from '@/api/lease/contract';
  import { getSimpleUserSelectList } from '@/api/system/user';

  const activeName = ref('0');
  const props = defineProps({
    data: Object
  });
  // 路由
  const route = useRoute();
  const router = useRouter();
  /** 加载状态 */
  const loading = ref(false);
  const emit = defineEmits(['done']);
  const isUpdate = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    type: 2,
    code: undefined,
    name: undefined,
    signingDate: undefined,
    signingChannel: 1,
    handler: undefined,
    handlerName: undefined,
    isRenewal: 0,
    industry: undefined,
    totalAmt: undefined,
    receivedTotalAmt: undefined,
    customerRefId: undefined,
    customerType: 1,
    customerName: undefined,
    contactName: undefined,
    contactNumber: undefined,
    idNumber: undefined,
    contactEmail: undefined,
    contactAddress: undefined,
    idNumber: undefined,
    files: [], //文件列表
    dateTime: [], //租赁期限
    daysOfYear: 1,
    monthBillingWay: 2,
    rentalPlan: 1,
    paymentFirstDate: undefined,
    paymentEndDateType: 1,
    paymentEndDateDays: undefined,
    unitPrice: undefined,
    rentalUnit: 1,
    paymentCycle: undefined,
    isPaymentFull: 0,
    ensureFee: undefined,
    renegeFee: undefined,
    penaltyAmtRatio: undefined,
    penaltyAmtType: 1,
    rentalArea: undefined,
    rentIncrease: { increaseType: 1, rentIncreaseItems: [] },
    paymentOtherList: [],
    paymentPlanList: [],
    rentalHoliday: [],
    planTotalAmt: undefined,
    planReceivedTotalAmt: undefined,
    otherTotalAmt: undefined,
    otherReceivedTotalAmt: undefined,
    houseInfo: { totalArea: 0, houseItemList: [] },
    throwLease: {
      throwLeaseDate: undefined,
      reason: -1,
      explanation: undefined
    }
  });

  /** 用户表格配置 */
  /** 表格下拉选中值 */
  const selectedValue2 = ref();
  const tableProps2 = reactive<any>({
    datasource: ({ pages, where, orders }) => {
      return getSimpleUserSelectList({ ...where, ...orders, ...pages });
    },
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'username',
        label: '用户名',
        slot: 'nickname',
        align: 'center',
        width: 130
      },
      {
        prop: 'nickname',
        label: '昵称',
        slot: 'nickname',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });
  //查看原合同
  const showOldContract = () => {};
  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };
  const initFormInfo = async () => {
    resetFields();
    await loadData();
  };
  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      isUpdate.value = true;
      loading.value = true;
      try {
        // 获取最新数据
        const response = await getContractDetial({ id: props.data.id });
        if (response) {
          //判断如果response中的houseInfo为空，则需要赋值初始化数据
          if (!response.houseInfo) {
            response.houseInfo = { totalArea: 0, houseItemList: [] };
          }
          assignFields(response);

          //定义一个数组
          form.dateTime = [];
          form.dateTime.push(response.startDate);
          form.dateTime.push(response.endDate);
          selectedValue2.value = response.handler;
        }
      } catch (error) {
        console.error('获取合同详情失败', error);
      } finally {
        loading.value = false;
      }
    } else {
      isUpdate.value = false;
    }
  };
  defineExpose({ initFormInfo });
</script>

<script lang="ts">
  export default {
    name: 'FormAdvanced'
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
      margin-right: 40px;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
  .stat-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* 统一控制元素间距，可根据需求调整 */
    padding: 10px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    width: calc(33.33% - 10px); /* 适配三列布局，减去 gap 间距 */
    background: #fff;
    border: 1px solid #e9e9e9;
    border-radius: 12px;
    overflow: hidden;
  }

  .stat-label {
    padding: 10px;
    font-size: 17px;
    font-weight: 600;
    color: #333;
    background: #f9f9f9;
  }

  .stat-value {
    padding: 10px;
    font-size: 17px;
    color: #666;
  }
</style>
