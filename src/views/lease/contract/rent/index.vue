<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }" v-if="showStep == 1">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同编号" prop="code">
              <el-input
                v-model.trim="queryParams.code"
                placeholder="请输入合同编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同名称" prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入合同名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="合同状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.CO_CONTRACT_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="4" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card
      flex-table
      :body-style="{ paddingTop: '8px' }"
      v-if="showStep == 1"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="editContract(null)"
          >
            添加合同
          </el-button>
          <el-button
            type="primary"
            class="ele-btn-icon"
            @click="submitContract(null)"
          >
            提交
          </el-button>
        </template>
        <template #code="{ row }">
          <el-link type="primary" :underline="false" @click="showContract(row)">
            {{ row.code }}
          </el-link>
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <!-- status: 0:新建, 2:未开始, 3:执行中, 4:已到期,5:已退租, 6:已作废 -->
          <span v-if="row.status === 0">
            <el-link
              type="primary"
              :underline="false"
              @click="editContract(row)"
            >
              编辑
            </el-link>
            <el-divider direction="vertical" />
            <el-link
              type="primary"
              :underline="false"
              @click="submitContract(row)"
            >
              提交
            </el-link>
          </span>
          <span v-else-if="row.status === 2">
            <el-link
              type="primary"
              :underline="false"
              @click="cancelContract(row)"
            >
              作废
            </el-link>
          </span>
          <span v-else-if="row.status === 3">
            <el-link
              type="primary"
              :underline="false"
              @click="terminalContract(row)"
            >
              退租
            </el-link>
            <el-divider direction="vertical" />
            <ele-dropdown
              v-if="moreItems.length"
              :items="moreItems"
              style="display: inline"
              @command="(key) => dropClick(key, row)"
            >
              <el-link type="primary" :underline="false">
                <span>更多</span>
                <el-icon
                  :size="12"
                  style="vertical-align: -1px; margin-left: 2px"
                >
                  <ArrowDown />
                </el-icon>
              </el-link>
            </ele-dropdown>
          </span>
        </template>
      </ele-pro-table>
    </ele-card>
    <ContractEditForm
      ref="contractRef"
      v-if="showStep == 2"
      :data="current"
      @done="detailDone"
    />
    <ContractViewForm
      ref="contractDetailRef"
      v-if="showStep == 3"
      :data="current"
      @done="detailDone"
    />
    <ContractChangeForm
      ref="contractChangeRef"
      v-if="showStep == 4"
      :data="current"
      @done="detailDone"
    />
    <ContractRenewalForm
      ref="contractRenewalRef"
      v-if="showStep == 5"
      :data="current"
      @done="detailDone"
    />
    <ContractTerminalForm
      ref="contractTerminalRef"
      v-if="showStep == 6"
      :data="current"
      @done="detailDone"
    />
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { dateFormatter2 } from '@/utils/formatTime';
  import { useFormData } from '@/utils/use-form-data';
  import {
    getContractPage,
    getContractDel,
    getSynInterface,
    getToDeposit,
    getCancelContract,
    getSubmitContract
  } from '@/api/lease/contract';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import ContractEditForm from './ContractEditForm.vue';
  import ContractViewForm from './ContractViewForm.vue';
  import ContractChangeForm from './ContractChangeForm.vue';
  import ContractRenewalForm from './ContractRenewalForm.vue';
  import ContractTerminalForm from './ContractTerminalForm.vue';
  import { ElLoading } from 'element-plus';

  defineOptions({ name: 'CoContractIndex' });

  const showStep = ref(1);
  const contractRef = ref(null);
  const contractDetailRef = ref(null);
  const contractChangeRef = ref(null);
  const contractRenewalRef = ref(null);
  const contractTerminalRef = ref(null);
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    code: undefined,
    name: undefined,
    status: undefined
  });
  // 路由
  const router = useRouter();
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        width: 48,
        align: 'center',
        fixed: 'left',
        selectable: (row) => {
          return row.status === 0;
        }
      },
      {
        prop: 'code',
        label: '合同编号',
        align: 'center',
        minWidth: 160,
        slot: 'code'
      },
      {
        prop: 'name',
        label: '合同名称',
        align: 'center',
        minWidth: 170
      },
      {
        prop: 'status',
        label: '合同状态',
        width: 90,
        align: 'center',
        slot: 'status'
      },
      {
        prop: 'houseNames',
        label: '楼宇名称',
        align: 'center',
        minWidth: 160
      },
      {
        prop: 'customerName',
        label: '客户名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'signingDate',
        label: '签订时间',
        width: 160,
        align: 'center',
        formatter: dateFormatter2
      },
      {
        prop: 'startDate',
        label: '开始时间',
        width: 160,
        align: 'center',
        formatter: dateFormatter2
      },
      {
        prop: 'endDate',
        label: '结束时间',
        width: 160,
        align: 'center',
        formatter: dateFormatter2
      },
      {
        prop: 'rentalArea',
        label: '租赁面积',
        width: 140,
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 230,
        align: 'center',
        fixed: 'right',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getContractPage({ ...where, type: 1, ...filters, ...pages });
  };
  /** 当前编辑数据 */
  const current = ref(null);
  //添加合同
  const editContract = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    contractRef.value?.initFormInfo();
  };
  //提交合同
  const submitContract = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查账单状态是否为待同步或者失败状态
    const allowedStatuses = [0];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有新建状态的数据项才能提交');
      return;
    }
    ElMessageBox.confirm(
      `是否确认提交合同编号为"${rows.map((d) => d.code).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        // 开启全局加载状态
        const loadingInstance = ElLoading.service({
          lock: true,
          text: '提交合同中...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        const p = rows.map((d) => getSubmitContract(d.id));
        Promise.all(p)
          .then(() => {
            // 关闭加载状态
            loadingInstance.close();
            EleMessage.success('提交成功');
            reload();
          })
          .catch((e) => {
            // 关闭加载状态
            loadingInstance.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  //退租合同
  const terminalContract = async (row) => {
    current.value = row ?? null;
    showStep.value = 6;
    // 初始化流程定义详情
    await nextTick();
    contractTerminalRef.value?.initFormInfo();
  };
  //作废合同
  const cancelContract = async (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    // 检查账单状态是否为待同步或者失败状态
    const allowedStatuses = [0, 2];
    const hasInvalidStatus = rows.some(
      (row) => !allowedStatuses.includes(row.status)
    );

    if (hasInvalidStatus) {
      EleMessage.error('只有未开始的数据项才能作废');
      return;
    }
    ElMessageBox.confirm(
      `是否确认作废合同编号为"${rows.map((d) => d.code).join()}"的数据项?`,
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        // 开启全局加载状态
        const loadingInstance = ElLoading.service({
          lock: true,
          text: '作废合同中...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        getCancelContract(rows[0].id)
          .then(() => {
            // 关闭加载状态
            loadingInstance.close();
            EleMessage.success('作废成功');
            reload();
          })
          .catch((e) => {
            // 关闭加载状态
            loadingInstance.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  //生成合同账单
  const synInterface = async (row) => {
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '生成账单中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await getSynInterface(row.id);
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('生成成功');
      reload();
    } finally {
      // 关闭加载状态
      loadingInstance.close();
    }
  };

  //生成押金
  const toDeposit = async (row) => {
    // 开启全局加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '生成押金中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await getToDeposit(row.id);
      // 关闭加载状态
      loadingInstance.close();
      EleMessage.success('生成成功');
      reload();
    } finally {
      // 关闭加载状态
      loadingInstance.close();
    }
  };
  //续租合同
  const renewalContract = async (row) => {
    current.value = row ?? null;
    showStep.value = 5;
    // 初始化流程定义详情
    await nextTick();
    contractRenewalRef.value?.initFormInfo();
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  const detailDone = () => {
    showStep.value = 1;
    reload();
  };
  const showContract = async (row) => {
    current.value = row ?? null;
    showStep.value = 3;
    // 初始化流程定义详情
    await nextTick();
    contractDetailRef.value?.initFormInfo();
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  const changeContract = async (row) => {
    current.value = row ?? null;
    showStep.value = 4;
  };

  /** 操作列更多下拉菜单 */
  const moreItems = computed(() => {
    const items = [];
    items.push({ title: '生成合同账单', command: 'synInterface' });
    items.push({ title: '生成押金', command: 'toDeposit' });
    return items;
  });

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'synInterface') {
      synInterface(row);
    } else if (key === 'toDeposit') {
      toDeposit(row);
    }
  };
</script>
