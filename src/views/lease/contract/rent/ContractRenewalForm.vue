<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 续租合同 </span>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="140px"
    @submit.prevent=""
  >
    <ele-card header="基础信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同编号" prop="code">
            <el-input
              maxlength="40"
              disabled
              clearable
              v-model="form.code"
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同名称" prop="name">
            <el-input
              maxlength="40"
              clearable
              v-model="form.name"
              placeholder="请输入合同名称"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="是否续租" prop="administrator">
            是
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同签订日期" prop="signingDate">
            <el-date-picker
              type="date"
              v-model="form.signingDate"
              value-format="x"
              placeholder="请选择合同签订日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="签订渠道" prop="signingChannel">
            <el-radio-group v-model="form.signingChannel">
              <el-radio :value="1" label="线上" />
              <el-radio :value="2" label="线下" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经办人" prop="handler">
            <ele-table-select
              filterable
              clearable
              placeholder="请选择"
              value-key="id"
              v-model:model-value="form.handler"
              label-key="nickname"
              v-model="selectedValue2"
              :table-props="tableProps2"
              @select="onHandlerSelect"
            >
            </ele-table-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="customerType">
            <el-radio-group
              v-model="form.customerType"
              @change="onCustomerTypeChange"
            >
              <el-radio :value="1" label="企业" />
              <el-radio :value="2" label="个人" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerRefId">
            <ele-table-select
              ref="customSelectRef"
              filterable
              clearable
              placeholder="请选择"
              value-key="id"
              v-model:model-value="form.customerRefId"
              label-key="customerName"
              v-model="selectedValue1"
              :table-props="tableProps1"
              @select="onCustomSelect"
            >
            </ele-table-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="industry">
            <el-select
              disabled
              clearable
              v-model="form.industry"
              placeholder="请选择所属行业"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item
            :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
            prop="idNumber"
          >
            <el-input
              disabled
              clearable
              v-model="form.idNumber"
              :placeholder="`${form.customerType == 1 ? '请输入纳税人识别号' : '请输入身份证号'}`"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              clearable
              disabled
              v-model="form.contactName"
              placeholder="请输入联系人姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input
              clearable
              disabled
              maxlength="11"
              v-model="form.contactNumber"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="公司邮箱" prop="contactEmail">
            <el-input
              clearable
              disabled
              v-model="form.contactEmail"
              placeholder="请输入公司邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input
              clearable
              disabled
              v-model="form.contactAddress"
              placeholder="请输入联系地址"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="租赁费用">
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁期限" prop="dateTime">
            <el-date-picker
              v-model="form.dateTime"
              type="daterange"
              value-format="x"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租金方案" prop="rentalPlan">
            <el-radio-group v-model="form.rentalPlan">
              <el-radio :value="1" label="固定租金（按月）" />
              <el-radio :value="2" label="按天计算" />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="月计费方式" prop="monthBillingWay">
            <el-radio-group v-model="form.monthBillingWay">
              <el-radio :value="2" label="整月" />
              <el-radio :value="1" label="自然月" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="年计算方式" prop="daysOfYear">
            <el-radio-group v-model="form.daysOfYear">
              <el-radio :value="1" label="365天" />
              <el-radio :value="2" label="366天" />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="首期收款日期" prop="paymentFirstDate">
            <el-date-picker
              type="date"
              v-model="form.paymentFirstDate"
              value-format="x"
              placeholder="请选择合同签订日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="收款截止日期" prop="paymentEndDateDays">
            <div class="formdata-flex">
              <el-select
                clearable
                v-model="form.paymentEndDateType"
                placeholder="请选择"
                class="ele-fluid"
              >
                <el-option :value="1" label="每期开始日前" />
                <el-option :value="2" label="每期开始日后" />
                <el-option :value="3" label="每期结束日前" />
                <el-option :value="4" label="每期结束日后" />
              </el-select>
              <div>
                <el-input
                  clearable
                  v-model="form.paymentEndDateDays"
                  placeholder="请输入天数"
                >
                  <template #append>天</template>
                </el-input>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租金单价" prop="unitPrice">
            <div class="formdata-flex">
              <el-input
                clearable
                type="number"
                v-model="form.unitPrice"
                placeholder="请输入单价"
                class="input-wh"
              />
              <el-select
                clearable
                v-model="form.rentalUnit"
                placeholder="请选择"
                class="ele-fluid"
              >
                <el-option :value="1" label="元/平米/天" />
                <el-option :value="2" label="元/平米/月" />
                <el-option :value="3" label="元/平米/年" />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="付款周期" prop="paymentCycle">
            <div class="formdata-flex">
              <el-input
                clearable
                type="number"
                v-model="form.paymentCycle"
                placeholder="请输入周期"
                class="input-wh"
              >
                <template #append>个月</template>
              </el-input>
              <el-switch
                v-model="form.isPaymentFull"
                :active-value="1"
                :inactive-value="0"
              >
              </el-switch>
              &nbsp;一次性付清
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁保证金(元)" prop="ensureFee">
            <el-input
              clearable
              v-model="form.ensureFee"
              placeholder="请输入租赁保证金(元)"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="违约金(元)" prop="renegeFee">
            <el-input
              clearable
              v-model="form.renegeFee"
              placeholder="请输入违约金(元)"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="滞纳金比例(%)" prop="penaltyAmtRatio">
            <el-input
              clearable
              v-model="form.penaltyAmtRatio"
              placeholder="请输入滞纳金比例(%)"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="滞纳金基数类型" prop="penaltyAmtType">
            <el-radio-group v-model="form.penaltyAmtType">
              <el-radio :value="1" label="租金" />
              <el-radio :value="2" label="保证金" />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="10" :md="12" :sm="12" :xs="24">
          <el-form-item label="租赁面积" prop="rentalArea">
            <el-input
              clearable
              v-model="form.rentalArea"
              placeholder="请输入租赁面积"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card style="min-height: 350px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
        <el-tab-pane label="房产信息" name="0">
          <div style="overflow: auto">
            <div class="file-flex-box">
              <div class="flex-box"
                ><h3>总面积：</h3
                ><!-- 修改：将总面积绑定到 form.houseInfo.totalArea -->
                <span>{{ form.houseInfo.totalArea }}(m²)</span></div
              >
              <el-button type="primary" @click="getHouseDel(1, '', null)"
                >添加</el-button
              >
            </div>
            <el-table
              :data="form.houseInfo.houseItemList"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="园区" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.parkInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼栋" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.buildingInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="楼层" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.floorInfo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间号" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNo"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="房间编码" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.roomNum"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="面积(㎡)" width="150">
                <template #default="{ row }">
                  <el-input disabled v-model="row.area"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getHouseDel(2, $index, null)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="租金递增" name="1">
          <div style="overflow: auto">
            <div class="file-flex-box">
              <el-radio-group v-model="form.rentIncrease.increaseType">
                <el-radio :value="1" label="下期账单递增" />
                <el-radio :value="2" label="当日递增" />
              </el-radio-group>
              <el-button type="primary" @click="getRentIncreaseAddDel(1, '')">
                添加
              </el-button>
            </div>
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.rentIncrease.rentIncreaseItems"
            >
              <el-table-column label="递增开始时间">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.date"
                    value-format="x"
                    placeholder="请选择递增开始时间"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="递增率(%)">
                <template #default="{ row }">
                  <el-input
                    type="number"
                    v-model="row.ratio"
                    class="input-wh"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getRentIncreaseAddDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="免租期" name="2">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getRentoalAddDel(1, '')">
                添加
              </el-button>
            </div>
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.rentalHoliday"
            >
              <el-table-column label="免租开始日期">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.startDate"
                    value-format="x"
                    placeholder="请选择开始日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="免租结束日期">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.endDate"
                    value-format="x"
                    placeholder="请选择结束日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getRentoalAddDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="收款信息" name="3">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getPaymentPlanDel(1, '')">
                添加
              </el-button>
              <el-button type="primary" @click="generatePlanConfirm()">
                生成收款计划
              </el-button>
            </div>
            <el-table
              :data="form.paymentPlanList"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="开始日期" width="250">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.startDate"
                    value-format="x"
                    placeholder="请选择开始日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="结束日期" width="250">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.endDate"
                    value-format="x"
                    placeholder="请选择结束日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="收款日" width="250">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.paymentDate"
                    value-format="x"
                    placeholder="请选择收款日"
                    class="ele-fluid"
                  />
                </template>
              </el-table-column>
              <el-table-column label="租金(元)" width="250">
                <template #default="{ row }">
                  <el-input type="number" v-model="row.paymentAmt"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getPaymentPlanDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件信息" name="4">
          <div style="overflow: auto">
            <div class="file-box">
              <el-upload :show-file-list="false" :http-request="httpRequest">
                <el-button type="primary">上传文件</el-button>
              </el-upload>
            </div>
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.files"
            >
              <el-table-column label="文件名" prop="name"></el-table-column>
              <el-table-column label="大小" prop="size">
                <template #default="scope">
                  <span>{{ scope.row.size }} KB</span>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="uploadTime">
                <template #default="scope">
                  <span>{{ formatDate2(scope.row.uploadTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-link
                    type="primary"
                    :underline="false"
                    download
                    :href="scope.row.url"
                  >
                    下载
                  </el-link>
                  <el-divider direction="vertical" />
                  <el-link
                    type="danger"
                    :underline="false"
                    @click="deleteFile(scope.row)"
                  >
                    删除
                  </el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="其它费用" name="5">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getPaymenOtherAddDel(1, '')">
                添加
              </el-button>
            </div>
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.paymentOtherList"
            >
              <el-table-column label="费用类型(必填)">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`paymentOtherList.${$index}.costType`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入费用类型',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input v-model="row.costType"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="总金额(必填)">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`paymentOtherList.${$index}.paymentAmt`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入总金额',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input v-model="row.paymentAmt"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="缴费日期(必填)">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`paymentOtherList.${$index}.paymentDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请选择缴费日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.paymentDate"
                      value-format="x"
                      placeholder="请选择递增开始时间"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="滞纳金%(非必填)">
                <template #default="{ row }">
                  <el-form-item class="input-margin-left">
                    <el-input
                      type="number"
                      v-model="row.penaltyRatio"
                      class="input-wh"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getPaymenOtherAddDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </el-form>
  <!-- 底部工具栏 -->
  <ele-bottom-bar>
    <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
      <span>{{ validMsg }}</span>
    </ele-text>
    <template #extra>
      <el-button type="primary" :loading="loading" class="ml-2" @click="submit"
        >提交</el-button
      >
    </template>
  </ele-bottom-bar>
  <HouseEditForm v-model="showHouseEdit" :data="current" @done="recordHandle" />
</template>

<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate2 } from '@/utils/formatTime';
  import HouseEditForm from './HouseEditForm.vue';

  import {
    getContractAdd,
    getContractEdit,
    updateFile,
    getContractDetial,
    getGeneratePlan
  } from '@/api/lease/contract';
  import { getSimpleUserSelectList } from '@/api/system/user';
  import { getCustomerPage } from '@/api/lease/attract/index';
  const activeName = ref('0');
  const props = defineProps({
    data: Object
  });
  /** 加载状态 */
  const loading = ref(false);
  const emit = defineEmits(['done']);
  const isUpdate = ref(false);
  /** 是否显示编辑弹窗 */
  const showHouseEdit = ref(false);
  /** 当前编辑数据 */
  const current = ref(null);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    type: 2,
    code: undefined,
    name: undefined,
    signingDate: undefined,
    signingChannel: 1,
    handler: undefined,
    handlerName: undefined,
    isRenewal: 0,
    customerRefId: undefined,
    customerType: 1,
    industry: undefined,
    customerName: undefined,
    contactName: undefined,
    contactNumber: undefined,
    idNumber: undefined,
    contactEmail: undefined,
    contactAddress: undefined,
    files: [], //文件列表
    dateTime: [], //租赁期限
    daysOfYear: 1,
    monthBillingWay: 2,
    rentalPlan: 1,
    paymentFirstDate: undefined,
    paymentEndDateType: 1,
    paymentEndDateDays: undefined,
    unitPrice: undefined,
    rentalUnit: 1,
    paymentCycle: undefined,
    isPaymentFull: 0,
    ensureFee: undefined,
    renegeFee: undefined,
    penaltyAmtRatio: undefined,
    penaltyAmtType: 1,
    rentalArea: undefined,
    rentIncrease: { increaseType: 1, rentIncreaseItems: [] },
    paymentOtherList: [],
    paymentPlanList: [],
    rentalHoliday: [],
    houseInfo: { totalArea: 0, houseItemList: [] }
  });

  /** 用户表格配置 */
  /** 表格下拉选中值 */
  const selectedValue1 = ref();
  const tableProps1 = reactive<any>({
    datasource: ({ pages, where, orders }) => {
      const query = { customerType: form.customerType };
      return getCustomerPage({ ...where, ...orders, ...pages, ...query });
    },
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'customerName',
        label: '客户名',
        align: 'center',
        width: 170
      },
      {
        prop: 'contactName',
        label: '昵称',
        slot: '联系人',
        align: 'center',
        width: 170
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  const selectedValue2 = ref();
  const tableProps2 = reactive<any>({
    datasource: ({ pages, where, orders }) => {
      return getSimpleUserSelectList({ ...where, ...orders, ...pages });
    },
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      ,
      {
        prop: 'username',
        label: '用户名',
        slot: 'nickname',
        align: 'center',
        width: 130
      },
      {
        prop: 'nickname',
        label: '昵称',
        slot: 'nickname',
        align: 'center'
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });
  const customSelectRef = ref(null);
  /** 选中事件 */
  const onHandlerSelect = (selection) => {
    form.handlerName = selection.nickname;
  };
  /** 选中事件 */
  const onCustomSelect = (selection) => {
    form.customerName = selection.customerName;
    form.contactName = selection.contactName;
    form.contactNumber = selection.contactNumber;
    form.contactEmail = selection.contactEmail;
    form.contactAddress = selection.contactAddress;
    form.idNumber = selection.idNumber;
    form.industry = selection.industry;
  };
  const resetCustom = () => {
    selectedValue1.value = undefined;
    form.customerRefId = undefined;
    form.customerName = undefined;
    form.contactName = undefined;
    form.contactNumber = undefined;
    form.contactEmail = undefined;
    form.contactAddress = undefined;
    form.idNumber = undefined;
    form.industry = undefined;
  };
  const onCustomerTypeChange = (value) => {
    resetCustom();
    customSelectRef.value?.tableRef?.reload();
  };
  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    signingChannel: [
      { required: true, message: '请选择签订渠道', trigger: 'blur' }
    ],
    customerType: [
      { required: true, message: '请选择客户类型', trigger: 'blur' }
    ],
    rentalPlan: [
      { required: true, message: '请选择租金方案', trigger: 'blur' }
    ],
    monthBillingWay: [
      { required: true, message: '请选择月计费方式', trigger: 'blur' }
    ],
    daysOfYear: [
      { required: true, message: '请选择年计算方式', trigger: 'blur' }
    ],
    signingDate: [
      { required: true, message: '请选择合同签订日期', trigger: 'blur' }
    ],
    handler: [{ required: true, message: '请选择经办人', trigger: 'blur' }],
    customerRefId: [
      { required: true, message: '请输入客户名称', trigger: 'blur' }
    ],
    dateTime: [
      { required: true, message: '请选择租赁期限日期', trigger: 'blur' }
    ],
    paymentFirstDate: [
      { required: true, message: '请选择首期收款日期', trigger: 'blur' }
    ],
    paymentEndDateDays: [
      { required: true, message: '请输入收款截止日期', trigger: 'blur' }
    ],
    unitPrice: [{ required: true, message: '请输入租金单价', trigger: 'blur' }],
    paymentCycle: [
      { required: true, message: '请输入付款周期', trigger: 'blur' }
    ],
    rentalArea: [{ required: true, message: '请输入租赁面积', trigger: 'blur' }]
  });
  //上传文件
  const httpRequest = async ({ file }) => {
    let formData = new FormData();
    formData.append('file', file);
    let res = await updateFile(formData);
    form.files.push({
      fileId: res.data.id,
      name: res.data.name,
      url: res.data.url,
      type: res.data.type,
      size: res.data.size,
      uploadTime: res.data.createTime,
      path: res.data.path
    });
  };
  const deleteFile = async (row) => {
    form.files.splice(row, 1);
  };
  //免租期添加内容
  const getRentoalAddDel = (type, index) => {
    if (type == 1) {
      form.rentalHoliday.push({ startDate: '', endDate: '' });
    } else {
      form.rentalHoliday.splice(index, 1);
    }
  };
  //租金递增添加
  const getRentIncreaseAddDel = (type, index) => {
    if (type == 1) {
      form.rentIncrease.rentIncreaseItems.push({ date: '', ratio: 0 });
    } else {
      form.rentIncrease.rentIncreaseItems.splice(index, 1);
    }
  };
  //其它费用添加
  const getPaymenOtherAddDel = (type, index) => {
    if (type == 1) {
      form.paymentOtherList.push({
        costType: '',
        paymentAmt: '',
        paymentDate: '',
        penaltyRatio: 0
      });
    } else {
      form.paymentOtherList.splice(index, 1);
    }
  };
  /** 表单验证失败提示信息 */
  const validMsg = ref('');

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      try {
        loading.value = true;
        validMsg.value = '';
        loading.value = true;
        let res = null;
        let base = {
          isRenewal: 1,
          renewalContractId: props.data.id
        };
        let params = {
          ...form,
          ...base,
          startDate: '',
          endDate: ''
        };
        if (params.dateTime && params.dateTime.length > 0) {
          params.startDate = params.dateTime[0];
          params.endDate = params.dateTime[1];
        }
        res = await getContractAdd(params);
        if (res) {
          EleMessage.success('提交成功');
          cancle();
        }
      } finally {
        loading.value = false;
      }
    });
  };
  //新增收款信息
  const getPaymentPlanDel = (type, index) => {
    if (type == 1) {
      form.paymentPlanList.push({
        startDate: '',
        endDate: '',
        paymentDate: '',
        paymentAmt: ''
      });
    } else {
      form.paymentPlanList.splice(index, 1);
    }
  };
  //生成付款计划
  const generatePlanConfirm = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      try {
        loading.value = true;
        form.paymentPlanList = [];
        validMsg.value = '';
        let params = {
          ...form,
          startDate: '',
          endDate: ''
        };
        if (params.dateTime && params.dateTime.length > 0) {
          params.startDate = params.dateTime[0];
          params.endDate = params.dateTime[1];
        }
        let res = await getGeneratePlan(params);
        form.paymentPlanList.push(...res);
      } finally {
        loading.value = false;
      }
    });
  };
  const initFormInfo = async () => {
    resetFields();
    await loadData();
  };
  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      isUpdate.value = true;
      loading.value = true;
      try {
        // 获取最新数据
        const response = await getContractDetial({ id: props.data.id });
        if (response) {
          assignFields(response);
          form.paymentFirstDate = undefined;
          form.paymentPlanList = [];
          selectedValue2.value = response.handler;
          selectedValue1.value = response.customerRefId;
        }
      } catch (error) {
        console.error('获取合同详情失败', error);
      } finally {
        loading.value = false;
      }
    } else {
      isUpdate.value = false;
    }
  };
  defineExpose({ initFormInfo });
  //新增房产信息
  const getHouseDel = async (type, index, row) => {
    if (type == 1) {
      if (row) {
        current.value = { ...row, index: index, isOldFlag: 'Y' };
      } else {
        current.value = null;
      }
      showHouseEdit.value = true;
      // 初始化流程定义详情
      await nextTick();
    } else {
      form.houseInfo.houseItemList.splice(index, 1);
    }
  };

  /** 关闭房产弹窗 */
  const recordHandle = (data) => {
    if (data) {
      //将houseId转换为roomId
      data = { ...data, roomId: data.houseId };
      if (data.isOldFlag == 'N') {
        data.isOldFlag = 'Y';
        form.houseInfo.houseItemList.push(data);
      } else {
        form.stationContractHouses.houseItemList.splice(data.index, 1, data);
      }
    }
  };

  // 监听 houseInfo.houseItemList 的变化，动态计算总面积
  watch(
    () => form.houseInfo.houseItemList,
    (newVal) => {
      form.houseInfo.totalArea = newVal.reduce(
        (sum, item) => sum + (parseFloat(item.area || 0) || 0),
        0
      );
    },
    { deep: true }
  );
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>
