<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!-- <role-search @search="reload" /> -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="addContract(1, '')"
          >
            添加租入合同
          </el-button>
        </template>
        <template #status="{ row }">
          {{ statusList[row.status] }}
        </template>
        <template #action="{ row }">
          <el-button type="primary" link @click="addContract(2, row)">
            修改
          </el-button>
          <el-button type="primary" link @click="getContractDelete(row)">
            删除
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { getContractPage, getContractDel } from '@/api/lease/contract';
  // 路由
  const router = useRouter();
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'houseNames',
        label: '楼宇名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'customerName',
        label: '客户名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'signingDate',
        label: '签订时间',
        width: 140,
        align: 'center',
        formatter: dateFormatter
      },
      {
        prop: 'startDate',
        label: '开始时间',
        width: 140,
        align: 'center',
        formatter: dateFormatter
      },
      {
        prop: 'endDate',
        label: '结束时间',
        width: 140,
        align: 'center',
        formatter: dateFormatter
      },
      {
        prop: 'rentalArea',
        label: '租赁面积',
        width: 140,
        align: 'center'
      },
      {
        prop: 'status',
        label: '合同状态',
        width: 90,
        align: 'center',
        slot: 'status'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  //数据状态显示
  const statusList = ref({
    1: '待审核',
    2: '未开始',
    3: '执行中',
    4: '已到期',
    5: '已退租',
    6: '已作废'
  });
  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getContractPage({ ...where, type: 2, ...filters, ...pages });
  };
  //添加合同
  const addContract = (type, row) => {
    if (type == 1) {
      router.push({
        path: '/system/lease/contract/formConract',
        query: { type: 2 }
      });
    } else {
      router.push({
        path: '/system/lease/contract/formConract',
        query: { type: 2, id: row.id }
      });
    }
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  //删除合同
  const getContractDelete = (row) => {
    ElMessageBox.confirm('确认删除合同数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await getContractDel(row);
        if (res) {
          EleMessage.success('删除成功');
          reload();
        } else {
          EleMessage.success('删除失败');
        }
      })
      .catch(() => console.info('操作取消'));
  };
</script>
