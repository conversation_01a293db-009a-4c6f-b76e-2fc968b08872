<template>
  <ele-page plain hide-footer :multi-card="false">
    <ele-page>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        @submit.prevent=""
      >
        <ele-card header="基础信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="合同编号" prop="code">
                <el-input
                  maxlength="40"
                  clearable
                  v-model="form.code"
                  placeholder="请输入合同编号"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="合同名称" prop="name">
                <el-input
                  maxlength="40"
                  clearable
                  v-model="form.name"
                  placeholder="请输入合同名称"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="是否续租" prop="administrator">
                否
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="合同签订日期" prop="signingDate">
                <el-date-picker
                  type="date"
                  v-model="form.signingDate"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择合同签订日期"
                  class="ele-fluid"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="签到渠道" prop="signingChannel">
                <el-radio-group v-model="form.signingChannel">
                  <el-radio :value="1" label="线上" />
                  <el-radio :value="2" label="线下" />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="经办人" prop="handler">
                <el-select
                  clearable
                  v-model="form.handler"
                  placeholder="请选择经办人"
                  class="ele-fluid"
                >
                  <el-option :value="1" label="私密" />
                  <el-option :value="2" label="公开" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
        <ele-card header="租赁信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户类型" prop="customerType">
                <el-radio-group v-model="form.customerType">
                  <el-radio :value="1" label="企业" />
                  <el-radio :value="2" label="个人" />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户名称" prop="customerName">
                <el-input
                  clearable
                  v-model="form.customerName"
                  placeholder="请输入客户名称"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="所属行业" prop="industry">
                <el-select
                  clearable
                  v-model="form.industry"
                  placeholder="请选择所属行业"
                  class="ele-fluid"
                >
                  <el-option :value="1" label="农、林、牧、渔业" />
                  <el-option :value="2" label="采矿业" />
                  <el-option :value="3" label="制造业" />
                  <el-option :value="4" label="建筑业" />
                  <el-option :value="5" label="批发和零售业" />
                  <el-option :value="6" label="交通运输、仓储和邮政业" />
                  <el-option :value="7" label="住宿、餐饮业" />
                  <el-option :value="8" label="金融业" />
                  <el-option :value="9" label="房地产业" />
                  <el-option :value="10" label="租赁和商业服务" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item
                :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
                prop="idNumber"
              >
                <el-input
                  clearable
                  v-model="form.idNumber"
                  :placeholder="`${form.customerType == 1 ? '请输入纳税人识别号' : '请输入身份证号'}`"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系人姓名" prop="contactName">
                <el-input
                  clearable
                  v-model="form.contactName"
                  placeholder="请输入联系人姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系电话" prop="contactNumber">
                <el-input
                  clearable
                  maxlength="11"
                  v-model="form.contactNumber"
                  placeholder="请输入联系电话"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="公司邮箱" prop="contactEmail">
                <el-input
                  clearable
                  v-model="form.contactEmail"
                  placeholder="请输入公司邮箱"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系地址" prop="contactAddress">
                <el-input
                  clearable
                  v-model="form.contactAddress"
                  placeholder="请输入联系地址"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
        <ele-card header="附件信息">
          <div style="overflow: auto">
            <div class="file-box">
              <el-upload :show-file-list="false" :http-request="httpRequest">
                <el-button type="primary">上传文件</el-button>
              </el-upload>
            </div>
            <el-table border :data="form.files">
              <el-table-column label="文件名"></el-table-column>
              <el-table-column label="大小"></el-table-column>
              <el-table-column label="上传时间"></el-table-column>
              <el-table-column label="操作"></el-table-column>
            </el-table>
          </div>
        </ele-card>
        <ele-card header="房产信息">
          <div style="overflow: auto">
            <div class="file-flex-box">
              <div class="flex-box"><h3>总面积：</h3>0.00(m²)</div>
              <el-button type="primary">添加</el-button>
            </div>
            <el-table border>
              <el-table-column label="楼宇名称"></el-table-column>
              <el-table-column label="楼层"></el-table-column>
              <el-table-column label="房间"></el-table-column>
              <el-table-column label="面积"></el-table-column>
              <el-table-column label="操作"></el-table-column>
            </el-table>
          </div>
        </ele-card>
        <ele-card header="租赁费用">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="租赁期限" prop="dateTime">
                <el-date-picker
                  v-model="form.dateTime"
                  type="daterange"
                  value-format="YYYY-MM-DD"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="租金方案" prop="rentalPlan">
                <el-radio-group v-model="form.rentalPlan">
                  <el-radio :value="1" label="固定租金（按月）" />
                  <el-radio :value="2" label="按天计算" />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="月计费方式" prop="monthBillingWay">
                <el-radio-group v-model="form.monthBillingWay">
                  <el-radio :value="1" label="整月" />
                  <el-radio :value="2" label="自然月" />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="年计算方式" prop="daysOfYear">
                <el-radio-group v-model="form.daysOfYear">
                  <el-radio :value="1" label="365天" />
                  <el-radio :value="2" label="366天" />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="首期收款日期" prop="paymentFirstDate">
                <el-date-picker
                  type="date"
                  v-model="form.paymentFirstDate"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择合同签订日期"
                  class="ele-fluid"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="收款截止日期" prop="paymentEndDateDays">
                <div class="formdata-flex">
                  <el-select
                    clearable
                    v-model="form.paymentEndDateType"
                    placeholder="请选择"
                    class="ele-fluid"
                  >
                    <el-option :value="1" label="每期开始日前" />
                    <el-option :value="2" label="每期开始日后" />
                    <el-option :value="3" label="每期结束日前" />
                    <el-option :value="4" label="每期结束日后" />
                  </el-select>
                  <div>
                    <el-input
                      clearable
                      v-model="form.paymentEndDateDays"
                      placeholder="请输入天数"
                    >
                      <template #append>天</template>
                    </el-input>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="租金单价" prop="unitPrice">
                <div class="formdata-flex">
                  <el-input
                    clearable
                    type="number"
                    v-model="form.unitPrice"
                    placeholder="请输入单价"
                    class="input-wh"
                  />
                  <el-select
                    clearable
                    v-model="form.paymentEndDateType"
                    placeholder="请选择"
                    class="ele-fluid"
                  >
                    <el-option :value="1" label="元/平米/天" />
                    <el-option :value="2" label="元/平米/月" />
                    <el-option :value="3" label="元/平米/年" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="付款周期" prop="paymentCycle">
                <div class="formdata-flex">
                  <el-input
                    clearable
                    type="number"
                    v-model="form.paymentCycle"
                    placeholder="请输入付款周期"
                    class="input-wh"
                  >
                    <template #append>个月</template>
                  </el-input>
                  <el-switch
                    v-model="form.isPaymentFull"
                    :active-value="1"
                    :inactive-value="0"
                  >
                  </el-switch>
                  &nbsp;一次性付清
                </div>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="租赁保证金(元)" prop="ensureFee">
                <el-input
                  clearable
                  v-model="form.ensureFee"
                  placeholder="请输入租赁保证金(元)"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="违约金(元)" prop="renegeFee">
                <el-input
                  clearable
                  v-model="form.renegeFee"
                  placeholder="请输入违约金(元)"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="滞纳金比例(%)" prop="penaltyAmtRatio">
                <el-input
                  clearable
                  v-model="form.penaltyAmtRatio"
                  placeholder="请输入滞纳金比例(%)"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="滞纳金基数类型" prop="penaltyAmtType">
                <el-radio-group v-model="form.penaltyAmtType">
                  <el-radio :value="1" label="租金" />
                  <el-radio :value="2" label="保证金" />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="租赁面积" prop="rentalArea">
                <el-input
                  clearable
                  v-model="form.rentalArea"
                  placeholder="请输入租赁面积"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
        <ele-card header="免租期">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getRentoalAddDel(1, '')">
                添加
              </el-button>
            </div>
            <el-table border :data="form.rentalHoliday">
              <el-table-column label="免租开始日期">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.startDate"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择合同签订日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="免租结束日期">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.endDate"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择合同签订日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getRentoalAddDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </ele-card>
        <ele-card header="租金递增">
          <div style="overflow: auto">
            <div class="file-flex-box">
              <el-radio-group v-model="form.rentIncrease.increaseType">
                <el-radio :value="1" label="下期账单递增" />
                <el-radio :value="2" label="当日递增" />
              </el-radio-group>
              <el-button type="primary" @click="getRentIncreaseAddDel(1, '')">
                添加
              </el-button>
            </div>
            <el-table border :data="form.rentIncrease.rentIncreaseItems">
              <el-table-column label="递增开始时间">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择递增开始时间"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="递增率(%)">
                <template #default="{ row }">
                  <el-input
                    type="number"
                    v-model="row.ratio"
                    class="input-wh"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getRentIncreaseAddDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </ele-card>
        <ele-card header="其它费用">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getPaymenOtherAddDel(1, '')">
                添加
              </el-button>
            </div>
            <el-table border :data="form.paymentOtherList">
              <el-table-column label="费用类型(必填)">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`paymentOtherList.${$index}.costType`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入费用类型',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input v-model="row.costType"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="总金额(必填)">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`paymentOtherList.${$index}.totalAmt`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入总金额',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input v-model="row.totalAmt"></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="缴费日期(必填)">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`paymentOtherList.${$index}.paymentDate`"
                    :rules="[
                      {
                        required: true,
                        message: '请选择缴费日期',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-date-picker
                      type="date"
                      v-model="row.paymentDate"
                      value-format="YYYY-MM-DD"
                      placeholder="请选择递增开始时间"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="滞纳金%(非必填)">
                <template #default="{ row }">
                  <el-form-item class="input-margin-left">
                    <el-input
                      type="number"
                      v-model="row.penaltyRatio"
                      class="input-wh"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getPaymenOtherAddDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </ele-card>
        <ele-card header="收款信息">
          <div style="overflow: auto">
            <div class="file-box">
              <el-button type="primary" @click="getPaymentPlanDel(1)">
                添加
              </el-button>
              <el-button type="primary" @click="generatePlanConfirm()">
                生成收款计划
              </el-button>
            </div>
            <el-table border :data="form.paymentPlanList">
              <el-table-column label="开始日期">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.startTime"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择开始日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="结束日期">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.endTime"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择结束日期"
                    class="date-time-o"
                  />
                </template>
              </el-table-column>
              <el-table-column label="收款日">
                <template #default="{ row }">
                  <el-date-picker
                    type="date"
                    v-model="row.paymentFirstDate"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择收款日"
                    class="ele-fluid"
                  />
                </template>
              </el-table-column>
              <el-table-column label="租金(元)">
                <template #default="{ row }">
                  <el-input v-model="row.unitPrice"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="{ $index }">
                  <el-button
                    type="primary"
                    link
                    @click="getPaymentPlanDel(2, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </ele-card>
      </el-form>
    </ele-page>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button type="primary" :loading="loading" @click="submit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined, PlusOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import type { UserItem } from '@/api/example/model';
  import { listAddedUsers } from '@/api/example';
  import {
    getContractAdd,
    getContractEdit,
    updateFile,
    getContractDetial,
    getGeneratePlan
  } from '@/api/lease/contract';

  // 路由
  const route = useRoute();
  const router = useRouter();
  /** 加载状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);

  /** 表单数据 */
  const form = ref({
    type: route.query.type,
    code: '',
    name: '',
    signingDate: '',
    signingChannel: 1,
    handler: '',
    isRenewal: 0,
    customerRefId: 1,
    customerType: 1,
    customerName: '',
    contactName: '',
    contactNumber: '',
    files: '', //文件列表
    dateTime: [], //租赁期限
    daysOfYear: 1,
    monthBillingWay: 1,
    rentalPlan: 1,
    paymentFirstDate: '',
    paymentEndDateType: 1,
    paymentEndDateDays: '',
    unitPrice: '',
    rentalUnit: 1,
    paymentCycle: '',
    isPaymentFull: 0,
    ensureFee: '',
    renegeFee: '',
    penaltyAmtRatio: '',
    penaltyAmtType: 1,
    rentalArea: '',
    rentIncrease: { increaseType: 1, rentIncreaseItems: [] },
    paymentOtherList: [],
    paymentPlanList: [],
    rentalHoliday: []
  });

  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    code: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
    name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    signingChannel: [
      { required: true, message: '请选择签到渠道', trigger: 'blur' }
    ],
    customerType: [
      { required: true, message: '请选择客户类型', trigger: 'blur' }
    ],
    rentalPlan: [
      { required: true, message: '请选择租金方案', trigger: 'blur' }
    ],
    monthBillingWay: [
      { required: true, message: '请选择月计费方式', trigger: 'blur' }
    ],
    daysOfYear: [
      { required: true, message: '请选择年计算方式', trigger: 'blur' }
    ],
    signingDate: [
      { required: true, message: '请选择合同签订日期', trigger: 'blur' }
    ],
    handler: [{ required: true, message: '请选择经办人', trigger: 'blur' }],
    customerName: [
      { required: true, message: '请输入客户名称', trigger: 'blur' }
    ],
    dateTime: [
      { required: true, message: '请选择租赁期限日期', trigger: 'blur' }
    ],
    paymentFirstDate: [
      { required: true, message: '请选择首期收款日期', trigger: 'blur' }
    ],
    paymentEndDateDays: [
      { required: true, message: '请输入收款截止日期', trigger: 'blur' }
    ],
    unitPrice: [{ required: true, message: '请输入租金单价', trigger: 'blur' }],
    paymentCycle: [
      { required: true, message: '请输入付款周期', trigger: 'blur' }
    ],
    rentalArea: [{ required: true, message: '请输入租赁面积', trigger: 'blur' }]
  });
  //上传文件
  const httpRequest = async ({ file }) => {
    let formData = new FormData();
    formData.append('file', file);
    let res = await updateFile(formData);
    console.log(res, '------');
  };
  //免租期添加内容
  const getRentoalAddDel = (type, index) => {
    if (type == 1) {
      form.value.rentalHoliday.push({ startDate: '', endDate: '' });
    } else {
      form.value.rentalHoliday.splice(index, 1);
    }
  };
  //租金递增添加
  const getRentIncreaseAddDel = (type, index) => {
    if (type == 1) {
      form.value.rentIncrease.rentIncreaseItems.push({ date: '', ratio: 0 });
    } else {
      form.value.rentIncrease.rentIncreaseItems.splice(index, 1);
    }
  };
  //其它费用添加
  const getPaymenOtherAddDel = (type, index) => {
    if (type == 1) {
      form.value.paymentOtherList.push({
        costType: '',
        totalAmt: '',
        paymentDate: '',
        penaltyRatio: 0
      });
    } else {
      form.value.paymentOtherList.splice(index, 1);
    }
  };
  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  onMounted(async () => {
    if (route.query.id) {
      form.value = await getContractDetial({ id: route.query.id });
    }
  });
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      validMsg.value = '';
      loading.value = true;
      loading.value = false;
      let res = null;
      let params = {
        ...form.value,
        startDate: '',
        endDate: ''
      };
      if (params.dateTime && params.dateTime.length > 0) {
        params.startDate = params.dateTime[0];
        params.endDate = params.dateTime[1];
      }
      // if (form.value.files && form.value.files.length === 0) {
      //   params.files = '';
      // }
      // if (
      //   form.value.paymentOtherList &&
      //   form.value.paymentOtherList.length === 0
      // ) {
      //   params.paymentOtherList = '';
      // }
      // if (
      //   form.value.paymentPlanList &&
      //   form.value.paymentPlanList.length === 0
      // ) {
      //   params.paymentPlanList = '';
      // }
      // if (form.value.rentalHoliday && form.value.rentalHoliday.length === 0) {
      //   params.rentalHoliday = '';
      // }
      // if (
      //   form.value.rentIncrease.rentIncreaseItems &&
      //   form.value.rentIncrease.rentIncreaseItems.length === 0
      // ) {
      //   params.rentIncrease.rentIncreaseItems = '';
      // }
      if (!route.query.id) {
        res = await getContractAdd(params);
      } else {
        res = await getContractEdit(params);
      }

      if (res) {
        EleMessage.success('提交成功');
        router.go(-1);
      }
      // setTimeout(() => {
      //   loading.value = false;
      //   EleMessage.success('提交成功');
      //   resetFields();
      // }, 1000);
    });
  };
  //新增收款信息
  const getPaymentPlanDel = (type, index) => {
    if (type == 1) {
      form.value.paymentPlanList.push({
        startTime: '',
        endTime: '',
        paymentFirstDate: '',
        unitPrice: ''
      });
    } else {
      form.value.paymentPlanList.splice(index, 1);
    }
  };
  //生成付款计划
  const generatePlanConfirm = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      validMsg.value = '';
      loading.value = true;
      loading.value = false;
      let params = {
        ...form.value,
        startDate: '',
        endDate: ''
      };
      if (params.dateTime && params.dateTime.length > 0) {
        params.startDate = params.dateTime[0];
        params.endDate = params.dateTime[1];
      }
      let res = await getGeneratePlan(params);
      if (res) {
        EleMessage.success('提交成功');
        router.go(-1);
      }
    });
  };
</script>

<script lang="ts">
  export default {
    name: 'FormAdvanced'
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: center;
    .ele-fluid {
      width: 230px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 150px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>
