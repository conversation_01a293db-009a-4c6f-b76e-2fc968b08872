<template>
  <ele-page plain hide-footer :multi-card="false">
    <ele-page>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent=""
      >
        <ele-card header="房东信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="房东类型" prop="type">
                <el-radio-group v-model="form.type">
                  <el-radio :value="1" label="企业" />
                  <el-radio :value="2" label="个人" />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="房东名称" prop="name">
                <el-input
                  clearable
                  v-model="form.name"
                  placeholder="请输入房东名称"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系人姓名" prop="contactName">
                <el-input
                  clearable
                  v-model="form.contactName"
                  placeholder="请输入联系人姓名"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系电话" prop="contactNumber">
                <el-input
                  clearable
                  maxlength="11"
                  v-model="form.contactNumber"
                  placeholder="请输入联系电话"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系人地址" prop="contactAddress">
                <el-input
                  clearable
                  v-model="form.contactAddress"
                  placeholder="请输入联系人地址"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="收款方名称" prop="recipientName">
                <el-input
                  clearable
                  v-model="form.recipientName"
                  placeholder="请输入收款方名称"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="开户银行" prop="depositBank">
                <el-input
                  clearable
                  v-model="form.depositBank"
                  placeholder="请输入开户银行"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="银行账户" prop="bankAccount">
                <el-input
                  clearable
                  v-model="form.bankAccount"
                  placeholder="请输入银行账户"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  clearable
                  v-model="form.remark"
                  placeholder="请输入备注"
                  maxlength="200"
                  show-word-limit
                  type="textarea"
                  :rows="5"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
        <ele-card header="房产信息">
          <div style="overflow: auto">
            <div class="file-box">
              <div> <h3>总面积</h3>{{ form.houseInfo.totalArea }}(m²) </div>
              <el-button type="primary">添加</el-button>
            </div>
            <el-table border :data="form.houseInfo.houseItemList">
              <el-table-column label="楼宇名称"></el-table-column>
              <el-table-column label="楼层"></el-table-column>
              <el-table-column label="房间"></el-table-column>
              <el-table-column label="面积"></el-table-column>
              <el-table-column label="操作"></el-table-column>
            </el-table>
          </div>
        </ele-card>
      </el-form>
    </ele-page>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button type="primary" :loading="loading" @click="submit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined, PlusOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import type { UserItem } from '@/api/example/model';
  import { listAddedUsers } from '@/api/example';
  import {
    getLandlordAdd,
    getLandlordEdit,
    getLandlordDetial
  } from '@/api/lease/landlord';
  //路由参数
  const route = useRoute();
  const router = useRouter();
  /** 加载状态 */
  const loading = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);
  /** 表单数据 */
  const form = ref({
    type: 1,
    name: '',
    contactName: '',
    contactNumber: '',
    contactAddress: '',
    contactEmail: '',
    recipientName: '',
    depositBank: '',
    bankAccount: '',
    remark: '',
    houseInfo: {
      totalArea: 0,
      houseItemList: []
    }
  });

  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    type: [
      {
        required: true,
        message: '请选择房东类型',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入房东名称',
        trigger: 'blur'
      }
    ],
    contactName: [
      {
        required: true,
        message: '请输入联系人姓名',
        trigger: 'blur'
      }
    ],
    contactNumber: [
      {
        required: true,
        message: '请输入联系人电话',
        trigger: 'blur'
      }
    ]
  });
  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  onMounted(async () => {
    if (route.query.id) {
      form.value = await getLandlordDetial({ id: route.query.id });
    }
  });
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      validMsg.value = '';
      loading.value = true;
      loading.value = false;
      let res = null;
      if (route.query.type == '1') {
        res = await getLandlordAdd(form.value);
      } else {
        res = await getLandlordEdit(form.value);
      }
      if (res) {
        EleMessage.success('提交成功');
        router.go(-1);
      }
    });
  };
</script>

<script lang="ts">
  export default {
    name: 'FormAdvanced'
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>
