<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!-- <role-search @search="reload" /> -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="getLandlordRouter(1, '')"
          >
            添加房东
          </el-button>
        </template>
        <template #action="{ row }">
          <el-button type="primary" link @click="getLandlordRouter(2, row)">
            修改
          </el-button>
          <el-button type="primary" link @click="getLandlordDelete(row)">
            删除
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { getLandlordPage, getLandlordDel } from '@/api/lease/landlord';

  // 路由
  const router = useRouter();
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '房东名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'houseNames',
        label: '房产信息',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'contactName',
        label: '联系人名称',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'contactNumber',
        label: '联系电话',
        width: 140,
        align: 'center'
      },
      {
        prop: 'contractNum',
        label: '合同数量',
        width: 140,
        align: 'center'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getLandlordPage({ ...where, ...filters, ...pages });
  };
  //添加-修改-房东
  const getLandlordRouter = (type, row) => {
    router.push({
      path: '/system/lease/landlordEdit',
      query: { type: type, id: row.id }
    });
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  //删除房东
  const getLandlordDelete = (row) => {
    ElMessageBox.confirm('确认删除房东数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await getLandlordDel(row);
        if (res) {
          EleMessage.success('删除成功');
          reload();
        } else {
          EleMessage.success('删除失败');
        }
      })
      .catch(() => console.info('操作取消'));
  };
</script>
