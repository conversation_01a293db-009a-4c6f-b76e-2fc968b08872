<template>
  <ele-modal
    form
    :width="860"
    top="40px"
    v-model="visible"
    :title="新增"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-row :gutter="8">
        <el-col :lg="12" :md="12" :sm="24" :xs="100">
          <el-form-item label="意向水平" prop="intentionLevel">
            <el-radio-group v-model="form.intentionLevel">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_INTENTION_LEVEL)"
                :key="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="8">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="跟进方式" prop="followWay">
            <el-select
              clearable
              v-model="form.followWay"
              placeholder="请选择跟进方式"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_FOLLOW_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :lg="8" :md="12" :sm="12" :xs="24">
        <el-form-item label="跟进时间" prop="followTime">
          <el-date-picker
            type="datetime"
            v-model="form.followTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择跟进时间"
            class="ele-fluid"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="12" :sm="12" :xs="24">
        <el-form-item label="跟进人员" prop="followUser">
          <ele-table-select
            filterable
            clearable
            placeholder="请选择"
            value-key="id"
            v-model:model-value="form.followUser"
            label-key="nickname"
            v-model="selectedValue2"
            :table-props="tableProps2"
            :popper-width="580"
            @filterChange="onFilterChange"
            @visibleChange="onVisibleChange"
          >
            <!-- 部门列 -->
            <template #depts="{ row }">
              <el-tag
                v-for="item in row.dept"
                :key="item.deptId"
                size="small"
                :disable-transitions="true"
              >
                {{ item.deptName }}
              </el-tag>
            </template>
          </ele-table-select>
        </el-form-item>
      </el-col>
      <el-form-item label="跟进内容" prop="followContent">
        <el-input
          clearable
          v-model="form.followContent"
          placeholder="请输入跟进内容"
          maxlength="300"
          show-word-limit
          type="textarea"
          :rows="5"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { getSimpleUserList } from '@/api/system/user';
  import { getOpportunityFollowAdd } from '@/api/lease/attract';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    opportunityId: void 0,
    intentionLevel: 1,
    followWay: 1,
    followTime: '',
    followUser: 1,
    followContent: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    intentionLevel: [
      { required: true, message: '意向水平不能为空', trigger: 'blur' }
    ],
    followWay: [
      { required: true, message: '跟进方式不能为空', trigger: 'blur' }
    ],
    followTime: [
      { required: true, message: '跟进时间不能为空', trigger: 'blur' }
    ],
    followUser: [
      { required: true, message: '跟进人员不能为空', trigger: 'blur' }
    ],
    followContent: [
      { required: true, message: '跟进内容不能为空', trigger: 'blur' }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        // 将日期转换为时间戳
        const requst = {
          ...form,
          followTime: new Date(form.followTime).getTime()
        };
        await getOpportunityFollowAdd(requst);
        EleMessage.success('修改成功');
        handleCancel();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      assignFields({ opportunityId: props.data.id });
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };

  /** 表格下拉选中值 */
  const selectedValue2 = ref();

  /** 表格配置 */
  const tableProps2 = reactive({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'nickname',
        label: '用户名',
        slot: 'nickname'
      },
      {
        prop: 'deptName',
        label: '部门',
        width: 80
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  let allData = [];

  /** 查询表格数据 */
  getSimpleUserList().then((data) => {
    tableProps2.datasource = data;
    allData = data;
  });

  /** 筛选输入框值改变事件 */
  const onFilterChange = (keyword) => {
    tableProps2.datasource = allData.filter((d) => {
      return d.nickname?.includes?.(keyword) || d.deptName?.includes?.(keyword);
    });
  };

  /** 下拉框展开状态改变事件 */
  const onVisibleChange = (visible) => {
    if (visible) {
      tableProps2.datasource = allData;
    }
  };
</script>
