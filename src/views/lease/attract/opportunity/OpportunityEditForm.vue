<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 商机详细 </span>
        </div>
      </template>
      <template #extra>
        <div class="flex items-center">
          <el-button type="primary" class="ml-2" @click="save">保存</el-button>
          <el-button type="primary" class="ml-2" @click="submmit"
            >提交</el-button
          >
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    @submit.prevent=""
  >
    <ele-card header="商机信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="商机标题" prop="title">
            <el-input
              maxlength="40"
              clearable
              v-model="form.title"
              placeholder="请输入商机标题"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="商机来源" prop="source">
            <el-select
              clearable
              v-model="form.source"
              placeholder="请选择商机来源"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="商机阶段" prop="stage">
            <el-select
              clearable
              v-model="form.stage"
              placeholder="请选择商机阶段"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.BUSINESS_OPPORTUNITY_STAGE
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              clearable
              v-model="form.remark"
              placeholder="请输入备注"
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="客户信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="task">
            <el-radio-group v-model="form.customerType">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_TYPE)"
                :key="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              clearable
              v-model="form.customerName"
              placeholder="请输入客户名称"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户简称" prop="customerShortName">
            <el-input
              clearable
              v-model="form.customerShortName"
              placeholder="请输入客户简称"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="industry">
            <el-select
              clearable
              v-model="form.industry"
              placeholder="请选择所属行业"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item
            :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
            prop="idNumber"
          >
            <el-input
              clearable
              v-model="form.idNumber"
              :placeholder="`${form.customerType == 1 ? '请输入纳税人识别号' : '请输入身份证号'}`"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              clearable
              v-model="form.contactName"
              placeholder="请输入联系人姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input
              clearable
              maxlength="11"
              v-model="form.contactNumber"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input
              clearable
              v-model="form.contactAddress"
              placeholder="请输入联系地址"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
  </el-form>
</template>
<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    getOpportunityAdd,
    getOpportunityEdit,
    getOpportunityDetial,
    getOpportunitySubmmit
  } from '@/api/lease/attract';

  /** 微信账户管理 表单 */
  defineOptions({ name: 'OpportunityEditForm' });

  const props = defineProps({
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    source: 1,
    stage: '',
    customerType: 1,
    customerName: '',
    customerShortName: '',
    idNumber: '',
    industry: '',
    contactName: '',
    contactNumber: '',
    contactAddress: '',
    contactEmail: '',
    remark: ''
  });

  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    title: [
      {
        required: true,
        message: '请输入商机标题',
        trigger: 'blur'
      }
    ],
    source: [
      {
        required: true,
        message: '请选择商机来源',
        trigger: 'blur'
      }
    ],
    stage: [
      {
        required: true,
        message: '请选择商机阶段',
        trigger: 'blur'
      }
    ],
    customerType: [
      {
        required: true,
        message: '请选择客户类型',
        trigger: 'blur'
      }
    ],
    customerName: [
      {
        required: true,
        message: '请输入客户名称',
        trigger: 'blur'
      },
      {
        pattern: /^(?! )[a-zA-Z0-9\u4e00-\u9fa5()\-_@:/ ]+(?<! )$/, // 修改后的正则表达式：不允许空格在首位和末尾
        message: '客户名称只能包含汉字、英文、数字、括号、空格、-_@:/，且空格不能在首位或末尾',
        trigger: 'blur'
      }
    ],
    customerShortName: [
      {
        required: true,
        message: '请输入客户简称',
        trigger: 'blur'
      },
      {
        pattern: /^(?! )[a-zA-Z0-9\u4e00-\u9fa5()\-_@:/ ]+(?<! )$/, // 修改后的正则表达式：不允许空格在首位和末尾
        message: '客户简称只能包含汉字、英文、数字、括号、空格、-_@:/，且空格不能在首位或末尾',
        trigger: 'blur'
      }
    ],
    contactName: [
      {
        required: true,
        message: '请输入联系人姓名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    contactNumber: [
      {
        required: true,
        message: '请输入联系人电话',
        type: 'string',
        trigger: 'blur'
      }
    ],
    idNumber: [
      {
        required: true,
        message: '请输入纳税人识别号或者身份证信息',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        const response = await getOpportunityAdd(form); // 调用 getOpportunityAdd 方法提交数据
        if (response) {
          assignFields(response);
          isUpdate.value = true;
        }
        message.success('新建成功');
      } else {
        await getOpportunityEdit(form);
        message.success('保存成功');
      }
      visible.value = false;
      // 发送操作成功的事件
      //emit('done');
    } finally {
      loading.value = false;
    }
  };

  const submmit = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        const response = await getOpportunityAdd(form); // 调用 getOpportunityAdd 方法提交数据
        assignFields(response);
      } else {
        await getOpportunityEdit(form);
      }
      await getOpportunitySubmmit(form);
      //保存完成后，提交数据
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };

  const initFormInfo = async (row) => {
    if (row) {
      assignFields(row);
    } else {
      resetFields();
    }
    await loadData();
  };
  defineExpose({ initFormInfo });

  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      try {
        // 假设有一个 API 可以获取最新数据
        const response = await getOpportunityDetial({ id: props.data.id });
        if (response) {
          assignFields(response);
          isUpdate.value = true;
        }
      } catch (error) {
        console.error('获取商机详情失败', error);
      }
    } else {
      resetFields();
      isUpdate.value = false;
    }
  };
</script>
