<template>
  <ele-modal
    form
    :width="560"
    top="240px"
    v-model="visible"
    :title="新增"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-col :lg="16" :md="12" :sm="12" :xs="24">
        <el-form-item label="分配人" prop="assignId">
          <ele-table-select
            filterable
            clearable
            placeholder="请选择"
            value-key="id"
            v-model:model-value="form.assignId"
            label-key="nickname"
            v-model="selectedValue2"
            @select="onAssignUserSelect"
            :table-props="tableProps2"
            :popper-width="480"
            @filterChange="onFilterChange"
            @visibleChange="onVisibleChange"
            :init-value="initHandlerValue"
          >
            <!-- 部门列 -->
            <template #depts="{ row }">
              <el-tag
                v-for="item in row.dept"
                :key="item.deptId"
                size="small"
                :disable-transitions="true"
              >
                {{ item.deptName }}
              </el-tag>
            </template>
          </ele-table-select>
        </el-form-item>
      </el-col>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { getSimpleUserList } from '@/api/system/user';
  import { getOpportunityAssignMoidfy } from '@/api/lease/attract';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  //分配人回显
  const initHandlerValue = ref();

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    assignId: void 0,
    assignName: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    assignName: [
      { required: true, message: '分配人员不能为空', trigger: 'blur' }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        // 将日期转换为时间戳
        const requst = {
          ...form
        };
        await getOpportunityAssignMoidfy(requst);
        EleMessage.success('修改成功');
        handleCancel();
        emit('done');
      } finally {
        loading.value = false;
      }
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      assignFields({
        id: props.data.id,
        assignId: props.data.assignId,
        assignName: props.data.assignName
      });
      //设置回显数据
      setInitValue({
        id: props.data.id,
        assignId: props.data.assignId,
        assignName: props.data.assignName
      });
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };

  /** 表格下拉选中值 */
  const selectedValue2 = ref();

  /** 表格配置 */
  const tableProps2 = reactive({
    datasource: [],
    columns: [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'nickname',
        label: '用户名',
        slot: 'nickname'
      },
      {
        prop: 'deptName',
        label: '部门',
        width: 180
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowStyle: { cursor: 'pointer' }
  });

  let allData = [];

  /** 查询表格数据 */
  getSimpleUserList().then((data) => {
    tableProps2.datasource = data;
    allData = data;
  });

  /** 筛选输入框值改变事件 */
  const onFilterChange = (keyword) => {
    tableProps2.datasource = allData.filter((d) => {
      return d.nickname?.includes?.(keyword) || d.deptName?.includes?.(keyword);
    });
  };

  /** 下拉框展开状态改变事件 */
  const onVisibleChange = (visible) => {
    if (visible) {
      tableProps2.datasource = allData;
    }
  };

  /** 选中事件 */
  const onAssignUserSelect = (selection) => {
    form.assignId = selection.id;
    form.assignName = selection.nickname;
  };

  //设置回显值
  const setInitValue = (data) => {
    //经办人回显
    initHandlerValue.value = {
      id: data.assignId,
      nickname: data.assignName
    };
  };
</script>
