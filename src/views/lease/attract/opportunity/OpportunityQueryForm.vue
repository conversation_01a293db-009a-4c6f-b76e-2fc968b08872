<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 商机详细 </span>
        </div>
      </template>
      <template #extra>
        <div class="flex items-center">
          <el-button
            v-if="form.billStatus == '20'"
            type="primary"
            class="ml-2"
            @click="followEdit"
            >跟进商机</el-button
          >
          <el-button
            v-if="form.billStatus == '20' && form.convertCustomerFlag !== '1'"
            type="primary"
            class="ml-2"
            @click="autoCustomer"
            >转为客户</el-button
          >
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    @submit.prevent=""
  >
    <ele-card header="商机信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="商机标题" prop="title">
            <el-input
              maxlength="40"
              clearable
              v-model="form.title"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="商机来源" prop="source">
            <el-select
              clearable
              v-model="form.source"
              placeholder=""
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="商机阶段" prop="stage">
            <el-select
              clearable
              v-model="form.stage"
              placeholder=""
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.BUSINESS_OPPORTUNITY_STAGE
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              clearable
              v-model="form.remark"
              placeholder=""
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="5"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="客户信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="task">
            <el-radio-group v-model="form.customerType" disabled>
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_TYPE)"
                :key="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              clearable
              v-model="form.customerName"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户简称" prop="customerShortName">
            <el-input
              clearable
              v-model="form.customerShortName"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="industry">
            <el-select
              clearable
              v-model="form.industry"
              placeholder=""
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item
            :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
            prop="idNumber"
          >
            <el-input clearable v-model="form.idNumber" disabled />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              clearable
              v-model="form.contactName"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input
              clearable
              maxlength="11"
              v-model="form.contactNumber"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input
              clearable
              v-model="form.contactAddress"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="跟进信息">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="followdatasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #intentionLevel="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_INTENTION_LEVEL"
            :model-value="row.intentionLevel"
          />
        </template>
        <template #followWay="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_FOLLOW_TYPE"
            :model-value="row.followWay"
          />
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 跟进弹窗 -->
    <OpportunityFollowEdit
      v-model="showFollowEdit"
      :data="current"
      @done="followReload"
    />
  </el-form>
</template>
<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, computed } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import OpportunityFollowEdit from './OpportunityFollowEdit.vue';
  import { dateFormatter } from '@/utils/formatTime';
  import {
    getOpportunityDetial,
    getOpportunityFollowPage,
    getOpportunityToCustomer
  } from '@/api/lease/attract';

  /** 微信账户管理 表单 */
  defineOptions({ name: 'OpportunityEditForm' });

  const props = defineProps({
    data: Object
  });

  /** 当前编辑数据 */
  const current = ref(null);

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  /** 是否显示跟进编辑弹窗 */
  const showFollowEdit = ref(false);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 搜索 */
  const followReload = () => {
    if (tableRef.value) {
      tableRef.value.reload();
    }
  };

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    source: 1,
    stage: '',
    customerType: 1,
    customerName: '',
    customerShortName: '',
    idNumber: '',
    industry: '',
    contactName: '',
    contactNumber: '',
    contactAddress: '',
    contactEmail: '',
    remark: '',
    billStatus: '',
    convertCustomerFlag: '0' // 添加 convertCustomerFlag 字段
  });

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };

  /** 打开商机跟进编辑弹窗 */
  const followEdit = async () => {
    current.value = form ?? null;
    showFollowEdit.value = true;
  };
  // 路由
  const router = useRouter();
  const autoCustomer = async () => {
    // 提交请求
    loading.value = true;
    try {
      await getOpportunityToCustomer(form); // 调用 getOpportunityAdd 方法提交数据
      visible.value = false;
      // 修改：将路由路径指向 CustomEditForm.vue
      router.push({
        path: '/lease/attract/myCustomer'
      });
    } finally {
      loading.value = false;
    }
  };
  const initFormInfo = async (row) => {
    if (row) {
      assignFields(row);
    } else {
      resetFields();
    }
    await loadData();
  };
  defineExpose({ initFormInfo });

  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      try {
        // 假设有一个 API 可以获取最新数据
        const response = await getOpportunityDetial({ id: props.data.id });
        if (response) {
          assignFields(response);
          isUpdate.value = true;
        }
      } catch (error) {
        console.error('获取商机详情失败', error);
      }
    } else {
      resetFields();
      isUpdate.value = false;
    }
  };

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'intentionLevel',
        label: '意向水平',
        align: 'center',
        minWidth: 50,
        slot: 'intentionLevel'
      },
      {
        prop: 'followWay',
        label: '跟进方式',
        align: 'center',
        minWidth: 50,
        slot: 'followWay'
      },
      {
        prop: 'followTime',
        label: '跟进时间',
        align: 'center',
        minWidth: 50,
        formatter: dateFormatter
      },
      {
        prop: 'followUserName',
        label: '跟进人',
        width: 50,
        align: 'center'
      },
      {
        prop: 'followContent',
        label: '跟进内容',
        width: 140,
        align: 'center'
      },
      {
        prop: 'createTime',
        label: '跟进记录时间	',
        align: 'center',
        minWidth: 50,
        formatter: dateFormatter
      }
    ];
  });
  /** 表格数据源 */
  const followdatasource = ({ pages, where, filters }) => {
    let opportunityId = form.id !== undefined ? form.id : props.data.id;
    return getOpportunityFollowPage({
      ...where,
      ...filters,
      ...pages,
      opportunityId: opportunityId
    });
  };
</script>
