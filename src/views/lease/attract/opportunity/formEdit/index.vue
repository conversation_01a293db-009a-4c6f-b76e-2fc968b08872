<template>
  <ele-page plain hide-footer :multi-card="false">
    <ele-page>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent=""
      >
        <ele-card header="商机信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="商机标题" prop="title">
                <el-input
                  maxlength="40"
                  clearable
                  v-model="form.title"
                  placeholder="请输入商机标题"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="商机来源" prop="source">
                <el-select
                  clearable
                  v-model="form.source"
                  placeholder="请选择商机来源"
                  class="ele-fluid"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(
                      DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="商机阶段" prop="stage">
                <el-select
                  clearable
                  v-model="form.stage"
                  placeholder="请选择商机阶段"
                  class="ele-fluid"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(
                      DICT_TYPE.BUSINESS_OPPORTUNITY_STAGE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  clearable
                  v-model="form.remark"
                  placeholder="请输入备注"
                  maxlength="300"
                  show-word-limit
                  type="textarea"
                  :rows="5"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
        <ele-card header="客户信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户类型" prop="task">
                <el-radio-group v-model="form.customerType">
                  <el-radio
                    v-for="dict in getIntDictOptions(
                      DICT_TYPE.CO_CUSTOMER_TYPE
                    )"
                    :key="dict.label"
                    :value="dict.value"
                  >
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户名称" prop="customerName">
                <el-input
                  clearable
                  v-model="form.customerName"
                  placeholder="请输入客户名称"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="所属行业" prop="industry">
                <el-select
                  clearable
                  v-model="form.industry"
                  placeholder="请选择所属行业"
                  class="ele-fluid"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item
                :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
                prop="idNumber"
              >
                <el-input
                  clearable
                  v-model="form.idNumber"
                  :placeholder="`${form.customerType == 1 ? '请输入纳税人识别号' : '请输入身份证号'}`"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系人姓名" prop="contactName">
                <el-input
                  clearable
                  v-model="form.contactName"
                  placeholder="请输入联系人姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系电话" prop="contactNumber">
                <el-input
                  clearable
                  maxlength="11"
                  v-model="form.contactNumber"
                  placeholder="请输入联系电话"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系地址" prop="contactAddress">
                <el-input
                  clearable
                  v-model="form.contactAddress"
                  placeholder="请输入联系地址"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
      </el-form>
    </ele-page>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button type="primary" :loading="loading" @click="submit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined, PlusOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import type { UserItem } from '@/api/example/model';
  import { listAddedUsers } from '@/api/example';
  import {
    getOpportunityAdd,
    getOpportunityEdit,
    getOpportunityDetial
  } from '@/api/lease/attract';
  //路由参数
  const route = useRoute();
  const router = useRouter();
  /** 加载状态 */
  const loading = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);

  /** 表单数据 */
  const form = ref({
    id: void 0,
    title: '',
    source: 1,
    stage: '',
    customerType: 1,
    customerName: '',
    idNumber: '',
    industry: '',
    contactName: '',
    contactNumber: '',
    contactAddress: '',
    contactEmail: '',
    remark: ''
  });

  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    title: [
      {
        required: true,
        message: '请输入商机标题',
        trigger: 'blur'
      }
    ],
    source: [
      {
        required: true,
        message: '请选择商机来源',
        trigger: 'blur'
      }
    ],
    stage: [
      {
        required: true,
        message: '请选择商机阶段',
        trigger: 'blur'
      }
    ],
    customerType: [
      {
        required: true,
        message: '请选择客户类型',
        trigger: 'blur'
      }
    ],
    customerName: [
      {
        required: true,
        message: '请输入客户名称',
        trigger: 'blur'
      }
    ],
    contactName: [
      {
        required: true,
        message: '请输入联系人姓名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    contactNumber: [
      {
        required: true,
        message: '请输入联系人电话',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  onMounted(async () => {
    if (route.query.id) {
      form.value = await getOpportunityDetial({ id: route.query.id });
    }
  });
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      validMsg.value = '';
      loading.value = true;
      loading.value = false;
      let res = null;
      if (route.query.type == '1') {
        res = await getOpportunityAdd(form.value);
      } else {
        res = await getOpportunityEdit(form.value);
      }
      if (res) {
        EleMessage.success('提交成功');
        router.go(-1);
      }
      // if (res.code == 200) {
      //   loading.value = false;
      //   EleMessage.success('提交成功');
      //   resetFields();
      // }
    });
  };
</script>

<script lang="ts">
  export default {
    name: 'FormAdvanced'
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
  }
</style>
