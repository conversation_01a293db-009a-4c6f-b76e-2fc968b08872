<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!-- <role-search @search="reload" /> -->
    <ele-card
      v-if="showStep == 1"
      flex-table
      :body-style="{ paddingTop: '8px' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        cache-key="systemMyCustomerTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit(null)"
          >
            添加客户
          </el-button>
        </template>
        <template #customerName="{ row }">
          <span class="title-clickable" @click="openQuery(row)">
            {{ row.customerName }}
          </span>
        </template>
        <template #customerType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CUSTOMER_TYPE"
            :model-value="row.customerType"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CUSTOMER_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #industry="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_SECTOR"
            :model-value="row.industry"
          />
        </template>
        <template #billStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_STATUS"
            :model-value="row.billStatus"
          />
        </template>
        <template #sourceFrom="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_SOURCE_NETHOD"
            :model-value="row.sourceFrom"
          />
        </template>
        <template #synStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_PAY_SYN_STATUS"
            :model-value="row.synStatus"
          />
        </template>
        <template #action="{ row }">
          <el-button type="primary" link @click="followEdit(row)">
            跟进
          </el-button>
          <el-button
            v-if="row.billStatus !== '20'"
            type="primary"
            link
            @click="openEdit(row)"
          >
            修改
          </el-button>
          <el-button type="primary" link @click="getCustomeDelete(row)">
            删除
          </el-button>
          <el-button type="primary" link @click="assignEdit(row)">
            分配
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <CustomEditForm
      ref="customRef"
      v-if="showStep == 2"
      :data="current"
      @done="stopDone"
    />
    <CustomQueryForm
      ref="customRef"
      v-if="showStep == 3"
      :data="current"
      @done="stopDone"
    />
    <!-- 跟进弹窗 -->
    <CustomerFollowEditForm
      v-model="showFollowEdit"
      :data="current"
      @done="stopDone"
    />
    <!-- 分配弹窗 -->
    <AssignEdit v-model="showAssignEdit" :data="current" @done="stopDone" />
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { getCustomeDel, getCustomerPageByMe } from '@/api/lease/attract';
  import CustomEditForm from '../customer/CustomEditForm.vue';
  import CustomQueryForm from '../customer/CustomQueryForm.vue';
  import CustomerFollowEditForm from '../customer/CustomerFollowEdit.vue';
  import AssignEdit from '../customer/AssignEdit.vue';

  const customRef = ref(null);
  /** 表格实例 */
  const tableRef = ref(null);

  const showStep = ref(1);

  /** 是否显示分配弹窗 */
  const showAssignEdit = ref(false);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'customerNum',
        label: '客户编码',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'customerName',
        label: '客户名称',
        align: 'center',
        minWidth: 150,
        slot: 'customerName'
      },
      {
        prop: 'customerShortName',
        label: '客户简称',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'customerType',
        label: '客户类型',
        align: 'center',
        minWidth: 120,
        slot: 'customerType'
      },
      {
        prop: 'status',
        label: '客户状态',
        align: 'center',
        minWidth: 120,
        slot: 'status'
      },
      {
        prop: 'industry',
        label: '所属行业',
        width: 120,
        align: 'center',
        slot: 'industry'
      },
      {
        prop: 'createName',
        label: '创建人姓名',
        width: 120,
        align: 'center'
      },
      {
        prop: 'unfollowedDays',
        label: '未跟进天数',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'billStatus',
        label: '状态',
        align: 'center',
        minWidth: 80,
        slot: 'billStatus'
      },
      {
        prop: 'sourceFrom',
        label: '来源方式',
        align: 'center',
        minWidth: 100,
        slot: 'sourceFrom'
      },
      {
        prop: 'assignName',
        label: '分配人',
        align: 'center',
        minWidth: 80
      },
      {
        prop: 'synStatus',
        label: '同步状态',
        align: 'center',
        minWidth: 80,
        slot: 'synStatus'
      },
      {
        prop: 'synMessage',
        label: '同步消息',
        align: 'center',
        minWidth: 150
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        slot: 'action',
        fixed: 'right',
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });

  /** 打开分配编辑弹窗 */
  const assignEdit = (row) => {
    current.value = row ?? null;
    showAssignEdit.value = true;
  };

  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getCustomerPageByMe({ ...where, ...filters, ...pages });
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };

  /** 当前编辑数据 */
  const current = ref(null);
  const openEdit = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    customRef.value?.initFormInfo(current);
  };

  const stopDone = () => {
    showStep.value = 1;
    reload();
  };
  //删除客户
  const getCustomeDelete = (row) => {
    ElMessageBox.confirm('确认删客户数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await getCustomeDel(row);
        if (res) {
          EleMessage.success('删除成功');
          reload();
        } else {
          EleMessage.success('删除失败');
        }
      })
      .catch(() => console.info('操作取消'));
  };
  const openQuery = async (row) => {
    current.value = row ?? null;
    showStep.value = 3;
    // 初始化流程定义详情
    await nextTick();
    customRef.value?.initFormInfo(current);
  };

  /** 是否显示跟进编辑弹窗 */
  const showFollowEdit = ref(false);

  /** 打开商机跟进编辑弹窗 */
  const followEdit = (row) => {
    current.value = row ?? null;
    showFollowEdit.value = true;
  };
</script>
<style scoped>
  .title-clickable {
    cursor: pointer;
    color: #409eff;
    text-decoration: underline;
  }
</style>
