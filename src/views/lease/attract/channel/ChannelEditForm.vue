<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 公司信息 </span>
        </div>
      </template>
      <template #extra>
        <div class="flex items-center">
          <el-button type="primary" class="ml-2" @click="save">提交</el-button>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="140px"
    @submit.prevent=""
  >
    <ele-card header="基本信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="公司名称" prop="name">
            <el-input
              clearable
              v-model="form.name"
              placeholder="请输入公司名称"
              class="ele-fluid"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="industry">
            <el-select
              clearable
              v-model="form.industry"
              placeholder="请选择所属行业"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              clearable
              v-model="form.contactName"
              placeholder="请输入联系人姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input
              clearable
              maxlength="11"
              v-model="form.contactNumber"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="固定电话" prop="fixedPhone">
            <el-input
              clearable
              v-model="form.fixedPhone"
              placeholder="请输入固定电话"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="电子邮箱" prop="contactEmail">
            <el-input
              clearable
              v-model="form.contactEmail"
              placeholder="请输入电子邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input
              clearable
              v-model="form.contactAddress"
              placeholder="请输入联系地址"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              clearable
              v-model="form.remark"
              placeholder="请输入备注"
              maxlength="200"
              show-word-limit
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
  </el-form>
</template>
<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  import { useFormData } from '@/utils/use-form-data';
  import {
    getPartnerAdd,
    getPartnerEdit,
    getPartnerDetial
  } from '@/api/lease/attract';

  /** 微信账户管理 表单 */
  defineOptions({ name: 'ChannelEditForm' });

  const props = defineProps({
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    name: '',
    industry: 1,
    contactName: '',
    contactNumber: '',
    fixedPhone: '',
    contactAddress: '',
    contactEmail: '',
    contractNum: '',
    remark: ''
  });
  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入公司名称',
        trigger: 'blur'
      }
    ],
    contactName: [
      {
        required: true,
        message: '请输入联系人姓名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    contactNumber: [
      {
        required: true,
        message: '请输入联系人电话',
        trigger: 'blur'
      }
    ]
  });
  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };

  const save = async () => {
    if (!formRef) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      if (!isUpdate.value) {
        await getPartnerAdd(form); // 调用 getCustomerAdd 方法提交数据
        message.success(t('common.createSuccess'));
      } else {
        await getPartnerEdit(form);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };
  const initFormInfo = async (row) => {
    if (row) {
      assignFields(row);
    } else {
      resetFields();
    }
    await loadData();
  };
  defineExpose({ initFormInfo });

  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      try {
        // 假设有一个 API 可以获取最新数据
        const response = await getPartnerDetial({ id: props.data.id });
        if (response) {
          assignFields(response);
          isUpdate.value = true;
        }
      } catch (error) {
        console.error('获取公司详情失败', error);
      }
    } else {
      resetFields();
      isUpdate.value = false;
    }
  };
</script>
