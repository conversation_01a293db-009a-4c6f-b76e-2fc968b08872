<template>
  <ele-page plain hide-footer :multi-card="false">
    <ele-page>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent=""
      >
        <ele-card header="基本信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="公司名称" prop="name">
                <el-input
                  clearable
                  v-model="form.name"
                  placeholder="请输入公司名称"
                  class="ele-fluid"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="所属行业" prop="industry">
                <el-select
                  clearable
                  v-model="form.industry"
                  placeholder="请选择所属行业"
                  class="ele-fluid"
                >
                <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系人姓名" prop="contactName">
                <el-input
                  clearable
                  v-model="form.contactName"
                  placeholder="请输入联系人姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系电话" prop="contactNumber">
                <el-input
                  clearable
                  maxlength="11"
                  v-model="form.contactNumber"
                  placeholder="请输入联系电话"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="固定电话" prop="fixedPhone">
                <el-input
                  clearable
                  v-model="form.fixedPhone"
                  placeholder="请输入固定电话"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="电子邮箱" prop="contactEmail">
                <el-input
                  clearable
                  v-model="form.contactEmail"
                  placeholder="请输入电子邮箱"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系地址" prop="contactAddress">
                <el-input
                  clearable
                  v-model="form.contactAddress"
                  placeholder="请输入联系地址"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  clearable
                  v-model="form.remark"
                  placeholder="请输入备注"
                  maxlength="200"
                  show-word-limit
                  type="textarea"
                  :rows="5"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
      </el-form>
    </ele-page>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button type="primary" :loading="loading" @click="submit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined, PlusOutlined } from '@/components/icons';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { useFormData } from '@/utils/use-form-data';
  import type { UserItem } from '@/api/example/model';
  import { listAddedUsers } from '@/api/example';
  import {
    getPartnerAdd,
    getPartnerEdit,
    getPartnerDetial
  } from '@/api/lease/attract';
  //路由参数
  const route = useRoute();
  const router = useRouter();
  /** 加载状态 */
  const loading = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);

  /** 表单数据 */
  const form = ref({
    name: '',
    industry: 1,
    contactName: '',
    contactNumber: '',
    fixedPhone: '',
    contactAddress: '',
    contactEmail: '',
    contractNum: ''
  });
  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    name: [
      {
        required: true,
        message: '请输入公司名称',
        trigger: 'blur'
      }
    ],
    contactName: [
      {
        required: true,
        message: '请输入联系人姓名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    contactNumber: [
      {
        required: true,
        message: '请输入联系人电话',
        trigger: 'blur'
      }
    ]
  });

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  onMounted(async () => {
    if (route.query.id) {
      form.value = await getPartnerDetial({ id: route.query.id });
      console.log(form, '------');
    }
  });
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      validMsg.value = '';
      loading.value = true;
      let res = null;
      if (route.query.type == '1') {
        res = await getPartnerAdd(form.value);
      } else {
        res = await getPartnerEdit(form.value);
      }
      loading.value = false;
      if (res) {
        EleMessage.success('提交成功');
        router.go(-1);
      }
      // setTimeout(() => {
      //   loading.value = false;
      //   EleMessage.success('提交成功');
      //   resetFields();
      // }, 1000);
    });
  };
</script>

<script lang="ts">
  export default {
    name: 'FormAdvanced'
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
  }
</style>
