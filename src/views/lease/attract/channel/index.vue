<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!-- <role-search @search="reload" /> -->
    <ele-card
      v-if="showStep == 1"
      flex-table
      :body-style="{ paddingTop: '8px' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit(null)"
          >
            添加公司
          </el-button>
        </template>
        <template #industry="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_SECTOR"
            :model-value="row.industry"
          />
        </template>
        <template #action="{ row }">
          <el-button type="primary" link @click="openEdit(row)">
            修改
          </el-button>
          <el-button type="primary" link @click="getChannelDelete(row)">
            删除
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <ChannelEditForm
      ref="channelRef"
      v-if="showStep == 2"
      :data="current"
      @done="stopDone"
    />
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { getPartnerPage, getCustomeDel } from '@/api/lease/attract';
  import ChannelEditForm from './ChannelEditForm.vue';
  /** 表格实例 */
  const tableRef = ref(null);
  const channelRef = ref(null);

  const showStep = ref(1);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '公司名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'industry',
        label: '所属行业',
        align: 'center',
        minWidth: 110,
        slot: 'industry'
      },
      {
        prop: 'contactName',
        label: '联系人',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'contactNumber',
        label: '联系电话',
        width: 140,
        align: 'center'
      },
      {
        prop: 'createTime',
        label: '创建开始时间',
        width: 140,
        align: 'center',
        formatter: dateFormatter
      },
      // {
      //   prop: 'createEndTime',
      //   label: '创建结束时间',
      //   width: 140,
      //   align: 'center',
      //   formatter: dateFormatter
      // },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getPartnerPage({ ...where, ...filters, ...pages });
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  /** 当前编辑数据 */
  const current = ref(null);
  const openEdit = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    channelRef.value?.initFormInfo(current);
  };

  const stopDone = () => {
    showStep.value = 1;
    reload();
  };
  //删除公司
  const getChannelDelete = (row) => {
    ElMessageBox.confirm('确认删除公司数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await getCustomeDel(row);
        if (res) {
          EleMessage.success('删除成功');
          reload();
        } else {
          EleMessage.success('删除失败');
        }
      })
      .catch(() => console.info('操作取消'));
  };
</script>
