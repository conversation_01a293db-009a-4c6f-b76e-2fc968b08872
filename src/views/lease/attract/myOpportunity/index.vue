<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!-- <role-search @search="reload" /> -->
    <ele-card
      v-if="showStep == 1"
      flex-table
      :body-style="{ paddingTop: '8px' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit(null)"
          >
            添加商机
          </el-button>
        </template>
        <template #title="{ row }">
          <span class="title-clickable" @click="openQuery(row)">
            {{ row.title }}
          </span>
        </template>
        <template #source="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE"
            :model-value="row.source"
          />
        </template>
        <template #stage="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BUSINESS_OPPORTUNITY_STAGE"
            :model-value="row.stage"
          />
        </template>
        <template #billStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_STATUS"
            :model-value="row.billStatus"
          />
        </template>
        <template #sourceFrom="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_SOURCE_NETHOD"
            :model-value="row.sourceFrom"
          />
        </template>
        <template #action="{ row, $index }">
          <el-button type="primary" link @click="followEdit(row)">
            跟进
          </el-button>
          <el-button
            v-if="row.billStatus !== '20'"
            type="primary"
            link
            @click="openEdit(row)"
          >
            修改
          </el-button>
          <el-button type="primary" link @click="getOpportunityDelete(row)">
            删除
          </el-button>
          <el-button type="primary" link @click="assignEdit(row)">
            分配
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <OpportunityEditForm
      ref="opportunityRef"
      v-if="showStep == 2"
      :data="current"
      @done="stopDone"
    />
    <OpportunityQueryForm
      ref="opportunityRef"
      v-if="showStep == 3"
      :data="current"
      @done="stopDone"
    />
    <!-- 跟进弹窗 -->
    <OpportunityFollowEdit
      v-model="showFollowEdit"
      :data="current"
      @done="stopDone"
    />
    <!-- 分配弹窗 -->
    <AssignEdit v-model="showAssignEdit" :data="current" @done="stopDone" />
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import OpportunityEditForm from '../opportunity/OpportunityEditForm.vue';
  import OpportunityQueryForm from '../opportunity/OpportunityQueryForm.vue';
  import OpportunityFollowEdit from '../opportunity/OpportunityFollowEdit.vue';
  import AssignEdit from '../opportunity/AssignEdit.vue';
  import {
    getOpportunityPageByMe,
    getOpportunityDel
  } from '@/api/lease/attract';

  const opportunityRef = ref(null);

  /** 是否显示跟进编辑弹窗 */
  const showFollowEdit = ref(false);

  /** 是否显示分配弹窗 */
  const showAssignEdit = ref(false);

  const showStep = ref(1);
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'title',
        label: '商机标题',
        align: 'center',
        minWidth: 110,
        slot: 'title'
      },
      {
        prop: 'customerName',
        label: '客户名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'customerShortName',
        label: '客户简称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'stage',
        label: '商机阶段',
        align: 'center',
        minWidth: 50,
        slot: 'stage'
      },
      {
        prop: 'source',
        label: '商机来源',
        width: 140,
        align: 'center',
        slot: 'source'
      },
      {
        prop: 'createName',
        label: '创建人',
        width: 100,
        align: 'center'
      },
      {
        prop: 'lastFollowTime',
        label: '最后跟进',
        align: 'center',
        minWidth: 50,
        formatter: dateFormatter
      },
      {
        prop: 'unfollowedDays',
        label: '未跟进天数',
        align: 'center',
        minWidth: 50
      },
      {
        prop: 'billStatus',
        label: '状态',
        align: 'center',
        minWidth: 50,
        slot: 'billStatus'
      },
      {
        prop: 'sourceFrom',
        label: '来源方式',
        align: 'center',
        minWidth: 50,
        slot: 'sourceFrom'
      },
      {
        prop: 'assignName',
        label: '分配人',
        align: 'center',
        minWidth: 80
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 打开商机跟进编辑弹窗 */
  const followEdit = (row) => {
    current.value = row ?? null;
    showFollowEdit.value = true;
  };

  /** 打开分配编辑弹窗 */
  const assignEdit = (row) => {
    current.value = row ?? null;
    showAssignEdit.value = true;
  };
  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getOpportunityPageByMe({ ...where, ...filters, ...pages });
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };

  const stopDone = () => {
    showStep.value = 1;
    reload();
  };

  /** 当前编辑数据 */
  const current = ref(null);

  const openEdit = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    opportunityRef.value?.initFormInfo(current);
  };

  const openQuery = async (row) => {
    current.value = row ?? null;
    showStep.value = 3;
    // 初始化流程定义详情
    await nextTick();
    opportunityRef.value?.initFormInfo(current);
  };

  //删除商机
  const getOpportunityDelete = (row) => {
    ElMessageBox.confirm('确认删除招商数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await getOpportunityDel(row);
        if (res) {
          EleMessage.success('删除成功');
          reload();
        } else {
          EleMessage.success('删除失败');
        }
      })
      .catch(() => {
        console.info('操作取消');
      });
  };
</script>
<style scoped>
  .title-clickable {
    cursor: pointer;
    color: #409eff;
    text-decoration: underline;
  }
</style>
