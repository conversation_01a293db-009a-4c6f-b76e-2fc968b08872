<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 客户详细 </span>
        </div>
      </template>
      <template #extra>
        <div class="flex items-center">
          <el-button
            v-if="form.billStatus == '20'"
            type="primary"
            class="ml-2"
            @click="followEdit"
            >跟进客户</el-button
          >
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form ref="formRef" :model="form" label-width="140px" @submit.prevent="">
    <ele-card header="客户信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="customerType">
            <el-radio-group v-model="form.customerType" disabled>
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_TYPE)"
                :key="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户编码" prop="customerNum">
            <el-input v-model="form.customerNum" disabled />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="form.customerName" disabled />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户简称" prop="customerShortName">
            <el-input v-model="form.customerShortName" disabled />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户来源" prop="source">
            <el-select
              clearable
              v-model="form.source"
              placeholder="请选择客户来源"
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户状态" prop="status">
            <el-select
              clearable
              v-model="form.status"
              placeholder="请选择客户状态"
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item
            :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
            prop="idNumber"
          >
            <el-input clearable v-model="form.idNumber" disabled />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="industry">
            <el-select
              clearable
              v-model="form.industry"
              placeholder=""
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              clearable
              v-model="form.contactName"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input
              clearable
              maxlength="11"
              v-model="form.contactNumber"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="公司邮箱" prop="contactEmail">
            <el-input
              clearable
              v-model="form.contactEmail"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input
              clearable
              v-model="form.contactAddress"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <!----->
    <ele-card header="工商信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="工商注册号" prop="businessRegistrationNum">
            <el-input
              clearable
              v-model="form.businessInfo.businessRegistrationNum"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经营状态" prop="businessStatus">
            <el-select
              clearable
              v-model="form.businessInfo.businessStatus"
              placeholder=""
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_RUN_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="统一社会信用代码" prop="unifiedCreditCode">
            <el-input
              clearable
              v-model="form.businessInfo.unifiedCreditCode"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="登记机关" prop="registrationAuthority">
            <el-input
              clearable
              v-model="form.businessInfo.registrationAuthority"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="组织机构代码" prop="orgCode">
            <el-input
              clearable
              v-model="form.businessInfo.orgCode"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="公司类型" prop="companyType">
            <el-select
              clearable
              v-model="form.businessInfo.companyType"
              placeholder=""
              class="ele-fluid"
              disabled
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_COMPANY_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="法人名" prop="legalName">
            <el-input
              clearable
              v-model="form.businessInfo.legalName"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="成立日期" prop="registrationDate">
            <el-date-picker
              type="datetime"
              v-model="form.businessInfo.registrationDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder=""
              class="ele-fluid"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="注册资金" prop="registrationCapital">
            <el-input
              clearable
              v-model="form.businessInfo.registrationCapital"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="营业结束日期" prop="businessEndDate">
            <el-date-picker
              type="datetime"
              v-model="form.businessInfo.businessEndDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder=""
              class="ele-fluid"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="注册地址" prop="registrationAddress">
            <el-input
              clearable
              v-model="form.businessInfo.registrationAddress"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经营范围" prop="businessScope">
            <el-input
              clearable
              v-model="form.businessInfo.businessScope"
              placeholder=""
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="5"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="跟进信息">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="followdatasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        cache-key="systemContractTable"
      >
        <template #intentionLevel="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_INTENTION_LEVEL"
            :model-value="row.intentionLevel"
          />
        </template>
        <template #followWay="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_FOLLOW_TYPE"
            :model-value="row.followWay"
          />
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 跟进弹窗 -->
    <CustomerFollowEdit
      v-model="showFollowEdit"
      :data="current"
      @done="followReload"
    />
  </el-form>
</template>
<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import CustomerFollowEdit from './CustomerFollowEdit.vue';
  import { useFormData } from '@/utils/use-form-data';
  import { dateFormatter } from '@/utils/formatTime';
  import { ref, computed } from 'vue';
  import { getCustomeDetial, getCustomerFollowPage } from '@/api/lease/attract';

  /** 微信账户管理 表单 */
  defineOptions({ name: 'CustomEditForm' });

  const props = defineProps({
    data: Object
  });

  /** 是否显示跟进编辑弹窗 */
  const showFollowEdit = ref(false);

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    customerType: 1,
    customerNum:  '',
    customerName: '',
    customerShortName:'',
    source: 1,
    status: 1,
    idNumber: '',
    industry: '',
    contactName: '',
    contactNumber: '',
    contactAddress: '',
    contactEmail: '',
    remark: '',
    billStatus: '',
    businessInfo: {
      businessRegistrationNum: '',
      businessStatus: 1,
      unifiedCreditCode: '',
      registrationAuthority: '',
      orgCode: '',
      companyType: '',
      legalName: '',
      registrationDate: '',
      registrationCapital: '',
      registrationAddress: '',
      businessEndDate: '',
      businessScope: ''
    }
  });
  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };
  const initFormInfo = async (row) => {
    if (row) {
      assignFields(row);
    } else {
      resetFields();
    }
    await loadData();
  };
  defineExpose({ initFormInfo });

  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      try {
        // 假设有一个 API 可以获取最新数据
        const response = await getCustomeDetial({ id: props.data.id });
        if (response) {
          // 将时间戳转换为日期格式
          const formattedResponse = {
            ...response,
            businessInfo: {
              ...response.businessInfo,
              registrationDate: response.businessInfo.registrationDate
                ? new Date(response.businessInfo.registrationDate)
                    .toISOString()
                    .split('T')[0]
                : null, // 如果为空，保持为空
              businessEndDate: response.businessInfo.businessEndDate
                ? new Date(response.businessInfo.businessEndDate)
                    .toISOString()
                    .split('T')[0]
                : null // 如果为空，保持为空
            }
          };
          assignFields(formattedResponse);
          isUpdate.value = true;
        }
      } catch (error) {
        console.error('获取客户详情失败', error);
      }
    } else {
      resetFields();
      isUpdate.value = false;
    }
  };

  /** 表格实例 */
  const tableRef = ref(null);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'intentionLevel',
        label: '意向水平',
        align: 'center',
        minWidth: 50,
        slot: 'intentionLevel'
      },
      {
        prop: 'followWay',
        label: '跟进方式',
        align: 'center',
        minWidth: 50,
        slot: 'followWay'
      },
      {
        prop: 'followTime',
        label: '跟进时间',
        align: 'center',
        minWidth: 50,
        formatter: dateFormatter
      },
      {
        prop: 'followUserName',
        label: '跟进人',
        width: 50,
        align: 'center'
      },
      {
        prop: 'followContent',
        label: '跟进内容',
        width: 140,
        align: 'center'
      },
      {
        prop: 'createTime',
        label: '跟进记录时间	',
        align: 'center',
        minWidth: 50,
        formatter: dateFormatter
      }
    ];
  });

  /** 打开商机跟进编辑弹窗 */
  const followEdit = async () => {
    current.value = form ?? null;
    showFollowEdit.value = true;
  };

  /** 搜索 */
  const followReload = () => {
    if (tableRef.value) {
      tableRef.value.reload();
    }
  };
  /** 表格数据源 */
  const followdatasource = ({ pages, where, filters }) => {
    let customerId = form.id !== undefined ? form.id : props.data.id;
    return getCustomerFollowPage({
      ...where,
      ...filters,
      ...pages,
      customerId: customerId
    });
  };
</script>
