<template>
  <ele-page plain hide-footer :multi-card="false">
    <ele-page>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        @submit.prevent=""
      >
        <ele-card header="客户信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户类型" prop="customerType">
                <el-radio-group v-model="form.customerType">
                  <el-radio
                    v-for="dict in getIntDictOptions(
                      DICT_TYPE.CO_CUSTOMER_TYPE
                    )"
                    :key="dict.label"
                    :value="dict.value"
                  >
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户名称" prop="customerName">
                <el-input v-model="form.customerName" />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户来源" prop="source">
                <el-select
                  clearable
                  v-model="form.source"
                  placeholder="请选择客户来源"
                  class="ele-fluid"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(
                      DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE
                    )"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="客户状态" prop="status">
                <el-select
                  clearable
                  v-model="form.status"
                  placeholder="请选择客户状态"
                  class="ele-fluid"
                >
                <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_STATUS)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item
                :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
                prop="idNumber"
              >
                <el-input
                  clearable
                  v-model="form.idNumber"
                  :placeholder="`${form.customerType == 1 ? '请输入纳税人识别号' : '请输入身份证号'}`"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="所属行业" prop="industry">
                <el-select
                  clearable
                  v-model="form.industry"
                  placeholder="请选择所属行业"
                  class="ele-fluid"
                >
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系人姓名" prop="contactName">
                <el-input
                  clearable
                  v-model="form.contactName"
                  placeholder="请输入联系人姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系电话" prop="contactNumber">
                <el-input
                  clearable
                  maxlength="11"
                  v-model="form.contactNumber"
                  placeholder="请输入联系电话"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="公司邮箱" prop="contactEmail">
                <el-input
                  clearable
                  v-model="form.contactEmail"
                  placeholder="请输入公司邮箱"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="联系地址" prop="contactAddress">
                <el-input
                  clearable
                  v-model="form.contactAddress"
                  placeholder="请输入联系地址"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
        <!----->
        <ele-card header="工商信息">
          <el-row :gutter="16">
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="工商注册号" prop="businessRegistrationNum">
                <el-input
                  clearable
                  v-model="form.businessInfo.businessRegistrationNum"
                  placeholder="请输入工商注册号"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="经营状态" prop="businessStatus">
                <el-select
                  clearable
                  v-model="form.businessInfo.businessStatus"
                  placeholder="请选择客户状态"
                  class="ele-fluid"
                >
                <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CO_RUN_STATUS)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="统一社会信用代码" prop="unifiedCreditCode">
                <el-input
                  clearable
                  v-model="form.businessInfo.unifiedCreditCode"
                  placeholder="请输入统一社会信用代码"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="登记机关" prop="registrationAuthority">
                <el-input
                  clearable
                  v-model="form.businessInfo.registrationAuthority"
                  placeholder="请输入登记机关"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="组织机构代码" prop="orgCode">
                <el-input
                  clearable
                  v-model="form.businessInfo.orgCode"
                  placeholder="请输入组织机构代码"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="公司类型" prop="companyType">
                <el-select
                  clearable
                  v-model="form.businessInfo.companyType"
                  placeholder="请选择客户状态"
                  class="ele-fluid"
                >
                <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.CO_COMPANY_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="法人名" prop="legalName">
                <el-input
                  clearable
                  v-model="form.businessInfo.legalName"
                  placeholder="请输入法人名"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="成立日期" prop="registrationDate">
                <el-date-picker
                  unlink-panels
                  type="daterange"
                  v-model="form.businessInfo.registrationDate"
                  range-separator="-"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="ele-fluid"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="注册资金" prop="registrationCapital">
                <el-input
                  clearable
                  v-model="form.businessInfo.registrationCapital"
                  placeholder="请输入注册资金"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="营业结束日期" prop="businessEndDate">
                <el-date-picker
                  unlink-panels
                  type="daterange"
                  v-model="form.businessInfo.businessEndDate"
                  range-separator="-"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="ele-fluid"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="注册地址" prop="registrationAddress">
                <el-input
                  clearable
                  v-model="form.businessInfo.registrationAddress"
                  placeholder="请输入注册地址"
                />
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="12" :sm="12" :xs="24">
              <el-form-item label="经营范围" prop="businessScope">
                <el-input
                  clearable
                  v-model="form.businessInfo.businessScope"
                  placeholder="请输入经营范围"
                  maxlength="300"
                  show-word-limit
                  type="textarea"
                  :rows="5"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </ele-card>
      </el-form>
    </ele-page>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button type="primary" :loading="loading" @click="submit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { ref, reactive } from 'vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloseCircleOutlined, PlusOutlined } from '@/components/icons';
  import { useFormData } from '@/utils/use-form-data';
  import type { UserItem } from '@/api/example/model';
  import { listAddedUsers } from '@/api/example';
  import {
    getCustomerAdd,
    getCustomerEdit,
    getCustomeDetial
  } from '@/api/lease/attract';
  //路由参数
  const route = useRoute();
  const router = useRouter();
  /** 加载状态 */
  const loading = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);
  /** 表单数据 */
  const form = ref({
    customerType: 1,
    customerName: '',
    source: 1,
    status: 1,
    idNumber: '',
    industry: '',
    contactName: '',
    contactNumber: '',
    contactAddress: '',
    contactEmail: '',
    remark: '',
    businessInfo: {
      businessRegistrationNum: '',
      businessStatus: 1,
      unifiedCreditCode: '',
      registrationAuthority: '',
      orgCode: '',
      companyType: '',
      legalName: '',
      registrationDate: '',
      registrationCapital: '',
      registrationAddress: '',
      businessEndDate: '',
      businessScope: ''
    }
  });
  /** 表单验证规则 */
  const rules = reactive<FormRules>({
    customerType: [
      {
        required: true,
        message: '请选择客户类型',
        trigger: 'blur'
      }
    ],
    customerName: [
      {
        required: true,
        message: '请输入客户名称',
        trigger: 'blur'
      }
    ],
    source: [
      {
        required: true,
        message: '请选择客户来源',
        trigger: 'blur'
      }
    ],
    status: [
      {
        required: true,
        message: '请选择客户状态',
        trigger: 'blur'
      }
    ],
    contactName: [
      {
        required: true,
        message: '请输入联系人姓名',
        trigger: 'blur'
      }
    ],
    contactNumber: [
      {
        required: true,
        message: '请输入联系人电话',
        trigger: 'blur'
      }
    ]
  });

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  onMounted(async () => {
    if (route.query.id) {
      form.value = await getCustomeDetial({ id: route.query.id });
    }
  });
  /** 表单提交 */
  const submit = () => {
    formRef.value?.validate?.(async (valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsg.value = ` 共有 ${errors} 项校验不通过`;
        return;
      }
      validMsg.value = '';
      loading.value = true;
      loading.value = false;
      let res = null;
      if (route.query.type == '1') {
        res = await getCustomerAdd(form.value);
      } else {
        res = await getCustomerEdit(form.value);
      }
      if (res) {
        EleMessage.success('提交成功');
        router.go(-1);
      }
      // setTimeout(() => {
      //   loading.value = false;
      //   EleMessage.success('提交成功');
      //   resetFields();
      // }, 1000);
    });
  };
</script>

<script lang="ts">
  export default {
    name: 'FormAdvanced'
  };
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
  }
</style>
