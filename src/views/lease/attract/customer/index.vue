<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!-- <role-search @search="reload" /> -->
    <ele-card
      v-if="showStep == 1"
      flex-table
      :body-style="{ paddingTop: '8px' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        cache-key="systemCustomerMangerTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit(null)"
          >
            添加客户
          </el-button>
        </template>
        <template #customerName="{ row }">
          <span class="title-clickable" @click="openQuery(row)">
            {{ row.customerName }}
          </span>
        </template>
        <template #customerType="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CUSTOMER_TYPE"
            :model-value="row.customerType"
          />
        </template>
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CUSTOMER_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #industry="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_SECTOR"
            :model-value="row.industry"
          />
        </template>
        <template #synStatus="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.CO_CONTRACT_PAY_SYN_STATUS"
            :model-value="row.synStatus"
          />
        </template>
        <template #action="{ row }">
          <el-button type="primary" link @click="followEdit(row)">
            跟进
          </el-button>
          <el-button
            v-if="!row.synStatus || row.synStatus === 1 || row.synStatus === 5"
            type="primary"
            link
            @click="synCustomer(row)"
          >
            同步
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <CustomEditForm
      ref="customRef"
      v-if="showStep == 2"
      :data="current"
      @done="stopDone"
    />
    <CustomQueryForm
      ref="customRef"
      v-if="showStep == 3"
      :data="current"
      @done="stopDone"
    />
    <!-- 跟进弹窗 -->
    <CustomerFollowEditForm
      v-model="showFollowEdit"
      :data="current"
      @done="stopDone"
    />
  </ele-page>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DICT_TYPE } from '@/utils/dict';
  import {
    getCustomerPage,
    getCustomeDel,
    getCustomerReturnBill,
    synCustomerInterface
  } from '@/api/lease/attract';
  import CustomEditForm from './CustomEditForm.vue';
  import CustomQueryForm from './CustomQueryForm.vue';
  import CustomerFollowEditForm from './CustomerFollowEdit.vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const customRef = ref(null);
  /** 表格实例 */
  const tableRef = ref(null);
  const showStep = ref(1);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'customerNum',
        label: '客户编码',
        align: 'center',
        minWidth: 150
      },
      {
        prop: 'customerName',
        label: '客户名称',
        align: 'center',
        minWidth: 150,
        slot: 'customerName'
      },
      {
        prop: 'customerShortName',
        label: '客户简称',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'customerType',
        label: '客户类型',
        align: 'center',
        minWidth: 120,
        slot: 'customerType'
      },
      {
        prop: 'status',
        label: '客户状态',
        align: 'center',
        minWidth: 120,
        slot: 'status'
      },
      {
        prop: 'industry',
        label: '所属行业',
        width: 120,
        align: 'center',
        slot: 'industry'
      },
      {
        prop: 'createName',
        label: '创建人姓名',
        width: 120,
        align: 'center'
      },
      {
        prop: 'unfollowedDays',
        label: '未跟进天数',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'assignName',
        label: '分配人',
        align: 'center',
        minWidth: 80
      },
      {
        prop: 'synStatus',
        label: '同步状态',
        align: 'center',
        minWidth: 80,
        slot: 'synStatus'
      },
      {
        prop: 'synMessage',
        label: '同步消息',
        align: 'center',
        minWidth: 150
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 180,
        align: 'center',
        fixed: 'right',
        slot: 'action',
        hideInPrint: true,
        hideInExport: true
      }
    ];
  });
  /** 表格选中数据 */
  const selections = ref([]);
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return getCustomerPage({ ...where, ...filters, ...pages });
  };
  /** 当前编辑数据 */
  const current = ref(null);
  const openEdit = async (row) => {
    current.value = row ?? null;
    showStep.value = 2;
    // 初始化流程定义详情
    await nextTick();
    customRef.value?.initFormInfo(current);
  };
  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };
  const stopDone = () => {
    showStep.value = 1;
    reload();
  };
  //同步客户
  const synCustomer = (row) => {
    if (!row.synStatus && row.synStatus !== 1 && row.synStatus !== 5) {
      EleMessage.warning('当前状态不允许同步');
      return;
    }
    ElMessageBox.confirm('确认同步客户数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await synCustomerInterface(row);
        if (res) {
          EleMessage.success('调用成功');
          reload();
        } else {
          EleMessage.error('调用失败');
        }
      })
      .catch(() => console.info('操作取消'));
  };

  //删除客户
  const getCustomeDelete = (row) => {
    ElMessageBox.confirm('确认删客户数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await getCustomeDel(row);
        if (res) {
          EleMessage.success('删除成功');
          reload();
        } else {
          EleMessage.success('删除失败');
        }
      })
      .catch(() => console.info('操作取消'));
  };
  const openQuery = async (row) => {
    current.value = row ?? null;
    showStep.value = 3;
    // 初始化流程定义详情
    await nextTick();
    customRef.value?.initFormInfo(current);
  };

  /** 是否显示跟进编辑弹窗 */
  const showFollowEdit = ref(false);

  /** 打开商机跟进编辑弹窗 */
  const followEdit = (row) => {
    current.value = row ?? null;
    showFollowEdit.value = true;
  };

  //退回客户
  const returnBill = (row) => {
    ElMessageBox.confirm('确认退回客户数据吗？', '提示', {
      confirmButtonText: '确 认',
      cancelButtonText: '取 消'
    })
      .then(async () => {
        let res = await getCustomerReturnBill(row);
        if (res) {
          EleMessage.success('退回成功');
          reload();
        } else {
          EleMessage.success('退回失败');
        }
      })
      .catch(() => {
        console.info('操作取消');
      });
  };
</script>
<style scoped>
  .title-clickable {
    cursor: pointer;
    color: #409eff;
    text-decoration: underline;
  }
</style>
