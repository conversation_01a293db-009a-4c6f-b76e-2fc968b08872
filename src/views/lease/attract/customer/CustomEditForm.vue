<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 客户详细 </span>
        </div>
      </template>
      <template #extra>
        <div class="flex items-center">
          <el-button type="primary" class="ml-2" @click="save">保存</el-button>
          <el-button type="primary" class="ml-2" @click="submmit"
            >提交</el-button
          >
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="140px"
    v-loading="loading"
    @submit.prevent=""
  >
    <ele-card header="客户信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="customerType">
            <el-radio-group v-model="form.customerType">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_TYPE)"
                :key="dict.label"
                :value="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户编码" prop="customerNum">
            <el-input
              v-model="form.customerNum"
              disabled
              placeholder="自动生成"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="form.customerName" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户简称" prop="customerShortName">
            <el-input v-model="form.customerShortName" />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户来源" prop="source">
            <el-select
              clearable
              v-model="form.source"
              placeholder="请选择客户来源"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.BUSINESS_OPPORTUNITY_SOURCE
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户状态" prop="status">
            <el-select
              clearable
              v-model="form.status"
              placeholder="请选择客户状态"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_CUSTOMER_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item
            :label="form.customerType == 1 ? '纳税人识别号' : '身份证'"
            prop="idNumber"
          >
            <el-input
              clearable
              v-model="form.idNumber"
              :placeholder="`${form.customerType == 1 ? '请输入纳税人识别号' : '请输入身份证号'}`"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="industry">
            <el-select
              clearable
              v-model="form.industry"
              placeholder="请选择所属行业"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系人姓名" prop="contactName">
            <el-input
              clearable
              v-model="form.contactName"
              placeholder="请输入联系人姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="contactNumber">
            <el-input
              clearable
              maxlength="11"
              v-model="form.contactNumber"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="公司邮箱" prop="contactEmail">
            <el-input
              clearable
              v-model="form.contactEmail"
              placeholder="请输入公司邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input
              clearable
              v-model="form.contactAddress"
              placeholder="请输入联系地址"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <!----->
    <ele-card header="工商信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="工商注册号" prop="businessRegistrationNum">
            <el-input
              clearable
              v-model="form.businessInfo.businessRegistrationNum"
              placeholder="请输入工商注册号"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经营状态" prop="businessStatus">
            <el-select
              clearable
              v-model="form.businessInfo.businessStatus"
              placeholder="请选择客户状态"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_RUN_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="统一社会信用代码" prop="unifiedCreditCode">
            <el-input
              clearable
              v-model="form.businessInfo.unifiedCreditCode"
              placeholder="请输入统一社会信用代码"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="登记机关" prop="registrationAuthority">
            <el-input
              clearable
              v-model="form.businessInfo.registrationAuthority"
              placeholder="请输入登记机关"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="组织机构代码" prop="orgCode">
            <el-input
              clearable
              v-model="form.businessInfo.orgCode"
              placeholder="请输入组织机构代码"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="公司类型" prop="companyType">
            <el-select
              clearable
              v-model="form.businessInfo.companyType"
              placeholder="请选择客户状态"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.CO_COMPANY_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="法人名" prop="legalName">
            <el-input
              clearable
              v-model="form.businessInfo.legalName"
              placeholder="请输入法人名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="成立日期" prop="registrationDate">
            <el-date-picker
              type="datetime"
              v-model="form.businessInfo.registrationDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="请选择成立日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="注册资金" prop="registrationCapital">
            <el-input
              clearable
              v-model="form.businessInfo.registrationCapital"
              placeholder="请输入注册资金"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="营业结束日期" prop="businessEndDate">
            <el-date-picker
              type="datetime"
              v-model="form.businessInfo.businessEndDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="该选项不填即为长期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="注册地址" prop="registrationAddress">
            <el-input
              clearable
              v-model="form.businessInfo.registrationAddress"
              placeholder="请输入注册地址"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="经营范围" prop="businessScope">
            <el-input
              clearable
              v-model="form.businessInfo.businessScope"
              placeholder="请输入经营范围"
              maxlength="300"
              show-word-limit
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
  </el-form>
</template>
<script setup lang="ts">
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';

  import { useFormData } from '@/utils/use-form-data';
  import {
    getCustomeDetial,
    getCustomerAdd,
    getCustomerEdit,
    getCustomerSubmmit
  } from '@/api/lease/attract';

  /** 微信账户管理 表单 */
  defineOptions({ name: 'CustomEditForm' });

  const props = defineProps({
    data: Object
  });

  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗

  const emit = defineEmits(['done']);

  const title = ref(''); // 弹窗的标题
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    customerType: 1,
    customerNum:'',
    customerName: '',
    customerShortName: '',
    source: 1,
    status: 1,
    idNumber: '',
    industry: '',
    contactName: '',
    contactNumber: '',
    contactAddress: '',
    contactEmail: '',
    remark: '',
    businessInfo: {
      businessRegistrationNum: '',
      businessStatus: 1,
      unifiedCreditCode: '',
      registrationAuthority: '',
      orgCode: '',
      companyType: '',
      legalName: '',
      registrationDate: '',
      registrationCapital: '',
      registrationAddress: '',
      businessEndDate: '',
      businessScope: ''
    }
  });

  /** 表单验证规则 */
  const rules = reactive({
    customerType: [
      {
        required: true,
        message: '请选择客户类型',
        trigger: 'blur'
      }
    ],
    customerName: [
      {
        required: true,
        message: '请输入客户名称',
        trigger: 'blur'
      },
      {
        pattern: /^(?! )[a-zA-Z0-9\u4e00-\u9fa5()\-_@:/ ]+(?<! )$/, // 修改后的正则表达式：不允许空格在首位和末尾
        message:
          '客户名称只能包含汉字、英文、数字、括号、空格、-_@:/，且空格不能在首位或末尾',
        trigger: 'blur'
      }
    ],
    customerShortName: [
      {
        required: true,
        message: '请输入客户简称',
        trigger: 'blur'
      },
      {
        pattern: /^(?! )[a-zA-Z0-9\u4e00-\u9fa5()\-_@:/ ]+(?<! )$/, // 修改后的正则表达式：不允许空格在首位和末尾
        message:
          '客户简称只能包含汉字、英文、数字、括号、空格、-_@:/，且空格不能在首位或末尾',
        trigger: 'blur'
      }
    ],
    source: [
      {
        required: true,
        message: '请选择客户来源',
        trigger: 'blur'
      }
    ],
    status: [
      {
        required: true,
        message: '请选择客户状态',
        trigger: 'blur'
      }
    ],
    idNumber: [
      {
        required: true,
        message: '请输入统一社会信用码或者身份证号',
        trigger: 'blur'
      }
    ],
    contactName: [
      {
        required: true,
        message: '请输入联系人姓名',
        trigger: 'blur'
      }
    ],
    contactNumber: [
      {
        required: true,
        message: '请输入联系人电话',
        trigger: 'blur'
      }
    ]
  });

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };

  const save = async () => {
    if (!formRef.value) return;
    const valid = await formRef.value.validate();
    if (!valid) return;
    // 提交请求
    loading.value = true;
    try {
      // 将日期转换为时间戳
      const formData = {
        ...form,
        businessInfo: {
          ...form.businessInfo,
          registrationDate: new Date(
            form.businessInfo.registrationDate
          ).getTime(),
          businessEndDate: new Date(form.businessInfo.businessEndDate).getTime()
        }
      };

      if (!isUpdate.value) {
        const response = await getCustomerAdd(formData); // 调用 getCustomerAdd 方法提交数据
        if (response) {
          assignFields(response);
          isUpdate.value = true;
        }
        message.success(t('common.createSuccess'));
      } else {
        await getCustomerEdit(formData);
        message.success(t('common.updateSuccess'));
      }
      visible.value = false;
      // 发送操作成功的事件
      //emit('done');
    } finally {
      loading.value = false;
    }
  };

  const submmit = async () => {
    // 提交请求
    loading.value = true;
    if (!formRef) {
      loading.value = false;
      return;
    }
    const valid = await formRef.value.validate();
    if (!valid) {
      loading.value = false;
      return;
    }
    try {
      if (!isUpdate.value) {
        const response = await getCustomerAdd(form); // 调用 getOpportunityAdd 方法提交数据
        assignFields(response);
      } else {
        await getCustomerEdit(form);
      }
      await getCustomerSubmmit(form);
      //保存完成后，提交数据
      visible.value = false;
      // 发送操作成功的事件
      emit('done');
    } finally {
      loading.value = false;
    }
  };
  const initFormInfo = async (row) => {
    if (row) {
      assignFields(row);
    } else {
      // 新增：从路由参数中获取商机数据并初始化表单
      if (route.query.data) {
        const formData = JSON.parse(route.query.data);
        assignFields({
          customerType: formData.customerType,
          customerNum:  formData.customerNum,
          customerName: formData.customerName,
          customerShortName: formData.customerShortName,
          idNumber: formData.idNumber,
          industry: formData.industry,
          contactName: formData.contactName,
          contactNumber: formData.contactNumber,
          contactAddress: formData.contactAddress,
          contactEmail: formData.contactEmail,
          remark: formData.remark,
          businessInfo: {
            businessRegistrationNum: '',
            businessStatus: 1,
            unifiedCreditCode: '',
            registrationAuthority: '',
            orgCode: '',
            companyType: '',
            legalName: '',
            registrationDate: '',
            registrationCapital: '',
            registrationAddress: '',
            businessEndDate: '',
            businessScope: ''
          }
        });
      } else {
        resetFields();
      }
    }
    await loadData();
  };
  defineExpose({ initFormInfo });

  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      try {
        // 假设有一个 API 可以获取最新数据
        const response = await getCustomeDetial({ id: props.data.id });
        if (response) {
          // 将时间戳转换为日期格式
          const formattedResponse = {
            ...response,
            businessInfo: {
              ...response.businessInfo,
              registrationDate: response.businessInfo.registrationDate
                ? new Date(response.businessInfo.registrationDate)
                    .toISOString()
                    .split('T')[0]
                : null, // 如果为空，保持为空
              businessEndDate: response.businessInfo.businessEndDate
                ? new Date(response.businessInfo.businessEndDate)
                    .toISOString()
                    .split('T')[0]
                : null // 如果为空，保持为空
            }
          };
          assignFields(formattedResponse);
          isUpdate.value = true;
        }
      } catch (error) {
        console.error('获取客户详情失败', error);
      }
    } else {
      resetFields();
      isUpdate.value = false;
    }
  };
</script>
