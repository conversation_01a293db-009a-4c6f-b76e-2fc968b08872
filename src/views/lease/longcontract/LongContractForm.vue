<template>
  <ele-card>
    <el-page-header @back="cancle">
      <template #content>
        <div class="flex items-center">
          <span class="text-small font-600 mr-3"> 合同查看 </span>
        </div>
      </template>
    </el-page-header>
  </ele-card>
  <el-form ref="formRef" :model="form" label-width="140px" @submit.prevent="">
    <ele-card header="基础信息">
      <el-row :gutter="16">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同编号" prop="code">
            <el-input maxlength="40" disabled clearable v-model="form.code" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同名称" prop="name">
            <el-input maxlength="40" clearable disabled v-model="form.name" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="合同签订日期" prop="signingDate">
            <el-date-picker
              type="date"
              disabled
              v-model="form.signingDate"
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="经办人" prop="handlerName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.handlerName"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="客户信息">
      <el-row :gutter="16">
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户类型" prop="customerType">
            <el-radio-group v-model="form.customerType" disabled>
              <el-radio :value="1" label="企业" />
              <el-radio :value="2" label="个人" />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="客户名称" prop="customerName">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.customerName"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="所属行业" prop="customerIndustry">
            <el-select
              disabled
              clearable
              placeholder=""
              v-model="form.customerIndustry"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.CO_SECTOR)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="身份证" prop="customerIdcard">
            <el-input disabled clearable v-model="form.customerIdcard" />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="联系电话" prop="customerPhone">
            <el-input
              clearable
              disabled
              maxlength="11"
              v-model="form.customerPhone"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="邮箱" prop="customerEmail">
            <el-input clearable disabled v-model="form.customerEmail" />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="房产信息">
      <el-row :gutter="0">
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="0" prop="parkInfo">
            <el-input
              disabled
              clearable
              v-model="form.longContractHouses.parkInfo"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="0" prop="buildingInfo">
            <el-input
              disabled
              clearable
              v-model="form.longContractHouses.buildingInfo"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="0" prop="floorInfo">
            <el-input
              disabled
              clearable
              v-model="form.longContractHouses.floorInfo"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="0" prop="roomNo">
            <el-input
              disabled
              clearable
              v-model="form.longContractHouses.roomNo"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item prop="price" label="价格">
            <el-input
              maxlength="40"
              disabled
              clearable
              v-model="form.longContractHouses.price"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card header="租赁费用">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="租金方案" prop="rentalPlan">
            <el-select
              clearable
              disabled
              v-model="form.rentalPlan"
              class="ele-fluid"
            >
              <el-option
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.CO_CONTRACT_RENTAL_PLAN
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item prop="downPaymentAmount" label="首付金额">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.downPaymentAmount"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item prop="penaltyAmtRatio" label="滞纳金比例(%)">
            <el-input
              maxlength="40"
              clearable
              disabled
              v-model="form.penaltyAmtRatio"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card v-if="form.status === 5" header="退租信息">
      <el-row :gutter="16">
        <el-col :lg="8" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租日期" prop="throwLeaseDate">
            <el-date-picker
              type="date"
              v-model="form.throwLease.throwLeaseDate"
              disabled
              value-format="x"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租原因" prop="reason">
            <el-radio-group v-model="form.throwLease.reason" disabled>
              <el-radio
                v-for="dict in getIntDictOptions(
                  DICT_TYPE.CO_CONTRACT_THROWLEASE_REASON
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :lg="24" :md="12" :sm="12" :xs="24">
          <el-form-item label="退租说明" prop="explanation">
            <el-input
              clearable
              disabled
              v-model="form.throwLease.explanation"
              maxlength="500"
              show-word-limit
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </ele-card>
    <ele-card style="min-height: 350px">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="">
        <el-tab-pane label="收款信息" name="0">
          <div style="overflow: auto">
            <el-table
              :data="form.longContractPaymentPlans"
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
            >
              <el-table-column label="开始日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`longContractPaymentPlans.${$index}.startDate`"
                  >
                    <el-date-picker
                      type="date"
                      disabled
                      v-model="row.startDate"
                      value-format="x"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="结束日期" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`longContractPaymentPlans.${$index}.endDate`"
                  >
                    <el-date-picker
                      type="date"
                      disabled
                      v-model="row.endDate"
                      value-format="x"
                      class="date-time-o"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="收款日" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`longContractPaymentPlans.${$index}.paymentDate`"
                  >
                    <el-date-picker
                      type="date"
                      disabled
                      v-model="row.paymentDate"
                      value-format="x"
                      class="ele-fluid"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="固定租金(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`longContractPaymentPlans.${$index}.fixedRent`"
                  >
                    <el-input-number
                      :min="0"
                      disabled
                      :precision="2"
                      v-model="row.fixedRent"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="浮动租金(元)" width="250">
                <template #default="{ row, $index }">
                  <el-form-item
                    class="input-margin-left"
                    :prop="`longContractPaymentPlans.${$index}.floatingRent`"
                  >
                    <el-input-number
                      :min="0"
                      disabled
                      :precision="2"
                      v-model="row.floatingRent"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="附件信息" name="1">
          <div style="overflow: auto">
            <el-table
              :header-cell-style="{
                color: '#1991eb',
                backgroundColor: '#eaf5fd'
              }"
              :data="form.longContractFiles"
            >
              <el-table-column label="文件名" prop="name"></el-table-column>
              <el-table-column label="大小" prop="size">
                <template #default="scope">
                  <span>{{ scope.row.size }} KB</span>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="uploadTime">
                <template #default="scope">
                  <span>{{ formatDate2(scope.row.uploadTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-link
                    type="primary"
                    :underline="false"
                    download
                    :href="scope.row.url"
                  >
                    下载
                  </el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </ele-card>
  </el-form>
</template>

<script lang="ts" setup>
  import {
    DICT_TYPE,
    getIntDictOptions,
    getStrDictOptions
  } from '@/utils/dict';
  import { ref } from 'vue';
  import type { FormInstance } from 'element-plus';
  import { useFormData } from '@/utils/use-form-data';
  import { formatDate2 } from '@/utils/formatTime';
  import { getLongContractDetail } from '@/api/lease/longcontract/index';
  const activeName = ref('0');
  const props = defineProps({
    data: Object
  });

  const showImport = ref(false);

  const importData = () => {
    showImport.value = true;
  };

  /** 加载状态 */
  const loading = ref(false);
  const emit = defineEmits(['done']);
  const isUpdate = ref(false);
  /** 表单实例 */
  const formRef = ref<FormInstance | null>(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: undefined,
    type: undefined,
    code: undefined,
    name: undefined,
    status: undefined,
    approveStatus: undefined,
    signingDate: undefined,
    signingChannel: undefined,
    handler: undefined,
    handlerName: undefined,
    partaId: undefined,
    partaName: undefined,
    customerId: undefined,
    customerType: 2,
    customerName: undefined,
    customerIndustry: undefined,
    customerIdcard: undefined,
    customerPhone: undefined,
    customerEmail: undefined,
    houseId: undefined,
    rentalPlan: undefined,
    downPaymentAmount: undefined,
    penaltyAmtRatio: undefined,
    remark: undefined,
    atributeVarchar1: undefined,
    atributeVarchar2: undefined,
    atributeVarchar3: undefined,
    atributeVarchar4: undefined,
    atributeVarchar5: undefined,
    longContractFiles: [],
    longContractHouses: {
      id: void 0,
      parkInfo: undefined,
      parkCode: undefined,
      buildingInfo: undefined,
      buildingCode: undefined,
      floorInfo: undefined,
      floorCode: undefined,
      roomNo: undefined,
      area: undefined,
      houseId: undefined,
      roomNum: undefined,
      price: undefined
    },
    longContractPaymentPlans: [],
    throwLease: {
      throwLeaseDate: undefined,
      reason: -1,
      explanation: undefined
    }
  });
  /** 表单验证失败提示信息 */
  const validMsg = ref('');

  /** 关闭弹窗 */
  const cancle = () => {
    emit('done');
  };
  const initFormInfo = async () => {
    resetFields();
    await loadData();
  };
  /** 弹窗打开事件 */
  const loadData = async () => {
    if (props.data) {
      isUpdate.value = true;
      loading.value = true;
      try {
        // 获取最新数据
        const response = await getLongContractDetail(props.data.id);
        if (response) {
          assignFields(response);
        }
      } catch (error) {
        console.error('获取合同详情失败', error);
      } finally {
        loading.value = false;
      }
    } else {
      isUpdate.value = false;
    }
  };
  defineExpose({ initFormInfo });
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }
  .file-box {
    width: 100%;
    text-align: right;
    margin-bottom: 10px;
  }
  .file-flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .flex-box {
      display: flex;
      align-items: center;
    }
  }
  .formdata-flex {
    display: flex;
    align-items: left;
    .ele-fluid {
      width: 175px;
      margin-right: 10px;
    }
  }
  .input-wh {
    width: 160px;
    margin-right: 10px;
  }
  .date-time-o {
    width: 200px;
  }
  .input-margin-left {
    :deep(.el-form-item__content) {
      margin-left: 0px !important;
    }
  }
</style>
