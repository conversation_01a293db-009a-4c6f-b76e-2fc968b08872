<template>
  <ele-modal
    :width="460"
    title="模版导入"
    :body-style="{ paddingTop: '8px' }"
    v-model="visible"
    @open="handleOpen"
  >
    <div v-loading="loading" class="import-upload">
      <el-upload
        drag
        ref="uploadRef"
        v-model:file-list="fileList"
        accept=".xls,.xlsx"
        :auto-upload="false"
        :disabled="formLoading"
        :headers="uploadHeaders"
        :limit="1"
        :on-error="submitFormError"
        :on-exceed="handleExceed"
        :on-success="submitFormSuccess"
        :action="
          importUrl +
          '?updateSupport=' +
          updateSupport +
          '&databaseId=' +
          databaseId
        "
      >
        <ele-text
          type="primary"
          :icon="CloudUploadOutlined"
          :icon-props="{ size: 52 }"
          style="margin-bottom: 10px"
        />
        <ele-text type="placeholder">将文件拖到此处, 或点击上传</ele-text>
      </el-upload>
    </div>
    <div style="display: flex; align-items: center">
      <ele-text size="sm" type="secondary" style="line-height: 17px; flex: 1">
        <span style="padding-right: 8px">只能上传 xls、xlsx 文件,</span>
        <el-link
          type="primary"
          :underline="false"
          style="font-size: inherit; line-height: inherit; vertical-align: 0"
          @click="onDownload"
        >
          下载模板
        </el-link>
      </ele-text>
      <!-- <el-checkbox v-model="updateSupport" style="height: 18px; flex-shrink: 0">
        更新已存在员工
      </el-checkbox> -->
    </div>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm"
        >确 定</el-button
      >
      <el-button @click="visible = false">取 消</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, h } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { CloudUploadOutlined } from '@/components/icons';
  import * as LongContractApi from '@/api/lease/longcontract';
  //import * as MsgDatabaseDetailApi from '@/api/system/msgdatabase/detail';
  import download from '@/utils/download';
  import { getAccessToken, getTenantId, getSsoAccessToken } from '@/utils/auth';
  const message = useMessage(); // 消息弹窗

  const formLoading = ref(false); // 表单的加载中

  const emits = defineEmits(['done']);
  const importUrl =
    import.meta.env.VITE_API_URL + '/lease/long-contract/import';
  const uploadRef = ref();
  const uploadHeaders = ref(); // 上传 Header 头
  const fileList = ref([]); // 文件列表
  const updateSupport = ref(0); // 是否更新已经存在的用户数据
  const props = defineProps({
    databaseId: Number
  });
  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });
  const databaseId = ref(0);
  /** 导入请求状态 */
  const loading = ref(false);
  /** 上传错误提示 */
  const submitFormError = () => {
    message.error('上传失败，请您重新上传！');
    formLoading.value = false;
  };
  /** 提交表单 */
  const submitForm = async () => {
    if (fileList.value.length == 0) {
      message.error('请上传文件');
      return;
    }
    // 提交请求
    uploadHeaders.value = {
      Authorization: 'Bearer ' + getAccessToken(),
      'tenant-id': getTenantId(),
      'Wehealplus-Sso-Token': getSsoAccessToken()
    };
    formLoading.value = true;
    uploadRef.value.submit();
  };
  const submitFormSuccess = (response) => {
    if (response.code !== 0) {
      message.error(response.msg);
      formLoading.value = false;
      return;
    }
    // 拼接提示语
    const data = response.data;
    formLoading.value = false;
    visible.value = false;
    // 发送操作成功的事件
    emits('done', data);
  };
  /** 文件数超出提示 */
  const handleExceed = () => {
    message.error('最多只能上传一个文件！');
  };
  /** 下载模板操作 */
  const onDownload = async () => {
    const loading = EleMessage.loading('请求中..');
    try {
      //获取下载模版
      const res = await LongContractApi.exportLongTemplateContract();
      download.excel(res, '长租合同费用模板.xlsx');
    } finally {
      loading.close();
    }
  };
  /** 重置表单 */
  const resetForm = async () => {
    // 重置上传状态和文件
    formLoading.value = false;
    await nextTick();
    uploadRef.value?.clearFiles();
  };
  /** 弹窗打开事件 */
  const handleOpen = async () => {
    visible.value = true;
    updateSupport.value = 0;
    fileList.value = [];
    resetForm();
    databaseId.value = props.databaseId;
  };
</script>

<style lang="scss" scoped>
  .import-upload {
    margin-bottom: 12px;

    :deep(.el-upload > .el-upload-dragger) {
      padding: 0;
      height: 168px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      transition: (border-color 0.2s, background-color 0.2s);

      &:not(.is-dragover) {
        background: var(--el-fill-color-light);
      }
    }

    :deep(.el-icon > svg) {
      stroke-width: 3;
    }
  }
</style>
