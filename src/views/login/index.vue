<template>
  <div class="container">
    <div class="forms-container">
      <div
        class="signin-signup"
        style="border-radius: 10px; background-color: rgb(245 245 245 / 60%)"
      >
        <h2 class="title">用户登录</h2>
        <el-form
          v-if="tabActive == 1"
          ref="formLogin"
          size="default"
          :model="loginData.loginForm"
          :rules="LoginRules"
          label-width="28px"
        >
          <el-form-item prop="tenantName">
            <el-input
              class="input-field"
              style="height: 45px"
              size="small"
              clearable
              v-model="loginData.loginForm.tenantName"
              placeholder="请输入租户"
              :prefix-icon="House"
            />
          </el-form-item>
          <el-form-item prop="username">
            <el-input
              class="input-field"
              style="height: 45px"
              size="small"
              clearable
              v-model="loginData.loginForm.username"
              placeholder="请输入登录账号"
              :prefix-icon="UserOutlined"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              style="height: 45px"
              class="input-field"
              show-password
              v-model="loginData.loginForm.password"
              placeholder="请输入登录密码"
              :prefix-icon="LockOutlined"
            />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="loginData.loginForm.rememberMe"
              >记住我</el-checkbox
            >
          </el-form-item>
          <el-form-item>
            <el-button
              size="large"
              type="primary"
              :loading="loginLoading"
              style="width: 93%"
              @click="getCode()"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
        <Verify
          ref="verify"
          :captchaType="captchaType"
          :imgSize="{ width: '400px', height: '200px' }"
          mode="pop"
          @success="handleLogin"
        />
      </div>
    </div>
    <div class="panels-container">
      <div class="panel left-panel">
        <div>
          <img src="@/assets/logo-full-w.png" style="width: 40%" />
        </div>
        <div class="content" style="padding-top: 20px">
          <h1 style="font-size: 60px"> SINCE 1988 </h1>
          <p style="font-size: 40px">偕同白衣使者 </p>
          <p style="font-size: 40px; margin-top: -30px">开创健康未来 </p>
          <p style="font-size: 24px; margin-top: -20px">
            Your Health We Care
          </p>
          <div style="padding-top: 90px">
            <button class="btn transparent" id="sign-up-btn" @click="goToWgUrl">
              <span style="font-weight: bold">探索威高 →</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { ref, reactive, unref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { UserOutlined, LockOutlined } from '@/components/icons';
  import { House } from '@element-plus/icons-vue';
  import { usePageTab } from '@/utils/use-page-tab';
  import { login, getTenantByWebsite, getTenantIdByName } from '@/api/login';
  import * as authUtil from '@/utils/auth';
  import { Verify } from '@/components/Verifition';
  const { currentRoute, push } = useRouter();
  const { goHomeRoute, cleanPageTabs } = usePageTab();
  import { useFormValid } from './useLogin';

  /** 页签选中 */
  const tabActive = ref(1);

  /** 表单 */
  const formLogin = ref();

  const loginData = reactive({
    isShowPassword: false,
    captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
    tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
    loginForm: {
      tenantName: import.meta.env.VITE_APP_DEFAULT_LOGIN_TENANT || '',
      username: '',
      password: '',
      captchaVerification: '',
      rememberMe: true // 默认记录我。如果不需要，可手动修改
    }
  });

  /** 表单验证规则 */
  const LoginRules = reactive({
    tenantName: [
      {
        required: true,
        message: '请输入租户名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    username: [
      {
        required: true,
        message: '请输入登录账号',
        type: 'string',
        trigger: 'blur'
      }
    ],
    password: [
      {
        required: true,
        message: '请输入登录密码',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  const verify = ref();
  const captchaType = ref('blockPuzzle'); // blockPuzzle 滑块 clickWord 点击文字
  // 获取验证码
  const getCode = async () => {
    // 情况一，未开启：则直接登录
    if (loginData.captchaEnable === 'false') {
      await handleLogin({});
    } else {
      // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
      const data = await validForm();
      if (!data) {
        return;
      }
      // 弹出验证码
      verify.value.show();
    }
  };
  // 获取租户 ID
  const getTenantId = async () => {
    if (loginData.tenantEnable === 'true') {
      const res = await getTenantIdByName(loginData.loginForm.tenantName);
      authUtil.setTenantId(res);
    }
  };
  // 记住我
  const getLoginFormCache = () => {
    const loginForm = authUtil.getLoginForm();
    if (loginForm) {
      loginData.loginForm = {
        ...loginData.loginForm,
        username: loginForm.username
          ? loginForm.username
          : loginData.loginForm.username,
        password: loginForm.password
          ? loginForm.password
          : loginData.loginForm.password,
        rememberMe: loginForm.rememberMe,
        tenantName: loginForm.tenantName
          ? loginForm.tenantName
          : loginData.loginForm.tenantName
      };
    }
  };
  const loginLoading = ref(false);
  const { validForm } = useFormValid(formLogin);
  // 登录
  const handleLogin = async (params) => {
    loginLoading.value = true;
    try {
      await getTenantId();
      const data = await validForm();
      if (!data) {
        return;
      }
      const loginDataLoginForm = { ...loginData.loginForm };
      loginDataLoginForm.captchaVerification = params.captchaVerification;
      const res = await login(loginDataLoginForm);
      if (!res) {
        return;
      }
      if (loginDataLoginForm.rememberMe) {
        authUtil.setLoginForm(loginDataLoginForm);
      } else {
        authUtil.removeLoginForm();
      }
      //setToken(res.accessToken,res.refreshToken, data.remember);
      authUtil.setToken(res);
      cleanPageTabs();
      goHome();
    } finally {
      loginLoading.value = false;
    }
  };
  const goToWgUrl = () => {
    window.open('https://www.weigaoholding.com');
  };

  /** 跳转到首页 */
  const goHome = () => {
    const { query } = unref(currentRoute);
    goHomeRoute(query.from);
  };

  // 如果已登录直接进入首页
  if (authUtil.getAccessToken()) {
    goHome();
  }
  onMounted(() => {
    getLoginFormCache();
  });
</script>

<style lang="scss">
  * {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
  }
  body,
  input {
    font-family: 'Poppins', sans-serif;
  }
  .container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    background-color: #fff;
    overflow: hidden;
    background-image: url('@/assets/login-bg-wh.jpg');
    background-size: 100% 100%;
  }
  .title {
    font-size: 25px;
    color: #444;
    margin-bottom: 10px;
    margin-top: 10px;
    text-align: center;
  }
  .input-field {
    max-width: 380px;
    width: 100%;
    background-color: #f0f0f0;
  }
  .input-field input {
    background: none;
    outline: none;
    border: none;
    line-height: 1;
    font-weight: 400;
    font-size: 1.1rem;
    color: #333;
  }
  .input-field input::placeholder {
    color: #aaa;
    font-weight: 380;
  }
  .btn {
    width: 150px;
    height: 50px;
    border: none;
    outline: none;
    border-radius: 49px;
    cursor: pointer;
    background-color: #5995fd;
    color: #fff;
    text-transform: uppercase;
    font-weight: 600;
    margin: 10px 0;
    transition: 0.5s;
  }
  .btn:hover {
    background-color: #4d84e2;
  }

  .social-icon:hover {
    color: #4481eb;
    border-color: #4481eb;
  }
  .signin-signup {
    position: absolute;
    top: 45%;
    left: 75%;
    transform: translate(-50%, -50%);
    width: 28%;
    display: grid;
    grid-template-columns: 1fr;
    z-index: 5;
    transition: 1s 0.7s ease-in-out;
    height: 450px;
  }
  .panels-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;

    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  .panel {
    display: flex;
    flex-direction: column;
    z-index: 7;
  }
  .left-panel {
    pointer-events: all;
    padding: 3rem 17% 2rem 12%;
  }
  .panel .content {
    color: #fff;
    transition: 0.9s 0.6s ease-in-out;
  }
  .panel h3 {
    font-weight: 600;
    line-height: 1;
    font-size: 1.5rem;
  }
  .panel p {
    font-size: 0.95rem;
    padding: 0.7rem 0;
  }
  .btn.transparent {
    margin: 0;
    background: none;
    border: 2px solid#fff;
    width: 130px;
    height: 41px;
    font-weight: 600;
    font-size: 0.8rem;
  }
  .right-panel {
    padding: 3rem 12% 2rem 17%;
    pointer-events: none;
  }
  .image {
    width: 100%;
    transition: 1.1s 0.4s ease-in-out;
  }
  .right-panel .content,
  .right-panel .image {
    transform: translateX(1000px);
  }
  .container.sign-up-mode::before {
    transform: translate(100%, -50%);
    right: 52%;
  }

  /* 验证码 */
  .login-captcha-group {
    width: 100%;
    display: flex;
    align-items: center;

    :deep(.el-input) {
      flex: 1;
    }

    .login-captcha {
      flex-shrink: 0;
      width: 108px;
      height: 40px;
      margin-left: 8px;
      border-radius: var(--el-border-radius-base);
      border: 1px solid var(--el-border-color);
      transition: border 0.2s;
      box-sizing: border-box;
      background: #fff;
      overflow: hidden;
      cursor: pointer;

      img {
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        margin: -2px 0 0 -2px;
        display: block;
      }

      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }
</style>
