<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="任务名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入任务名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.BPM_TASK_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link :underline="false" type="primary" @click="handleAudit(row)">
            历史
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter, formatPast2 } from '@/utils/formatTime';
  import { Search, Refresh } from '@element-plus/icons-vue';
  import * as TaskApi from '@/api/bpm/task';

  defineOptions({ name: 'BpmManagerTask' });
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: null,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };

  const { push } = useRouter(); // 路由  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'processInstance.name',
        label: '流程',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'processInstance.startUser.nickname',
        label: '发起人',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'createTime',
        label: '发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'name',
        label: '当前任务',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'createTime',
        label: '任务开始时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'endTime',
        label: '任务结束时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'assigneeUser.nickname',
        label: '审批人',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'status',
        label: '审批状态',
        align: 'center',
        minWidth: 110,
        slot: 'status'
      },
      {
        prop: 'reason',
        label: '审批建议',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'durationInMillis',
        label: '耗时',
        align: 'center',
        minWidth: 110,
        formatter: formatPast2
      },
      {
        prop: 'id',
        label: '流程编号',
        align: 'center',
        minWidth: 180
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaskApi.getTaskManagerPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };

  /** 处理审批按钮 */
  const handleAudit = (row) => {
    push({
      name: 'BpmProcessInstanceTaskDetail',
      query: {
        id: row.processInstance.id
      }
    });
  };
</script>
