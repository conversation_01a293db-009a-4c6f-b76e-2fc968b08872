<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="任务名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入任务名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :load-on-created="false"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="TaskIndexTable"
      >
        <template #action="{ row }">
          <el-button link type="primary" @click="handleAudit(row)"
            >办理</el-button
          >
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { dateFormatter } from '@/utils/formatTime';
  import * as TaskApi from '@/api/bpm/task';
  import { Search, Refresh, Plus } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmTodoTask' });

  const { push } = useRouter(); // 路由

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: '',
    createTime: []
  });
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'processInstance.name',
        label: '流程',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'processInstance.startUser.nickname',
        label: '发起人',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'createTime',
        label: '发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'name',
        label: '当前任务',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'createTime',
        label: '任务时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'id',
        label: '流程编号',
        align: 'center',
        minWidth: 320
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaskApi.getTaskTodoPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };

  /** 处理审批按钮 */
  const handleAudit = (row: any) => {
    push({
      name: 'BpmProcessInstanceTodoDetail',
      query: {
        id: row.processInstance.id,
        taskId: row.id
      }
    });
  };
  /** 初始化 **/
  onActivated(() => {
    reload();
  });
</script>
