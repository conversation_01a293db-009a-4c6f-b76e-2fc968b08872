<template>
  <ele-page flex-table>
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="任务名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入任务名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="抄送时间">
              <el-date-picker
                v-model="queryParams.createTime"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                end-placeholder="结束日期"
                start-placeholder="开始日期"
                type="daterange"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="ProcessDoneTable"
      >
        <template #status="{ row }">
          <dict-data
            type="tag"
            :code="DICT_TYPE.BPM_TASK_STATUS"
            :model-value="row.status"
          />
        </template>
        <template #durationInMillis="{ row }">
          {{ formatPast2(row.durationInMillis) }}
        </template>
        <template #action="{ row }">
          <el-button link type="primary" @click="handleAudit(row)">
            历史
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>
<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter, formatPast2 } from '@/utils/formatTime';
  import * as TaskApi from '@/api/bpm/task';
  import { Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'BpmTodoTask' });

  const { push } = useRouter(); // 路由
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: '',
    createTime: []
  });

  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'processInstance.name',
        label: '流程',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'processInstance.startUser.nickname',
        label: '发起人',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'createTime',
        label: '发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'name',
        label: '当前任务',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'createTime',
        label: '任务开始时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'endTime',
        label: '任务结束时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'status',
        label: '审批状态',
        align: 'center',
        minWidth: 180,
        slot: 'status'
      },
      {
        prop: 'reason',
        label: '审批建议',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'durationInMillis',
        label: '耗时',
        align: 'center',
        minWidth: 180,
        slot: 'durationInMillis'
      },
      {
        prop: 'id',
        label: '流程编号',
        align: 'center',
        minWidth: 180
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });
  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return TaskApi.getTaskDonePage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 处理审批按钮 */
  const handleAudit = (row: any) => {
    push({
      name: 'BpmProcessInstanceDoneDetail',
      query: {
        id: row.processInstance.id,
        taskId: row.id
      }
    });
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
</script>
