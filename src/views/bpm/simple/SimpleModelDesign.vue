<template>
  <ele-page flex-table>
    <ele-card :body-style="{ padding: '20px 16px' }">
      <SimpleProcessDesigner :model-id="modelId" @success="close" />
    </ele-card>
  </ele-page>
</template>
<script setup lang="ts">
  import { SimpleProcessDesigner } from '@/components/SimpleProcessDesignerV2/src/';
  import { usePageTab } from '@/utils/use-page-tab';
  const { finishPageTab } = usePageTab();

  const router = useRouter(); // 路由
  const { query } = useRoute(); // 路由的查询
  const modelId = query.modelId as string;
  const close = () => {
    //关闭当前先
    finishPageTab();
    router.push({ path: '/manager/model' });
  };
</script>
<style lang="scss" scoped></style>
