<template>
  <ele-card
    flex-table
    :bodyStyle="{ padding: '10px 20px 0' }"
    shadow="never"
    class="position-relative mb-15px"
    style="height: 100%"
    v-loading="loading"
  >
    <div class="text-#878c93 h-15px text-14px font-bold"
      >流程：{{ selectProcessDefinition.name }}</div
    >
    <el-divider class="!my-8px" />
    <!-- 中间主要内容 tab 栏 -->
    <div class="tabs-container" style="height: calc(100% - 90px)">
      <el-tabs v-model="activeTab" style="height: 100%">
        <!-- 表单信息 -->
        <el-tab-pane label="表单填写" name="form">
          <div class="form-scroll-area" style="height: 100%">
            <ele-split-panel
              space="0px"
              size="330px"
              :allow-collapse="false"
              :resizable="false"
              :vertical="false"
              :reverse="true"
              :flexTable="true"
              style="height: 100%"
              :body-style="{ overflow: 'hidden' }"
              :custom-style="{
                overflow: 'hidden',
                border: 'none'
              }"
              :responsive="false"
            >
              <el-scrollbar style="height: 100%">
                <ProcessInstanceTimeline
                  ref="timelineRef"
                  :activity-nodes="activityNodes"
                  :show-status-icon="false"
                  @select-user-confirm="selectUserConfirm"
                />
              </el-scrollbar>
              <template #body>
                <!-- 表单信息 -->
                <el-scrollbar style="height: 100%">
                  <div
                    :style="{ '--ele-input-disabled-bg': '#fff' }"
                    style="
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      height: 100%;
                    "
                    class="form-box flex flex-col mb-30px flex-1"
                  >
                    <form-create
                      style="width: 95%"
                      :rule="detailForm.rule"
                      v-model:api="fApi"
                      v-model="detailForm.value"
                      :option="detailForm.option"
                      @submit="submitForm"
                    />
                  </div>
                </el-scrollbar>
              </template>
            </ele-split-panel>
          </div>
        </el-tab-pane>
        <!-- 流程图 -->
        <el-tab-pane label="流程图" name="diagram">
          <el-scrollbar style="height: 100%">
            <!-- BPMN 流程图预览 -->
            <ProcessInstanceBpmnViewer
              :bpmn-xml="bpmnXML"
              v-if="BpmModelType.BPMN === selectProcessDefinition.modelType"
            />

            <!-- Simple 流程图预览 -->
            <ProcessInstanceSimpleViewer
              :simple-json="simpleJson"
              v-if="BpmModelType.SIMPLE === selectProcessDefinition.modelType"
            />
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 底部操作栏 -->
    <div class="b-t-solid border-t-1px border-[var(--el-border-color)]">
      <!-- 操作栏按钮 -->
      <div
        v-if="activeTab === 'form'"
        class="h-50px bottom-10 text-14px flex items-center color-#32373c dark:color-#fff font-bold btn-container"
      >
        <el-button type="success" :loading="loading" @click="submitForm">
          <el-icon><Select /></el-icon>&nbsp; 发起
        </el-button>
        <el-button type="danger" :loading="loading" @click="handleCancel">
          <el-icon><Close /></el-icon>&nbsp; 取消
        </el-button>
      </div>
    </div>
  </ele-card>
</template>
<script lang="ts" setup>
  import { Select, Close } from '@element-plus/icons-vue';
  import { decodeFields, setConfAndFields2 } from '@/utils/formCreate';
  import { BpmModelType } from '@/utils/constants';
  import {
    CandidateStrategy,
    NodeId
  } from '@/components/SimpleProcessDesignerV2/src/consts';
  import ProcessInstanceBpmnViewer from '../detail/ProcessInstanceBpmnViewer.vue';
  import ProcessInstanceSimpleViewer from '../detail/ProcessInstanceSimpleViewer.vue';
  import ProcessInstanceTimeline from '../detail/ProcessInstanceTimeline.vue';
  import type { ApiAttrs } from '@form-create/element-ui/types/config';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import * as DefinitionApi from '@/api/bpm/definition';
  import { ApprovalNodeInfo } from '@/api/bpm/processInstance';
  import { useMessage } from '@/hooks/web/useMessage';
  import { usePageTab } from '@/utils/use-page-tab';

  defineOptions({ name: 'ProcessDefinitionDetail' });
  const props = defineProps<{
    selectProcessDefinition: any;
  }>();
  const { finishPageTab } = usePageTab();
  const emit = defineEmits(['cancel']);
  const loading = ref(false);
  const { push, currentRoute } = useRouter(); // 路由
  const message = useMessage(); // 消息弹窗

  const detailForm: any = ref({
    rule: [],
    option: {},
    value: {}
  }); // 流程表单详情
  const fApi = ref<ApiAttrs>();
  // 指定审批人
  const startUserSelectTasks: any = ref([]); // 发起人需要选择审批人或抄送人的任务列表
  const startUserSelectAssignees = ref({}); // 发起人选择审批人的数据
  const bpmnXML: any = ref(null); // BPMN 数据
  const simpleJson = ref<string | undefined>(); // Simple 设计器数据 json 格式

  const activeTab = ref('form'); // 当前的 Tab
  const activityNodes = ref<ProcessInstanceApi.ApprovalNodeInfo[]>([]); // 审批节点信息

  /** 设置表单信息、获取流程图数据 **/
  const initProcessInfo = async (row: any, formVariables?: any) => {
    // 重置指定审批人
    startUserSelectTasks.value = [];
    startUserSelectAssignees.value = {};

    // 情况一：流程表单
    if (row.formType == 10) {
      // 设置表单
      // 注意：需要从 formVariables 中，移除不在 row.formFields 的值。
      // 原因是：后端返回的 formVariables 里面，会有一些非表单的信息。例如说，某个流程节点的审批人。
      //        这样，就可能导致一个流程被审批不通过后，重新发起时，会直接后端报错！！！
      const allowedFields = decodeFields(row.formFields).map(
        (fieldObj: any) => fieldObj.field
      );
      for (const key in formVariables) {
        if (!allowedFields.includes(key)) {
          delete formVariables[key];
        }
      }
      setConfAndFields2(
        detailForm,
        row.formConf,
        row.formFields,
        formVariables
      );
      await nextTick();
      fApi.value?.btn.show(false); // 隐藏提交按钮
      debugger;
      // 获取流程审批信息
      await getApprovalDetail(row);

      // 加载流程图
      const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
        row.id
      );
      if (processDefinitionDetail) {
        bpmnXML.value = processDefinitionDetail.bpmnXml;
        simpleJson.value = processDefinitionDetail.simpleModel;
      }
    }
    //外部表单
    else if (row.formType == 30) {
      fApi.value?.btn.show(false); // 隐藏提交按钮
      // 获取流程审批信息
      await getApprovalDetail(row);

      // 加载流程图
      const processDefinitionDetail = await DefinitionApi.getProcessDefinition(
        row.id
      );
      if (processDefinitionDetail) {
        bpmnXML.value = processDefinitionDetail.bpmnXml;
        simpleJson.value = processDefinitionDetail.simpleModel;
      }
    }
    // 业务表单
    else if (row.formCustomCreatePath) {
      await push({
        path: row.formCustomCreatePath
      });
      // 这里暂时无需加载流程图，因为跳出到另外个 Tab；
    }
  };

  /** 获取审批详情 */
  const getApprovalDetail = async (row: any) => {
    try {
      const data = await ProcessInstanceApi.getApprovalDetail({
        processDefinitionId: row.id,
        activityId: NodeId.START_USER_NODE_ID
      });
      if (!data) {
        message.error('查询不到审批详情信息！');
        return;
      }

      // 获取发起人自选的任务
      startUserSelectTasks.value = data.activityNodes?.filter(
        (node: ApprovalNodeInfo) =>
          CandidateStrategy.START_USER_SELECT === node.candidateStrategy
      );
      if (startUserSelectTasks.value?.length > 0) {
        for (const node of startUserSelectTasks.value) {
          startUserSelectAssignees.value[node.id] = [];
        }
      }

      // 获取审批节点，显示 Timeline 的数据
      activityNodes.value = data.activityNodes;
      // 获取表单字段权限
      const formFieldsPermission = data.formFieldsPermission;
      // 设置表单字段权限
      if (formFieldsPermission) {
        Object.keys(formFieldsPermission).forEach((item) => {
          setFieldPermission(item, formFieldsPermission[item]);
        });
      }
    } finally {
    }
  };
  /**
   * 设置表单权限
   */
  const setFieldPermission = (field: string, permission: string) => {
    if (permission === '1') {
      //@ts-ignore
      fApi.value?.disabled(true, field);
    }
    if (permission === '2') {
      //@ts-ignore
      fApi.value?.disabled(false, field);
    }
    if (permission === '3') {
      //@ts-ignore
      fApi.value?.hidden(true, field);
    }
  };
  /** 提交按钮 */
  const submitForm = async () => {
    try {
      loading.value = true;
      if (!fApi.value || !props.selectProcessDefinition) {
        return;
      }
      // 流程表单校验
      await fApi.value.validate();
      // 如果有指定审批人，需要校验
      if (startUserSelectTasks.value?.length > 0) {
        for (const userTask of startUserSelectTasks.value) {
          if (
            Array.isArray(startUserSelectAssignees.value[userTask.id]) &&
            startUserSelectAssignees.value[userTask.id].length === 0
          )
            return message.warning(`请选择${userTask.name}的候选人`);
        }
      }

      // 提交请求
      fApi.value.btn.loading(true);
      try {
        await ProcessInstanceApi.createProcessInstance({
          processDefinitionId: props.selectProcessDefinition.id,
          variables: detailForm.value.value,
          startUserSelectAssignees: startUserSelectAssignees.value
        });
        // 提示
        message.success('发起流程成功');
        //退回上一层
        emit('cancel');
      } finally {
        fApi.value.btn.loading(false);
      }
    } finally {
      loading.value = false;
    }
  };

  /** 取消发起审批 */
  const handleCancel = () => {
    emit('cancel');
  };

  /** 选择发起人 */
  const selectUserConfirm = (id: string, userList: any[]) => {
    startUserSelectAssignees.value[id] = userList?.map((item: any) => item.id);
  };

  defineExpose({ initProcessInfo });
</script>

<style lang="scss" scoped>
  $wrap-padding-height: 20px;
  $wrap-margin-height: 15px;
  $button-height: 51px;
  $process-header-height: 105px;

  .processInstance-wrap-main {
    height: calc(
      100vh - var(--top-tool-height) - var(--tags-view-height) - var(
          --app-footer-height
        ) -
        35px
    );
    max-height: calc(
      100vh - var(--top-tool-height) - var(--tags-view-height) - var(
          --app-footer-height
        ) -
        35px
    );
    overflow: auto;

    .form-scroll-area {
      height: calc(
        100vh - var(--top-tool-height) - var(--tags-view-height) - var(
            --app-footer-height
          ) -
          35px - $process-header-height - 40px
      );
      max-height: calc(
        100vh - var(--top-tool-height) - var(--tags-view-height) - var(
            --app-footer-height
          ) -
          35px - $process-header-height - 40px
      );
      overflow: auto;
    }
  }

  .form-box {
    :deep(.el-card) {
      border: none;
    }
  }

  .tabs-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .el-tabs {
    display: flex;
    height: 100%;
  }

  .el-tabs__header {
    order: 1; /* 确保 tabs 头部在顶部 */
  }

  .el-tabs__content {
    flex: 1;
    overflow: hidden;
    order: 2; /* 确保 tabs 内容在底部 */
  }

  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
</style>
