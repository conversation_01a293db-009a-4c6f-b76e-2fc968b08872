<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发起人">
              <el-select
                v-model="queryParams.startUserId"
                placeholder="请选择发起人"
                clearable
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.nickname"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程名称">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="所属流程">
              <el-input
                clearable
                v-model.trim="queryParams.processDefinitionId"
                placeholder="请输入流程定义的编号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程分类">
              <el-select
                v-model="queryParams.category"
                placeholder="请选择流程分类"
                clearable
              >
                <el-option
                  v-for="category in categoryList"
                  :key="category.code"
                  :label="category.name"
                  :value="category.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="流程状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择流程状态"
                clearable
                class="!w-240px"
              >
                <el-option
                  v-for="dict in getIntDictOptions(
                    DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS
                  )"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button type="primary" @click="reload">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #durationInMillis="{ row }">
          {{
            row.durationInMillis > 0 ? formatPast2(row.durationInMillis) : '-'
          }}
        </template>
        <template #tasks="{ row }">
          <el-button
            type="primary"
            v-for="task in row.tasks"
            :key="task.id"
            link
          >
            <span>{{ task.name }}</span>
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="handleDetail(row)"
            v-permission="['bpm:process-instance:query']"
          >
            详细
          </el-link>
          <el-divider
            direction="vertical"
            v-if="row.status === 1"
            v-permission="'bpm:process-instance:cancel'"
          />
          <el-link
            :underline="false"
            type="primary"
            v-if="row.status === 1"
            @click="handleCancel(row)"
            v-permission="['bpm:process-instance:cancel']"
          >
            取消
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter, formatPast2 } from '@/utils/formatTime';
  import { ref, computed } from 'vue';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import { CategoryApi } from '@/api/bpm/category';
  import * as UserApi from '@/api/system/user';
  import { cancelProcessInstanceByAdmin } from '@/api/bpm/processInstance';
  import { ElMessageBox } from 'element-plus';
  const categoryList = ref([]); // 流程分类列表
  defineOptions({ name: 'BpmProcessInstanceManager' });
  const userList = ref([]); // 用户列表
  const { push } = useRouter(); // 路由
  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    startUserId: undefined,
    name: '',
    processDefinitionId: undefined,
    category: undefined,
    status: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        label: '流程名称',
        align: 'center',
        minWidth: 200
      },
      {
        prop: 'categoryName',
        label: '流程分类',
        align: 'center',
        minWidth: 100,
        slot: 'icon'
      },
      {
        prop: 'startUser.nickname',
        label: '流程发起人',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'startUser.deptName',
        label: '发起部门',
        align: 'center',
        minWidth: 120
      },
      {
        prop: 'status',
        label: '流程状态',
        align: 'center',
        minWidth: 110,
        slot: 'status'
      },
      {
        prop: 'startTime',
        label: '发起时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'endTime',
        label: '结束时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        prop: 'durationInMillis',
        label: '耗时',
        align: 'center',
        minWidth: 110,
        slot: 'durationInMillis'
      },
      {
        prop: 'tasks',
        label: '当前审批任务',
        align: 'center',
        minWidth: 120,
        slot: 'tasks'
      },
      {
        prop: 'id',
        label: '流程编号',
        align: 'center',
        minWidth: 320
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ProcessInstanceApi.getProcessInstanceManagerPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 初始化 **/
  onMounted(async () => {
    categoryList.value = await CategoryApi.getCategorySimpleList();
    userList.value = await UserApi.getSimpleUserList();
  });
  /** 流程实例查看详情 */
  const handleDetail = (row) => {
    push({
      name: 'BpmProcessInstanceManagerDetail',
      query: {
        id: row.id
      }
    });
  };

  /** 取消按钮操作 */
  const handleCancel = async (row) => {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '取消原因不能为空'
    });
    // 发起取消
    await ProcessInstanceApi.cancelProcessInstanceByAdmin(row.id, value);
    message.success('取消成功');
    // 刷新列表
    reload();
  };
</script>
