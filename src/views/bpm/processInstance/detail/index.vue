<template>
  <ele-page flex-table style="min-height: 450px; height: 100vh">
    <ele-card
      flex-table
      :bodyStyle="{ padding: '10px 20px 0' }"
      shadow="never"
      class="position-relative mb-15px"
      style="height: 100%"
    >
      <img
        class="position-absolute right-20px"
        width="120"
        :src="auditIconsMap[processInstance.status]"
        alt=""
      />
      <div class="text-#878c93 h-15px">编号：{{ processInstance.id }}</div>
      <el-divider class="!my-8px" />
      <div class="flex items-center gap-5 mb-10px text-13px h-35px">
        <div class="text-18px font-bold mb-5px">{{ processInstance.name }}</div>
        <div class="text-#878c93">
          {{ processInstance?.startUser?.nickname }}
          {{ formatDate(processInstance.startTime) }} 提交
        </div>
      </div>

      <div class="tabs-container" style="height: calc(100% - 130px)">
        <el-tabs v-model="activeTab" style="height: 100%">
          <!-- 表单信息 -->
          <el-tab-pane label="审批详情" name="form">
            <div class="form-scroll-area" style="height: 100%">
              <ele-split-panel
                space="0px"
                size="330px"
                :allow-collapse="false"
                :resizable="false"
                :vertical="false"
                :reverse="true"
                :flexTable="true"
                style="height: 100%"
                :body-style="{ overflow: 'hidden' }"
                :custom-style="{
                  overflow: 'hidden',
                  border: 'none'
                }"
                :responsive="false"
              >
                <el-scrollbar style="height: 100%">
                  <ProcessInstanceTimeline :activity-nodes="activityNodes" />
                </el-scrollbar>
                <template #body>
                  <!-- 表单信息 -->
                  <el-scrollbar style="height: 100%">
                    <div
                      :style="{ '--ele-input-disabled-bg': '#fff' }"
                      style="
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                      "
                      v-loading="processInstanceLoading"
                      class="form-box flex flex-col mb-30px flex-1"
                    >
                      <form-create
                        class="form-view"
                        style="width: 95%"
                        v-if="processDefinition?.formType === 10"
                        v-model="detailForm.value"
                        v-model:api="fApi"
                        :option="detailForm.option"
                        :rule="detailForm.rule"
                      />
                      <!-- 情况二：业务表单 -->
                      <div
                        style="flex: 1; height: 100%"
                        v-if="processDefinition?.formType === 20"
                      >
                        <BusinessFormComponent
                          :id="processInstance.businessKey"
                        />
                      </div>
                      <!-- 外部表单 -->
                      <div
                        style="flex: 1; height: 100%"
                        v-if="processDefinition?.formType === 30"
                      >
                        <div class="ele-admin-iframe-wrap" style="height: 100%">
                          <iframe
                            :src="externalUrl"
                            class="ele-admin-iframe"
                          ></iframe>
                        </div>
                      </div>
                    </div>
                  </el-scrollbar>
                </template>
              </ele-split-panel>
            </div>
          </el-tab-pane>

          <!-- 流程图 -->
          <el-tab-pane label="流程图" name="diagram">
            <el-scrollbar style="height: 100%">
              <ProcessInstanceSimpleViewer
                v-show="
                  processDefinition.modelType &&
                  processDefinition.modelType === BpmModelType.SIMPLE
                "
                :loading="processInstanceLoading"
                :model-view="processModelView"
              />
              <ProcessInstanceBpmnViewer
                v-show="
                  processDefinition.modelType &&
                  processDefinition.modelType === BpmModelType.BPMN
                "
                :loading="processInstanceLoading"
                :model-view="processModelView"
              />
            </el-scrollbar>
          </el-tab-pane>

          <!-- 流转记录 -->
          <el-tab-pane label="流转记录" name="record">
            <div style="height: 100%">
              <el-scrollbar>
                <ProcessInstanceTaskList
                  :loading="processInstanceLoading"
                  :id="id"
                />
              </el-scrollbar>
            </div>
          </el-tab-pane>

          <!-- 流转评论 TODO 待开发 -->
          <el-tab-pane label="流转评论" name="comment" v-if="false">
            <div class="form-scroll-area">
              <el-scrollbar> 流转评论 </el-scrollbar>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="b-t-solid border-t-1px border-[var(--el-border-color)]">
        <!-- 操作栏按钮 -->
        <ProcessInstanceOperationButton
          ref="operationButtonRef"
          :process-instance="processInstance"
          :process-definition="processDefinition"
          :userOptions="userOptions"
          :normal-form="detailForm"
          :normal-form-api="fApi"
          :writable-fields="writableFields"
          @success="refresh"
        />
      </div>
    </ele-card>
  </ele-page>
</template>

<script lang="ts" setup>
  import { formatDate } from '@/utils/formatTime';
  import { BpmModelType } from '@/utils/constants';
  import { setConfAndFields2 } from '@/utils/formCreate';
  import { registerComponent } from '@/utils/routerHelper';
  import type { ApiAttrs } from '@form-create/element-ui/types/config';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import * as UserApi from '@/api/system/user';
  import ProcessInstanceBpmnViewer from './ProcessInstanceBpmnViewer.vue';
  import ProcessInstanceSimpleViewer from './ProcessInstanceSimpleViewer.vue';
  import ProcessInstanceTaskList from './ProcessInstanceTaskList.vue';
  import ProcessInstanceOperationButton from './ProcessInstanceOperationButton.vue';
  import ProcessInstanceTimeline from './ProcessInstanceTimeline.vue';
  import { FieldPermissionType } from '@/components/SimpleProcessDesignerV2/src/consts';
  import { TaskStatusEnum } from '@/api/bpm/task';
  import runningSvg from '@/assets/svgs/bpm/running.svg';
  import approveSvg from '@/assets/svgs/bpm/approve.svg';
  import rejectSvg from '@/assets/svgs/bpm/reject.svg';
  import cancelSvg from '@/assets/svgs/bpm/cancel.svg';
  import * as authUtil from '@/utils/auth';

  defineOptions({ name: 'BpmProcessInstanceDetail' });
  const props = defineProps<{
    id: string; // 流程实例的编号
    taskId?: string; // 任务编号
    activityId?: string; //流程活动编号，用于抄送查看
  }>();
  const message = useMessage(); // 消息弹窗
  const processInstanceLoading = ref(false); // 流程实例的加载中
  const processInstance = ref<any>({}); // 流程实例
  const processDefinition = ref<any>({}); // 流程定义
  const processModelView = ref<any>({}); // 流程模型视图
  const operationButtonRef = ref(); // 操作按钮组件 ref
  const auditIconsMap = {
    [TaskStatusEnum.RUNNING]: runningSvg,
    [TaskStatusEnum.APPROVE]: approveSvg,
    [TaskStatusEnum.REJECT]: rejectSvg,
    [TaskStatusEnum.CANCEL]: cancelSvg
  };
  const writableFields: Array<string> = []; // 表单可以编辑的字段

  // ========== 申请信息 ==========
  const externalUrl = ref('');
  const fApi = ref<ApiAttrs>(); //
  const detailForm = ref({
    rule: [],
    option: {},
    value: {}
  }); // 流程实例的表单详情

  /** 获得详情 */
  const getDetail = async () => {
    await getApprovalDetail();

    await getProcessModelView();
  };

  /** 加载流程实例 */
  const BusinessFormComponent = ref<any>(null); // 异步组件
  /** 获取审批详情 */
  const getApprovalDetail = async () => {
    detailForm.value.value = {};
    detailForm.value.rule = [];
    detailForm.value.option = {};
    processInstanceLoading.value = true;
    try {
      const param = {
        processInstanceId: props.id,
        activityId: props.activityId,
        taskId: props.taskId
      };
      const data = await ProcessInstanceApi.getApprovalDetail(param);
      if (!data) {
        message.error('查询不到审批详情信息！');
        return;
      }
      if (!data.processDefinition || !data.processInstance) {
        message.error('查询不到流程信息！');
        return;
      }
      processInstance.value = data.processInstance;
      processDefinition.value = data.processDefinition;

      // 设置表单信息
      if (processDefinition.value.formType === 10) {
        // 获取表单字段权限
        const formFieldsPermission = data.formFieldsPermission;
        // 清空可编辑字段为空
        writableFields.splice(0);
        if (detailForm.value.rule.length > 0) {
          // 避免刷新 form-create 显示不了
          detailForm.value.value = processInstance.value.formVariables;
        } else {
          setConfAndFields2(
            detailForm,
            processDefinition.value.formConf,
            processDefinition.value.formFields,
            processInstance.value.formVariables
          );
        }
        nextTick().then(() => {
          fApi.value?.btn.show(false);
          fApi.value?.resetBtn.show(false);
          //@ts-ignore
          fApi.value?.disabled(true);
          // 设置表单字段权限
          if (formFieldsPermission) {
            Object.keys(data.formFieldsPermission).forEach((item) => {
              setFieldPermission(item, formFieldsPermission[item]);
            });
          }
        });
      } else {
        // 注意：data.processDefinition.formCustomViewPath 是组件的全路径，例如说：/crm/contract/detail/index.vue
        BusinessFormComponent.value = registerComponent(
          data.processDefinition.formCustomViewPath
        );
      }

      // 获取审批节点，显示 Timeline 的数据
      activityNodes.value = data.activityNodes;

      // 获取待办任务显示操作按钮
      operationButtonRef.value?.loadTodoTask(data.todoTask);
      if (processInstance.value.formVariables.external) {
        const url =
          processInstance.value.formVariables.external +
          //'http://uc.wehealplus.com/system/process/org?id=E412ADA2AC144CA0914F0BF8A1BD8430' +
          '&tokenSource=SSO&accessToken=' +
          authUtil.getSsoAccessToken();
        externalUrl.value = url;
      }
    } finally {
      processInstanceLoading.value = false;
    }
  };

  /** 获取流程模型视图*/
  const getProcessModelView = async () => {
    if (BpmModelType.BPMN === processDefinition.value?.modelType) {
      // 重置，解决 BPMN 流程图刷新不会重新渲染问题
      processModelView.value = {
        bpmnXml: ''
      };
    }
    const data = await ProcessInstanceApi.getProcessInstanceBpmnModelView(
      props.id
    );
    if (data) {
      processModelView.value = data;
    }
  };

  // 审批节点信息
  const activityNodes = ref<ProcessInstanceApi.ApprovalNodeInfo[]>([]);
  /**
   * 设置表单权限
   */
  const setFieldPermission = (field: string, permission: string) => {
    if (permission === FieldPermissionType.READ) {
      //@ts-ignore
      fApi.value?.disabled(true, field);
    }
    if (permission === FieldPermissionType.WRITE) {
      //@ts-ignore
      fApi.value?.disabled(false, field);
      // 加入可以编辑的字段
      writableFields.push(field);
    }
    if (permission === FieldPermissionType.NONE) {
      //@ts-ignore
      fApi.value?.hidden(true, field);
    }
  };

  /**
   * 操作成功后刷新
   */
  const refresh = async () => {
    // 重新获取详情
    await getDetail();
  };

  /** 当前的Tab */
  const activeTab = ref('form');

  /** 初始化 */
  const userOptions = ref<UserApi.UserVO[]>([]); // 用户列表

  onActivated(async () => {
    await getDetail();
    // 获得用户列表
    userOptions.value = await UserApi.getSimpleUserList();
  });
</script>

<style lang="scss" scoped>
  $wrap-padding-height: 20px;
  $wrap-margin-height: 15px;
  $button-height: 51px;
  $process-header-height: 194px;

  .processInstance-wrap-main {
    height: calc(
      100vh - var(--top-tool-height) - var(--tags-view-height) - var(
          --app-footer-height
        ) -
        35px
    );
    max-height: calc(
      100vh - var(--top-tool-height) - var(--tags-view-height) - var(
          --app-footer-height
        ) -
        35px
    );
    overflow: auto;
  }
  .form-box {
    :deep(.el-card) {
      border: none;
    }
  }
  //改变输入框选中时边框颜色
  .form-view {
    :deep(.is-disabled) {
      --ele-input-disabled-bg: #ffffff;
      --ele-input-disabled-color: #606266;
    }
  }

  .ele-admin-iframe-wrap {
    height: 100%;
    width: 100%;
  }

  .ele-admin-iframe {
    height: 100%;
    width: 100%;
    border: none;
  }

  .form-scroll-area {
    height: 100%;
  }

  .tabs-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .el-tabs {
    display: flex;
    height: 100%;
  }

  .el-tabs__header {
    order: 1; /* 确保 tabs 头部在顶部 */
  }

  .el-tabs__content {
    flex: 1;
    overflow: hidden;
    order: 2; /* 确保 tabs 内容在底部 */
  }

  .el-tab-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
</style>
