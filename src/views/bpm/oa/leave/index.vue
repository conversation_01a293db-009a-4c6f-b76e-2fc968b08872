<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <!-- 搜索工作栏 -->
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="68px"
      >
        <el-form-item label="请假类型" prop="type">
          <el-select
            v-model="queryParams.type"
            class="!w-240px"
            clearable
            placeholder="请选择请假类型"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.BPM_OA_LEAVE_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
            end-placeholder="结束日期"
            start-placeholder="开始日期"
            type="daterange"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="审批结果" prop="status">
          <el-select
            v-model="queryParams.status"
            class="!w-240px"
            clearable
            placeholder="请选择审批结果"
          >
            <el-option
              v-for="dict in getIntDictOptions(
                DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="原因" prop="reason">
          <el-input
            v-model="queryParams.reason"
            class="!w-240px"
            clearable
            placeholder="请输入原因"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            重置
          </el-button>
          <el-button plain type="primary" @click="handleCreate()">
            <el-icon class="mr-5px"><Plus /></el-icon>
            发起流程
          </el-button>
          <el-button plain type="primary" @click="handleSend()">
            <el-icon class="mr-5px"><Plus /></el-icon>
            发送表单进行审批
          </el-button>
          <el-button plain type="primary" @click="handleCheck()">
            <el-icon class="mr-5px"><Plus /></el-icon>
            查看表单
          </el-button>
        </el-form-item>
      </el-form>
    </ele-card>

    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <el-table v-loading="loading" :data="list">
        <el-table-column align="center" label="流程实例" width="300" prop="processInstanceId" />
        <el-table-column align="center" label="状态" prop="status">
          <template #default="scope">
            <dict-data
              type="tag"
              :code="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
              :model-value="scope.row.status"
            />
          </template>
        </el-table-column>
        <el-table-column
          :formatter="dateFormatter"
          align="center"
          label="开始时间"
          prop="startTime"
          width="180"
        />
        <el-table-column
          :formatter="dateFormatter"
          align="center"
          label="结束时间"
          prop="endTime"
          width="180"
        />
        <el-table-column align="center" label="请假类型" prop="type">
          <template #default="scope">
            <dict-data
              type="tag"
              :code="DICT_TYPE.BPM_OA_LEAVE_TYPE"
              :model-value="scope.row.type"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" width="190" label="原因" prop="reason" />
        <el-table-column
          :formatter="dateFormatter"
          align="center"
          label="申请时间"
          prop="createTime"
          width="180"
        />
      </el-table>
    </ele-card>
  </ele-page>
</template>
<script lang="ts" setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as LeaveApi from '@/api/bpm/leave';
  import * as ProcessInstanceApi from '@/api/bpm/processInstance';
  import { Plus } from '@element-plus/icons-vue';
  defineOptions({ name: 'BpmOALeave' });

  const message = useMessage(); // 消息弹窗
  const router = useRouter(); // 路由
  const { t } = useI18n(); // 国际化

  const loading = ref(true); // 列表的加载中
  const total = ref(0); // 列表的总页数
  const list = ref([]); // 列表的数据
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    type: undefined,
    status: undefined,
    reason: undefined,
    createTime: []
  });
  const queryFormRef = ref(); // 搜索的表单

  /** 查询列表 */
  const getList = async () => {
    loading.value = true;
    try {
      const data = await LeaveApi.getLeavePage(queryParams);
      list.value = data.list;
      total.value = data.total;
    } finally {
      loading.value = false;
    }
  };

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.pageNo = 1;
    getList();
  };

  /** 重置按钮操作 */
  const resetQuery = () => {
    queryFormRef.value.resetFields();
    handleQuery();
  };

  /** 添加操作 */
  const handleCreate = () => {
    router.push({ name: 'OALeaveCreate' });
  };
  const handleSend= async () => {
    await LeaveApi.createForm();
    message.success('发送成功');  
    resetQuery();
  }
  const handleCheck= () => {
    window.open("https://www.xl-zht.vip/attachments/req.html")
  };
  /** 详情操作 */
  const handleDetail = (row: LeaveApi.LeaveVO) => {
    router.push({
      name: 'OALeaveDetail',
      query: {
        id: row.id
      }
    });
  };

  /** 取消请假操作 */
  const cancelLeave = async (row) => {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '取消原因不能为空'
    });
    // 发起取消
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.id, value);
    message.success('取消成功');
    // 刷新列表
    await getList();
  };

  /** 审批进度 */
  const handleProcessDetail = (row) => {
    router.push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: row.processInstanceId
      }
    });
  };

  // fix: 列表不刷新的问题。
  watch(
    () => router.currentRoute.value,
    () => {
      getList();
    }
  );

  /** 初始化 **/
  onMounted(() => {
    getList();
  });
</script>
