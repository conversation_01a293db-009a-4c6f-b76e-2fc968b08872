<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="表单名">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入表单名"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            v-permission="'bpm:form:create'"
            @click="openForm"
          >
            新增表单
          </el-button>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm(row.id)"
            v-permission="['bpm:form:update']"
          >
            编辑
          </el-link>
          <el-divider direction="vertical" v-permission="'bpm:form:query'" />
          <el-link
            :underline="false"
            type="primary"
            @click="openDetail(row.id)"
            v-permission="['bpm:form:query']"
          >
            详情
          </el-link>
          <el-divider direction="vertical" v-permission="'bpm:form:delete'" />
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
            v-permission="['bpm:form:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单详情的弹窗 -->
    <el-dialog v-model="detailVisible" title="表单详情" width="800">
      <form-create :option="detailData.option" :rule="detailData.rule" />
    </el-dialog>
  </ele-page>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import * as FormApi from '@/api/bpm/form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { setConfAndFields2 } from '@/utils/formCreate';
  import { Plus, Search, Refresh } from '@element-plus/icons-vue';

  defineOptions({ name: 'InfraFile' });

  const { currentRoute, push } = useRouter(); // 路由

  const message = useMessage(); // 消息弹窗

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: null
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '表单名',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        minWidth: 110,
        slot: 'status'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return FormApi.getFormPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 添加/修改操作 */
  const openForm = (id) => {
    const toRouter = {
      name: 'BpmFormEditor'
    };
    // 表单新建的时候id传的是event需要排除
    if (typeof id === 'number') {
      toRouter.query = {
        id
      };
    }
    push(toRouter);
  };

  /** 删除按钮操作 */
  const handleDelete = async (id) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await FormApi.deleteForm(id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };
  /** 详情操作 */
  const detailVisible = ref(false);
  const detailData = ref({
    rule: [],
    option: {}
  });
  const openDetail = async (rowId) => {
    // 设置表单
    const data = await FormApi.getForm(rowId);
    setConfAndFields2(detailData, data.conf, data.fields);
    // 弹窗打开
    detailVisible.value = true;
  };
  /**表单保存返回后重新加载列表 */
  watch(
    () => currentRoute.value,
    () => {
      reload();
    },
    {
      immediate: true
    }
  );
</script>
