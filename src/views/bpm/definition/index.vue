<template>
  <ele-page flex-table>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="definitionTable"
      >
        <template #name="{ row }">
          <el-button type="primary" link @click="handleBpmnDetail(row)">
            <span>{{ row.name }}</span>
          </el-button>
        </template>
        <template #formType="{ row }">
          <el-button
            v-if="row.formType === 10"
            type="primary"
            link
            @click="handleFormDetail(row)"
          >
            <span>{{ row.formName }}</span>
          </el-button>
          <el-button v-else type="primary" link @click="handleFormDetail(row)">
            <span>{{ row.formCustomCreatePath }}</span>
          </el-button>
        </template>
        <template #version="{ row }">
          <el-tag v-if="row">v{{ row.version }}</el-tag>
          <el-tag type="warning" v-else>未部署</el-tag>
        </template>
        <template #processVersion="{ row }">
          <el-tag type="success" v-if="row.suspensionState === 1">激活</el-tag>
          <el-tag type="warning" v-if="row.suspensionState === 2">挂起</el-tag>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 弹窗：表单详情 -->
    <ele-modal title="表单详情" v-model="formDetailVisible" width="800">
      <form-create
        :rule="formDetailPreview.rule"
        :option="formDetailPreview.option"
      />
    </ele-modal>

    <!-- 弹窗：流程模型图的预览 -->
    <ele-modal title="流程图" v-model="bpmnDetailVisible" width="800">
      <MyProcessViewer style="height: 700px" key="designer" :xml="bpmnXml" />
    </ele-modal>
  </ele-page>
</template>

<script lang="ts" setup>
  import { dateFormatter } from '@/utils/formatTime';
  import { MyProcessViewer } from '@/components/bpmnProcessDesigner/package';
  import * as DefinitionApi from '@/api/bpm/definition';
  import { setConfAndFields2 } from '@/utils/formCreate';

  defineOptions({ name: 'BpmProcessDefinition' });

  const { push } = useRouter(); // 路由
  const { query } = useRoute(); // 查询参数
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'id',
        label: '定义编号',
        align: 'center',
        minWidth: 190
      },
      {
        prop: 'name',
        label: '流程名称',
        align: 'center',
        minWidth: 120,
        slot: 'name'
      },
      {
        prop: 'categoryName',
        label: '定义分类',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'formType',
        label: '表单信息',
        align: 'center',
        minWidth: 110,
        slot: 'formType'
      },
      {
        prop: 'processVersion',
        label: '流程版本',
        align: 'center',
        minWidth: 110,
        slot: 'processVersion'
      },
      {
        prop: 'version',
        label: '状态',
        align: 'center',
        minWidth: 110,
        slot: 'version'
      },
      {
        prop: 'deploymentTime',
        label: '部署时间',
        align: 'center',
        minWidth: 160,
        formatter: dateFormatter
      },
      {
        prop: 'description',
        label: '定义描述',
        align: 'center',
        minWidth: 300
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    const param = { key: query.key };
    return DefinitionApi.getProcessDefinitionPage({
      ...where,
      ...filters,
      ...pages,
      ...param
    });
  };
  /** 流程表单的详情按钮操作 */
  const formDetailVisible = ref(false);
  const formDetailPreview = ref({
    rule: [],
    option: {}
  });
  const handleFormDetail = async (row: any) => {
    if (row.formType == 10) {
      // 设置表单
      setConfAndFields2(formDetailPreview, row.formConf, row.formFields);
      // 弹窗打开
      formDetailVisible.value = true;
    } else {
      await push({
        path: row.formCustomCreatePath
      });
    }
  };

  /** 流程图的详情按钮操作 */
  const bpmnDetailVisible = ref(false);
  const bpmnXml = ref('');
  const handleBpmnDetail = async (row: any) => {
    // 设置可见
    bpmnXml.value = '';
    bpmnDetailVisible.value = true;
    // 加载 BPMN XML
    bpmnXml.value = (await DefinitionApi.getProcessDefinition(row.id))?.bpmnXml;
  };
</script>
