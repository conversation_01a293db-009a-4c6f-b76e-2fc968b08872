<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="组名">
              <el-input
                clearable
                v-model.trim="queryParams.name"
                placeholder="请输入组名"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择状态"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            :icon="Plus"
            v-permission="'bpm:user-group:create'"
            @click="openForm('create')"
          >
            新增分组
          </el-button>
        </template>
        <template #user="{ row }">
          <span
            v-for="userId in row.userIds"
            :key="userId"
            style="padding-right: 5px"
          >
            {{ userList.find((user) => user.id === userId)?.nickname }}
          </span>
        </template>
        <template #status="{ row }">
          <dict-data
            :code="DICT_TYPE.COMMON_STATUS"
            type="tag"
            :model-value="row.status"
          />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm('update', row.id)"
            v-permission="['bpm:user-group:update']"
          >
            编辑
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="'bpm:user-group:delete'"
          />
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
            v-permission="['bpm:user-group:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <user-group-form ref="formRef" @success="reload" />
  </ele-page>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';
  import { useMessage } from '@/hooks/web/useMessage';
  import { Plus, Search, Refresh } from '@element-plus/icons-vue';
  import * as UserGroupApi from '@/api/bpm/userGroup';
  import * as UserApi from '@/api/system/user';
  import UserGroupForm from './user-group-form.vue';
  const message = useMessage(); // 消息弹窗

  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: null,
    status: null,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  const userList = ref([]); // 用户列表
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '组名',
        align: 'center',
        minWidth: 100
      },
      {
        prop: 'description',
        label: '描述',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'userId',
        label: '成员',
        align: 'center',
        minWidth: 110,
        slot: 'user'
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        minWidth: 110,
        slot: 'status'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return UserGroupApi.getUserGroupPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type, id) => {
    formRef.value.open(type, id);
  };
  /** 删除按钮操作 */
  const handleDelete = async (id) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await UserGroupApi.deleteUserGroup(id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };
  /** 详情操作 */
  const detailVisible = ref(false);
  const detailData = ref({
    rule: [],
    option: {}
  });
  const openDetail = async (rowId) => {
    // 设置表单
    const data = await FormApi.getForm(rowId);
    setConfAndFields2(detailData, data.conf, data.fields);
    // 弹窗打开
    detailVisible.value = true;
  };
  onMounted(async () => {
    // 加载用户列表
    userList.value = await UserApi.getSimpleUserList();
  });
</script>
<script>
  export default {
    name: 'BpmForm'
  };
</script>
