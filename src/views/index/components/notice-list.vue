<template>
  <div style="padding: 10px 0">
    <template
      v-for="(d, i) in noticeList"
      :key="i"
      v-if="noticeList.length > 0"
    >
      <el-divider v-if="i > 0" border-style="dashed" style="margin: 0" />
      <div style="padding: 8px 0; display: flex">
        <ele-ellipsis class="notice-tab-item-title" @click="openEdit(d)">
          <span style="color: #ffe600"> ● </span>{{ d.title }}</ele-ellipsis
        >
        <ele-text type="placeholder">{{ formatDate(d.createTime) }}</ele-text>
      </div>
    </template>
    <div v-else>
      <el-empty :image-size="100"></el-empty>
    </div>
    <NoticeDetail v-model="showDetail" :data="current" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { NoticeApi } from '@/api/system/notice/noticemanager';
  import { formatDate } from '@/utils/formatTime';
  import NoticeDetail from './notice-detail.vue';

  const props = defineProps<{
    type: number;
  }>();
  const noticeList = ref([]);
  const params = ref({
    pageNo: 1,
    pageSize: 6,
    type: ''
  });
  const getNoticeList = async () => {
    const currentTabIndex = props.type;
    if (currentTabIndex == 0) {
      params.value.type = 'notice';
    } else if (currentTabIndex == 1) {
      params.value.type = 'LatestNews';
    } else if (currentTabIndex == 2) {
      params.value.type = 'HotNews';
    } else if (currentTabIndex == 3) {
      params.value.type = 'Specialrecommendation';
    }
    const result = await NoticeApi.getNoticePageQuery(params.value);
    noticeList.value = result.list;
  };
  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示详情弹窗 */
  const showDetail = ref(false);
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showDetail.value = true;
  };
  const getNoticeListExpose = async (currentTabIndex) => {
    if (currentTabIndex == 0) {
      params.value.type = 'notice';
    } else if (currentTabIndex == 1) {
      params.value.type = 'LatestNews';
    } else if (currentTabIndex == 2) {
      params.value.type = 'HotNews';
    } else if (currentTabIndex == 3) {
      params.value.type = 'Specialrecommendation';
    }
    const result = await NoticeApi.getNoticePageQuery(params.value);
    noticeList.value = result.list;
  };
  defineExpose({
    getNoticeListExpose
  });
  onMounted(() => {
    getNoticeList();
  });
</script>

<style lang="scss" scoped>
  .notice-tab-item-title {
    flex: 1;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: var(--el-color-primary);
    }
  }
</style>
