<!-- 最新动态 -->
<template>
  <ele-card :header="title" :body-style="{ padding: '6px 0', height: '370px' }">
    <template #extra>
      <div style="margin-right: 15px">
        <el-link
          type="primary"
          @click="moreNotice"
          :underline="false"
          target="_blank"
        >
          更多
        </el-link>
      </div>
      <more-icon @command="onCommand" />
    </template>
    <el-scrollbar :view-style="{ padding: '5px 15px 0 15px' }">
      <div style="max-width: 480px; min-height: 224px">
        <ele-tabs
          :items="[
            { name: 'new', label: '最新动态' },
            { name: 'hot', label: '热门时讯' },
            { name: 'subject', label: '专题推荐' }
          ]"
          v-model="active"
          type="simple"
          class="notice-tabs"
          @tab-click="clickTab"
        >
          <template #new>
            <notice-list :type="1" ref="newRef" />
          </template>
          <template #hot>
            <notice-list :type="2" ref="hotRef" />
          </template>
          <template #subject>
            <notice-list :type="3" ref="subjectRef" />
          </template>
        </ele-tabs>
      </div>
    </el-scrollbar>
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import MoreIcon from './more-icon.vue';
  import NoticeList from './notice-list.vue';
  defineProps({
    title: String
  });
  const router = useRouter(); // 路由
  const newRef = ref(null);
  const hotRef = ref(null);
  const subjectRef = ref(null);
  const emit = defineEmits(['command']);
  const active = ref('new');

  const onCommand = (command) => {
    emit('command', command);
  };
  const clickTab = (pane) => {
    const clickName = pane.paneName;
    if (clickName == 'new') {
      newRef.value.getNoticeListExpose(1);
    } else if (clickName == 'hot') {
      hotRef.value.getNoticeListExpose(2);
    } else if (clickName == 'subject') {
      subjectRef.value.getNoticeListExpose(3);
    }
  };
  const moreNotice = () => {
    router.push({
      path: '/system/messages/notice/notice-query'
    });
  };
</script>

<style lang="scss" scoped>
  div.notice-tabs.ele-tabs :deep(.el-tabs__header) {
    --ele-tab-padding: 0;
    --ele-tab-height: 46px;
    --ele-tab-font-size: 16px;
    --ele-tab-simple-hover-color: var(--el-color-primary);
    --ele-tab-simple-hover-bg: none;
    --ele-tab-simple-active-bg: none;
    --ele-tab-simple-active-weight: normal;

    .el-tabs__item {
      & + .el-tabs__item {
        margin-left: 40px;
      }

      &.is-active::after {
        height: 3px;
        width: 20px !important;
        left: 50%;
        transform: translateX(-50%);
        display: block;
      }
    }
  }
</style>
