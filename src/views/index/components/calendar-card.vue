<!-- 本月目标 -->
<template>
  <ele-card :header="title" :body-style="{ height: '370px', padding: '10px' }">
    <template #extra>
      <div style="margin-right: 15px">
        <el-link
          type="primary"
          :underline="false"
          target="_blank"
          @click.stop="openEdit('')"
        >
          添加日程
        </el-link>
      </div>
      <more-icon @command="onCommand" />
    </template>
    <ele-split-panel
      space="0px"
      size="450px"
      :allow-collapse="false"
      :resizable="false"
      :vertical="false"
      :reverse="true"
      :flexTable="true"
      style="height: 100%"
      :body-style="{ overflow: 'hidden' }"
      :custom-style="{
        overflow: 'hidden',
        border: 'none'
      }"
    >
      <el-scrollbar style="height: 100%">
        <el-timeline
          v-loading="loading"
          v-if="listDetail.length > 0"
          :reverse="false"
          style="padding: 0 20px 0 3px"
        >
          <el-timeline-item
            color="#4daf1bc9"
            v-for="(item, index) in listDetail"
            :key="index"
            :timestamp="
              formatDate(item.planDate, 'YYYY-MM-DD') + ' ' + item.planTime
            "
            placement="top"
          >
            <el-card body-style="padding:10px">
              <div class="item-card">
                <span class="time-title">日程主题：{{ item.title }}</span>
                <a
                  class="time-extra"
                  style="color: #007bd3"
                  @click="openEdit(item)"
                  >详细</a
                >
              </div>
              <!-- <span style="font-weight: 200">{{ item.planDetail }}</span> -->
              <span
                style="font-weight: 200"
                v-html="formattedPlanDetail(item)"
              ></span>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        <div v-else>
          <el-empty description="暂无日程" />
        </div>
      </el-scrollbar>
      <template #body>
        <el-calendar v-model="calendarValue">
          <template #date-cell="{ data }">
            <div class="is-point" @click="clickPoint(data.day)">
              <span>{{ Number(data.day.split('-')[2]) }}</span>
              <i v-show="pointList.includes(data.day)"></i>
            </div>
          </template>
        </el-calendar>
      </template>
    </ele-split-panel>
    <!-- 编辑弹窗 -->
    <plan-edit v-model="showEdit" :data="current" @done="reload" />
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import MoreIcon from './more-icon.vue';
  import PlanEdit from './plan-edit.vue';
  import * as UserPlandApi from '@/api/system/plan';
  import { onMounted, nextTick } from 'vue';
  import { formatDate } from '@/utils/formatTime';

  defineProps({
    title: String
  });
  const calendarValue = ref(new Date());
  const pointList = ref([]);
  const emit = defineEmits(['command']);
  const onCommand = (command) => {
    emit('command', command);
  };
  /** 详细日程 */
  const listDetail = ref([]);
  /** 当前编辑数据 */
  const current = ref(null);
  const clickValue = ref(null);
  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };
  const clickPoint = (day) => {
    clickValue.value = day;
    reloadDetail();
  };
  // 判断时间
  const reloadList = async () => {
    const res = await UserPlandApi.getUserPlanPoint();
    pointList.value = res;
  };
  const loading = ref(false);
  const reloadDetail = async () => {
    loading.value = true;
    let planDate = clickValue.value;
    if (planDate == null) {
      planDate = formatDate(calendarValue.value, 'YYYY-MM-DD');
    }
    try {
      const result = await UserPlandApi.getUserPlanList({
        planDate: planDate
      });
      listDetail.value = result;
    } finally {
      loading.value = false;
    }
  };
  const reload = () => {
    reloadList();
    reloadDetail();
  };
  // 计算属性，将换行符转换为 <br> 标签
  const formattedPlanDetail = computed(() => {
    return (item) => item.planDetail.replace(/\n/g, '<br>');
  });
  const addEventListeners = async () => {
    await nextTick();
    // 点击前一个月
    const prevBtn = document.querySelector(
      '.el-calendar__button-group .el-button-group>button:nth-child(1)'
    );
    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        clickValue.value = formatDate(calendarValue.value, 'YYYY-MM-DD');
        reloadDetail();
      });
    }
    // 点击当前日期
    const dayBtn = document.querySelector(
      '.el-calendar__button-group .el-button-group>button:nth-child(2)'
    );
    if (dayBtn) {
      dayBtn.addEventListener('click', () => {
        clickValue.value = formatDate(calendarValue.value, 'YYYY-MM-DD');
        reloadDetail();
      });
    }
    // 点击下一个月
    const nextBtn = document.querySelector(
      '.el-calendar__button-group .el-button-group>button:nth-child(3)'
    );
    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        clickValue.value = formatDate(calendarValue.value, 'YYYY-MM-DD');
        reloadDetail();
      });
    }
  };
  onMounted(addEventListeners);
  reloadList();
  reloadDetail();
</script>

<style lang="scss" scoped>
  :deep(.el-calendar) {
    padding-right: 17px;

    .el-calendar__header {
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      color: #000;
      border-bottom: 0;
    }

    .el-calendar__body {
      padding: 0 0 5px;

      thead {
        th {
          font-size: 14px;
          font-weight: 600;
          color: #7f2ca9;
        }
      }

      .el-calendar-table__row {
        td {
          height: unset;
          font-size: 13px;
          border: 0;
          border-radius: 50%;
        }

        .el-calendar-day {
          height: 40px;
          padding: 0;
          line-height: 37px;

          span {
            display: inline-block;
            width: 24px;
            height: 24px;
            font-size: 15px;
            line-height: 24px;
            text-align: center;
            border-radius: 50%;
          }

          &:hover {
            background-color: unset;

            span {
              color: #7f2ca9;
              background-color: #7f2ca91a;
            }
          }
        }

        .is-selected {
          background-color: unset;

          .el-calendar-day {
            span {
              color: #fff;
              background-color: #7f2ca9;
            }
          }
        }
      }
    }

    .is-point {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      margin: 0;

      i {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-top: 1px;
        background: #f00;
        border-radius: 50%;
      }
    }
  }
  .time-title {
    margin-bottom: 10px;
    font-size: 12px;
    --tw-text-opacity: 1;
    color: rgba(156, 163, 175, var(--tw-text-opacity));
    padding-left: 5px;
    padding-right: 5px;
  }
  .time-extra {
    margin-bottom: 10px;
    padding-left: 5px;
    padding-right: 5px;
    font-size: 13px;
    cursor: pointer; /* 添加这一行 */
  }
  .item-card {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
  }
</style>
