<!-- 最新动态 -->
<template>
  <ele-card :header="title" :body-style="{ padding: '6px 0', height: '370px' }">
    <template #extra>
      <div style="margin-right: 15px">
        <el-link
          type="primary"
          @click="moreNotice"
          :underline="false"
          target="_blank"
        >
          更多
        </el-link>
      </div>
      <more-icon @command="onCommand" />
    </template>
    <el-scrollbar :view-style="{ padding: '5px 15px 0 15px' }">
      <div style="max-width: 480px; min-height: 224px">
        <notice-list :type="0" />
      </div>
    </el-scrollbar>
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import MoreIcon from './more-icon.vue';
  import NoticeList from './notice-list.vue';
  defineProps({
    title: String
  });
  const router = useRouter(); // 路由

  const emit = defineEmits(['command']);
  const active = ref('new');
  const moreNotice = () => {
    router.push({
      path: '/system/messages/notice/notice-query'
    });
  };
  const onCommand = (command) => {
    emit('command', command);
  };
</script>

<style lang="scss" scoped>
  div.notice-tabs.ele-tabs :deep(.el-tabs__header) {
    --ele-tab-padding: 0;
    --ele-tab-height: 46px;
    --ele-tab-font-size: 16px;
    --ele-tab-simple-hover-color: var(--el-color-primary);
    --ele-tab-simple-hover-bg: none;
    --ele-tab-simple-active-bg: none;
    --ele-tab-simple-active-weight: normal;

    .el-tabs__item {
      & + .el-tabs__item {
        margin-left: 40px;
      }

      &.is-active::after {
        height: 3px;
        width: 20px !important;
        left: 50%;
        transform: translateX(-50%);
        display: block;
      }
    }
  }
</style>
