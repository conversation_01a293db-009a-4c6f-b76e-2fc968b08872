<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="文件路径">
              <el-input
                v-model="queryParams.path"
                placeholder="请输入文件路径"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="文件类型">
              <el-input
                v-model="queryParams.type"
                placeholder="请输入文件类型"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Upload"
            @click="openForm"
          >
            上传文件
          </el-button>
        </template>
        <template #fileContent="{ row }">
          <el-image
            v-if="row.type.includes('image')"
            class="h-60px w-60px"
            lazy
            :src="row.url"
            :preview-src-list="[row.url]"
            preview-teleported
            fit="cover"
          />
          <el-link
            v-else-if="row.type.includes('pdf')"
            type="primary"
            :href="row.url"
            :underline="false"
            target="_blank"
            >预览</el-link
          >
          <el-link
            v-else
            type="primary"
            download
            :href="row.url"
            :underline="false"
            target="_blank"
            >下载</el-link
          >
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
            v-permission="['infra:file:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单弹窗：添加/修改 -->
    <FileForm ref="formRef" @success="reload" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { Upload, Search, Refresh } from '@element-plus/icons-vue';
  import { fileSizeFormatter } from '@/utils';
  import { dateFormatter } from '@/utils/formatTime';
  import * as FileApi from '@/api/infra/file';
  import FileForm from './FileForm.vue';
  import { useMessage } from '@/hooks/web/useMessage';

  defineOptions({ name: 'InfraFile' });

  const message = useMessage(); // 消息弹窗
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: undefined,
    type: undefined,
    path: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '文件名',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'path',
        label: '文件路径',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'url',
        label: 'URL',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'size',
        label: '文件大小',
        align: 'center',
        minWidth: 120,
        formatter: fileSizeFormatter
      },
      {
        prop: 'type',
        label: '文件类型',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'fileContent',
        label: '文件内容',
        align: 'center',
        minWidth: 110,
        slot: 'fileContent'
      },
      {
        prop: 'createTime',
        label: '上传时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return FileApi.getFilePage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = () => {
    formRef.value.open();
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await FileApi.deleteFile(id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };
</script>
