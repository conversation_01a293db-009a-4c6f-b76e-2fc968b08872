<template>
  <ele-page flex-table>
    <!-- 列表 -->
    <!-- 列表 -->
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            v-permission="['infra:data-source-config:create']"
            @click="openForm('create')"
          >
            新建
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            v-permission="['infra:data-source-config:update']"
            @click="openForm('update', row.id)"
            :disabled="row.id === 0"
          >
            编辑
          </el-link>
          <el-divider
            v-permission="['infra:data-source-config:delete']"
            direction="vertical"
            style="margin: 0 8px"
          />
          <el-link
            :underline="false"
            type="danger"
            v-permission="['infra:data-source-config:delete']"
            @click="handleDelete(row.id)"
            :disabled="row.id === 0"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 表单弹窗：添加/修改 -->
    <DataSourceConfigForm ref="formRef" @success="reload" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { Plus } from '@element-plus/icons-vue';
  import { dateFormatter } from '@/utils/formatTime';
  import * as DataSourceConfigApi from '@/api/infra/dataSourceConfig';
  import DataSourceConfigForm from './DataSourceConfigForm.vue';

  defineOptions({ name: 'InfraDataSourceConfig' });

  const message = useMessage(); // 消息弹窗
  const { t } = useI18n(); // 国际化
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = [
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '数据源名称',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'url',
      label: '数据源连接',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'username',
      label: '用户名',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '登录日期',
      align: 'center',
      minWidth: 150,
      formatter: dateFormatter
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ];
  /** 表格数据源 */
  const datasource = () => {
    return DataSourceConfigApi.getDataSourceConfigList();
  };
  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.();
  };

  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type: string, id?: number) => {
    formRef.value.open(type, id);
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await DataSourceConfigApi.deleteDataSourceConfig(id);
      message.success(t('common.delSuccess'));
      // 刷新列表
      reload();
    } catch {}
  };
</script>
