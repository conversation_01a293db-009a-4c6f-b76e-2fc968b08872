<!-- 搜索表单 -->
<template>
  <el-form label-width="120px" @keyup.enter="search" @submit.prevent="">
    <el-row :gutter="8">
      <el-col :lg="8" :md="12" :sm="12" :xs="24">
        <el-form-item label="任务名称">
          <el-input
            clearable
            v-model.trim="form.name"
            placeholder="请输入"
            :disabled="!!data"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="12" :sm="12" :xs="24">
        <el-form-item label="处理器名称">
          <el-input
            clearable
            v-model.trim="form.handlerName"
            placeholder="请输入处理器的名称"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="12" :sm="12" :xs="24">
        <el-form-item label="状态">
          <dict-data
            :code="DICT_TYPE.INFRA_JOB_LOG_STATUS"
            v-model="form.status"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="12" :sm="12" :xs="24">
        <el-form-item label="开始执行时间" prop="beginTime">
          <el-date-picker
            v-model="form.beginTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择开始执行时间"
            clearable
            class="!w-240px"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="8" :md="12" :sm="12" :xs="24">
        <el-form-item label="结束执行时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择结束执行时间"
            clearable
            :default-time="new Date('1 23:59:59')"
            class="!w-240px"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label-width="16px">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { DICT_TYPE } from '@/utils/dict';

  const emit = defineEmits(['search']);

  const props = defineProps({
    /** 定时任务 */
    data: Object
  });

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    name: undefined,
    handlerName: undefined,
    beginTime: undefined,
    endTime: undefined,
    status: undefined
  });

  /** 日期范围 */
  const dateRange = ref(['', '']);

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value ?? [];
    emit('search', {
      ...form,
      params: {
        beginTime: d1 ? `${d1} 00:00:00` : '',
        endTime: d2 ? `${d2} 23:59:59` : ''
      }
    });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    dateRange.value = ['', ''];
    search();
  };
</script>
