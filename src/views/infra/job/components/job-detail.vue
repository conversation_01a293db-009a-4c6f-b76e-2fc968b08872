<template>
  <ele-modal
    title="任务详情"
    :width="720"
    :body-style="{ paddingTop: '6px' }"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    @open="handleOpen"
  >
    <el-descriptions
      v-if="data"
      :border="true"
      :column="mobile ? 1 : 2"
      class="detail-table"
    >
      <el-descriptions-item label="任务编号">
        <div>{{ data.id }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="任务名称">
        <div>{{ data.name }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="任务状态">
        <dict-data type="tag"  :code="DICT_TYPE.INFRA_JOB_STATUS" :model-value="data.status" />
      </el-descriptions-item>
      <el-descriptions-item label="处理器名称">
        <div>{{ data.handlerName }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="处理器参数">
        <div>{{ data.handlerParam }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="cron表达式" :span="2">
        <div>{{ data.cronExpression }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="下次执行时间">
        <div>{{ data.nextValidTime }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="重试次数">
        {{ data.retryCount }}
      </el-descriptions-item>
      <el-descriptions-item label="重试间隔">
        {{ data.retryInterval + ' 毫秒' }}
      </el-descriptions-item>
      <el-descriptions-item label="监控超时时间">
        {{ data.monitorTimeout > 0 ? data.monitorTimeout + ' 毫秒' : '未开启' }}
      </el-descriptions-item>
      <el-descriptions-item label="后续执行时间">
        <el-timeline>
          <el-timeline-item
            v-for="(nextTime, index) in nextTimes"
            :key="index"
            :timestamp="formatDate(nextTime)"
          >
            第 {{ index + 1 }} 次
          </el-timeline-item>
        </el-timeline>
      </el-descriptions-item>
    </el-descriptions>
  </ele-modal>
</template>

<script setup>
import { DICT_TYPE } from '@/utils/dict'

  import { useMobile } from '@/utils/use-mobile';
  import * as JobApi from '@/api/infra/job'

  const emit = defineEmits(['update:modelValue']);

  defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });
  const nextTimes = ref([]) // 下一轮执行时间的数组
  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const { mobile } = useMobile();

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      data.value = await JobApi.getJob(id);
      nextTimes.value = await JobApi.getJobNextTimes(id)
    }
  };
</script>

<style lang="scss" scoped>
  .detail-table :deep(.el-descriptions__label) {
    width: 120px;
    text-align: right;
    font-weight: normal;
  }
</style>
