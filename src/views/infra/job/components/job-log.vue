<template>
  <ele-drawer
    :size="1200"
    style="max-width: 100%"
    title="调度日志"
    :append-to-body="true"
    :destroy-on-close="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <job-log-search :data="data" style="margin-bottom: -8px" @search="reload" />
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      cache-key="monitorJobLogTable"
    >
      <template #toolbar>
        <el-button
          class="ele-btn-icon"
          :icon="DownloadOutlined"
          :loading="exportLoading"
          @click="exportData"
        >
          导出
        </el-button>
      </template>
      <template #excuteTime="{ row }">
        <span>{{
          formatDate(row.beginTime) + ' ~ ' + formatDate(row.endTime)
        }}</span>
      </template>
      <template #duration="{ row }">
        <span>{{ row.duration + ' 毫秒' }}</span>
      </template>
      <template #status="{ row }">
        <dict-data
          :code="DICT_TYPE.INFRA_JOB_LOG_STATUS"
          type="tag"
          :model-value="row.status"
        />
      </template>
      <template #action="{ row }">
        <el-link type="primary" :underline="false" @click="openDetail(row)">
          详情
        </el-link>
      </template>
    </ele-pro-table>
  </ele-drawer>
  <!-- 详情弹窗 -->
  <job-log-detail v-model="showInfo" :data="current" />
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { DownloadOutlined } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';
  import { formatDate } from '@/utils/formatTime';
  import JobLogSearch from './job-log-search.vue';
  import JobLogDetail from './job-log-detail.vue';
  import * as JobLogApi from '@/api/infra/jobLog';
  import download from '@/utils/download';

  const emit = defineEmits(['update:modelValue']);
  const exportLoading = ref(false); // 导出的加载中
  const message = useMessage(); // 消息弹窗

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 定时任务 */
    data: Object
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'handlerName',
        label: '处理器名称',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'handlerParam',
        label: '处理器的参数',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'executeIndex',
        label: '第几次执行',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'excuteTime',
        label: '处理器的参数',
        align: 'center',
        minWidth: 110,
        slot: 'excuteTime'
      },
      {
        prop: 'duration',
        label: '执行时长',
        align: 'center',
        minWidth: 140,
        slot: 'duration'
      },
      {
        prop: 'status',
        label: '任务状态',
        align: 'center',
        minWidth: 110,
        slot: 'status'
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 80,
        align: 'center',
        slot: 'action',
        fixed: 'right',
        hideInPrint: true
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前选中数据 */
  const current = ref({});

  /** 是否显示查看弹窗 */
  const showInfo = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    const params = { ...where, ...filters, ...pages };
    if (props.data) {
      params.jobId = props.data.id;
    }
    return JobLogApi.getJobLogPage(params);
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ where });
  };

  /** 导出数据 */
  const exportData = async () => {
    try {
      // 导出的二次确认
      await message.exportConfirm();
      // 发起导出
      exportLoading.value = true;
      tableRef.value?.fetch?.(async ({ where, filters }) => {
        const params = { ...where, ...filters };
        if (props.data) {
          params.jobId = props.data.jobId;
        }
        const data = await JobLogApi.exportJobLog(params);
        download.excel(data, '定时任务执行日志.xls');
      });
    } finally {
      exportLoading.value = false;
    }
  };

  /** 详情 */
  const openDetail = (row) => {
    current.value = row;
    showInfo.value = true;
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        reload();
      } else {
        selections.value = [];
      }
    }
  );
</script>
