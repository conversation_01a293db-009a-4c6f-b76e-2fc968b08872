<template>
  <ele-modal
    title="调度日志详情"
    :width="720"
    :body-style="{ paddingTop: '6px' }"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    @open="handleOpen"
  >
    <el-descriptions
      v-if="data"
      :border="true"
      :column="mobile ? 1 : 2"
      class="detail-table"
    >
      <el-descriptions-item label="日志编号">
        <div>{{ data.id }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="任务名称">
        <div>{{ data.jobName }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="处理器名称">
        <div>{{ data.handlerName }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="处理器参数">
        <div>{{ data.handlerParam }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="第几次执行">
        {{ data.executeIndex }}
      </el-descriptions-item>
      <el-descriptions-item label="执行时间">
        {{ formatDate(data.beginTime) + ' ~ ' + formatDate(data.endTime) }}
      </el-descriptions-item>
      <el-descriptions-item label="执行时长">
        {{ data.duration + ' 毫秒' }}
      </el-descriptions-item>
      <el-descriptions-item label="任务状态">
        <dict-tag :type="DICT_TYPE.INFRA_JOB_LOG_STATUS" :value="data.status" />
      </el-descriptions-item>
      <el-descriptions-item label="执行结果">
        {{ data.result }}
      </el-descriptions-item>
    </el-descriptions>
  </ele-modal>
</template>

<script setup>
  import { useMobile } from '@/utils/use-mobile';
  import { DICT_TYPE } from '@/utils/dict';
  import { formatDate } from '@/utils/formatTime';
  import * as JobLogApi from '@/api/infra/jobLog';
  import { handleError } from 'vue';
  const emit = defineEmits(['update:modelValue']);

  defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  const { mobile } = useMobile();

  /** 弹窗打开事件 */
  const handleOpen = async () => {
    if (props.data) {
      data.value = await JobLogApi.getJobLog(id)
    }
  };
</script>

<style lang="scss" scoped>
  .detail-table :deep(.el-descriptions__label) {
    width: 88px;
    text-align: right;
    font-weight: normal;
  }
</style>
