<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <config-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        highlight-current-row
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            class="ele-btn-icon hidden-sm-and-down"
            :icon="DeleteOutlined"
            @click="removeBatch()"
          >
            删除
          </el-button>
        </template>
        <template #visible="{ row }">
          <dict-data
            :code="DICT_TYPE.INFRA_BOOLEAN_STRING"
            type="tag"
            :model-value="row.visible"
          />
        </template>
        <template #type="{ row }">
          <dict-data
            :code="DICT_TYPE.INFRA_CONFIG_TYPE"
            type="tag"
            :model-value="row.type"
          />
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" :underline="false" @click="removeBatch(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <config-edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { PlusOutlined, DeleteOutlined } from '@/components/icons';
  import { DICT_TYPE } from '@/utils/dict';
  import ConfigSearch from './components/config-search.vue';
  import ConfigEdit from './components/config-edit.vue';
  import * as ConfigApi from '@/api/system/config';
  import { dateFormatter } from '@/utils/formatTime';

  defineOptions({ name: 'SystemConfig' });
  const { t } = useI18n(); // 国际化
  const message = useMessage(); // 消息弹窗
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'category',
        label: '参数分类',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'name',
        label: '参数名称',
        align: 'center',
        minWidth: 160
      },
      {
        prop: 'key',
        label: '参数键名',
        align: 'center',
        minWidth: 210
      },
      {
        prop: 'value',
        label: '参数键值',
        align: 'center',
        minWidth: 110
      },
      {
        prop: 'visible',
        label: '是否可见',
        width: 110,
        align: 'center',
        slot: 'visible'
      },
      {
        prop: 'type',
        label: '系统内置',
        width: 110,
        align: 'center',
        slot: 'type'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'createTime',
        label: '创建日期',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 120,
        align: 'center',
        slot: 'action',
        hideInPrint: true,
        fixed: 'right'
      }
    ];
  });

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return ConfigApi.getConfigPage({ ...where, ...filters, ...pages });
  };

  /** 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 批量删除 */
  const removeBatch = async (row) => {
    // 删除的二次确认
    await message.delConfirm();
    // 发起删除
    await ConfigApi.deleteConfig(row.id);
    message.success(t('common.delSuccess'));
    // 刷新列表
    reload();
  };
</script>
