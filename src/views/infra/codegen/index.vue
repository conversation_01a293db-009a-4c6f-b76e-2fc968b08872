<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <gen-search @search="reload" />
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        cache-key="toolGenTable"
      >
        <template #toolbar>
          <el-space :size="12" wrap>
            <el-button
              v-permission="['infra:codegen:create']"
              type="primary"
              class="ele-btn-icon"
              :icon="UploadOutlined"
              @click="openImport"
            >
              导入
            </el-button>
          </el-space>
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            @click="handlePreview(row)"
          >
            预览
          </el-link>
          <el-divider direction="vertical" v-permission="['infra:codegen:download']"/>
          <el-link type="primary" v-permission="['infra:codegen:download']" :underline="false" @click="handleGenTable(row)">
            生成
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['infra:codegen:update']"
          />
          <el-link
            type="primary"
            v-permission="['infra:codegen:update']"
            :underline="false"
            @click="handleSyncDB(row)"
          >
            同步
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="['infra:codegen:update']"
          />
          <el-link
            type="primary"
            v-permission="'infra:codegen:update'"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            direction="vertical"
            v-permission="'infra:codegen:delete'"
          />
          <el-link
            type="danger"
            v-permission="'infra:codegen:delete'"
            :underline="false"
            @click="handleDelete(row.id)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <gen-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 导入弹窗 -->
    <gen-import v-model="showImport" @done="reload" />
    <!-- 创建表弹窗 -->
    <gen-create v-model="showCreate" @done="reload" />
    <preview-code ref="previewRef" />
  </ele-page>
</template>

<script setup>
      import { ref } from 'vue';
      import { UploadOutlined } from '@/components/icons';
      import GenSearch from './components/gen-search.vue';
      import GenEdit from './components/gen-edit.vue';
      import GenImport from './components/gen-import.vue';
      import GenCreate from './components/gen-create.vue';
      import { dateFormatter } from '@/utils/formatTime';
      import PreviewCode from './components/preview-code.vue';
      import download from '@/utils/download'
      import * as CodegenApi from '@/api/infra/codegen';
      const { t } = useI18n(); // 国际化
      const message = useMessage(); // 消息弹窗
      /** 表格实例 */
      const tableRef = ref(null);

      /** 表格列配置 */
      const columns = ref([
        {
          type: 'selection',
          columnKey: 'selection',
          width: 50,
          align: 'center',
          fixed: 'left'
        },
        {
          type: 'index',
          columnKey: 'index',
          width: 50,
          align: 'center',
          fixed: 'left'
        },
        {
          prop: 'tableName',
          label: '表名称',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'tableComment',
          label: '表描述',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'className',
          label: '实体',
          align: 'center',
          minWidth: 110
        },
        {
          prop: 'createTime',
          label: '创建时间',
          align: 'center',
          minWidth: 110,
          formatter: dateFormatter
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          align: 'center',
          minWidth: 110,
          formatter: dateFormatter
        },
        {
          columnKey: 'action',
          label: '操作',
          width: 280,
          align: 'center',
          slot: 'action',
          hideInPrint: true
        }
      ]);

      /** 表格选中数据 */
      const selections = ref([]);

      /** 当前编辑数据 */
      const current = ref(null);

      /** 是否显示编辑弹窗 */
      const showEdit = ref(false);

      /** 是否显示导入弹窗 */
      const showImport = ref(false);

      /** 是否显示创建表弹窗 */
      const showCreate = ref(false);

      /** 表格数据源 */
      const datasource = ({ pages, where }) => {
        return CodegenApi.getCodegenTablePage({ ...where, ...pages });
      };

      /** 搜索 */
      const reload = (where) => {
        tableRef.value?.reload?.({ page: 1, where });
      };

      /** 打开编辑弹窗 */
      const openEdit = (row) => {
        current.value = row ?? null;
        showEdit.value = true;
      };

      /** 打开导入弹窗 */
      const openImport = () => {
        showImport.value = true;
      };

    /** 同步操作  */
    const handleSyncDB = async (row) => {
      // 基于 DB 同步
      const tableName = row.tableName
      try {
        await message.confirm('确认要强制同步' + tableName + '表结构吗?', t('common.reminder'))
        await CodegenApi.syncCodegenFromDB(row.id)
        message.success('同步成功')
      } catch {}
    }

  /** 生成代码操作 */
  const handleGenTable = async (row) => {
    const res = await CodegenApi.downloadCodegen(row.id)
    download.zip(res, 'codegen-' + row.className + '.zip')
  }
      /** 删除按钮操作 */
      const handleDelete = async (id) => {
        try {
          // 删除的二次确认
          await message.delConfirm();
          // 发起删除
          await CodegenApi.deleteCodegenTable(id);
          message.success(t('common.delSuccess'));
          // 刷新列表
          await getList();
        } catch {}
      };

      /** 预览操作 */
      const previewRef = ref();
      const handlePreview = (row) => {
        previewRef.value.open(row.id);
      };
      /** 打开创建表弹窗 */
      const openCreate = () => {
        showCreate.value = true;
      };
</script>

<script>
  export default {
    name: 'ToolGen'
  };
</script>
