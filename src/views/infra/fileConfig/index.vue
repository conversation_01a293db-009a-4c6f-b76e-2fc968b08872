<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <ele-card :body-style="{ paddingBottom: '2px' }">
      <el-form label-width="72px" @keyup.enter="reload" @submit.prevent="">
        <el-row :gutter="8">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="文件路径">
              <el-input
                v-model="queryParams.path"
                placeholder="请输入文件路径"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="文件类型">
              <el-input
                v-model="queryParams.type"
                placeholder="请输入文件类型"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label-width="16px">
              <el-button :icon="Search" type="primary" @click="reload"
                >查询</el-button
              >
              <el-button :icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <ele-card flex-table :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        cache-key="bpmFormTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="Plus"
            @click="openForm('create')"
            v-permission="['infra:file-config:create']"
          >
            新增
          </el-button>
        </template>
        <template #storage="{ row }">
          <dict-data type="tag" :code="DICT_TYPE.INFRA_FILE_STORAGE" :model-value="row.storage" />
        </template>
        <template #master="{ row }">
          <dict-data type="tag" :code="DICT_TYPE.INFRA_BOOLEAN_STRING" :model-value="row.master" />
        </template>
        <template #action="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="openForm('update',row.id)"
            v-permission="['infra:file-config:update']"
          >
          编辑
          </el-link>
          <el-divider direction="vertical" style="margin: 0" />
          <el-link
            :underline="false"
            type="primary"
            @click="handleMaster(row.id)"
            v-permission="['infra:file-config:update']"
          >
          主配置
          </el-link>
          <el-divider v-permission="['infra:file-config:delete']" direction="vertical" style="margin: 0" />
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(row.id)"
            v-permission="['infra:file-config:delete']"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 表单弹窗：添加/修改 -->
    <FileConfigForm ref="formRef" @success="reload" />
  </ele-page>
</template>
<script lang="ts" setup>
  import { useFormData } from '@/utils/use-form-data';
  import { Plus, Search, Refresh } from '@element-plus/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import * as FileConfigApi from '@/api/infra/fileConfig';
  import FileConfigForm from './FileConfigForm.vue';
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict';
  import { dateFormatter } from '@/utils/formatTime';

  defineOptions({ name: 'InfraFileConfig' });

  const message = useMessage(); // 消息弹窗
  /** 表单数据 */
  const [queryParams, resetFields] = useFormData({
    name: undefined,
    storage: undefined,
    createTime: []
  });
  /**  重置 */
  const reset = () => {
    resetFields();
    reload();
  };
  /** 表格实例 */
  const tableRef = ref(null);
  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '配置名',
        align: 'center',
        minWidth: 130
      },
      {
        prop: 'storage',
        label: '存储器',
        align: 'center',
        minWidth: 180,
        slot: 'storage'
      },
      {
        prop: 'remark',
        label: '备注',
        align: 'center',
        minWidth: 180
      },
      {
        prop: 'master',
        label: '主配置',
        align: 'center',
        minWidth: 180,
        slot: 'master'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        align: 'center',
        minWidth: 180,
        formatter: dateFormatter
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 170,
        fixed: 'right',
        align: 'center',
        slot: 'action',
        hideInPrint: true
      }
    ];
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, filters }) => {
    return FileConfigApi.getFileConfigPage({
      ...where,
      ...filters,
      ...pages
    });
  };

  /** 搜索 */
  const reload = () => {
    tableRef.value?.reload?.({ page: 1, where: { ...queryParams } });
  };
  /** 添加/修改操作 */
  const formRef = ref();
  const openForm = (type: string, id?: number) => {
    formRef.value.open(type, id);
  };

  /** 删除按钮操作 */
  const handleDelete = async (id: number) => {
    try {
      // 删除的二次确认
      await message.delConfirm();
      // 发起删除
      await FileConfigApi.deleteFileConfig(id);
      message.success('删除成功');
      // 刷新列表
      reload();
    } catch {}
  };

  /** 主配置按钮操作 */
  const handleMaster = async (id) => {
    try {
      await message.confirm(
        '是否确认修改当前配置为主配置?'
      );
      await FileConfigApi.updateFileConfigMaster(id);
      message.success('保存成功');
      reload();
    } catch {}
  };

  /** 测试按钮操作 */
  const handleTest = async (id) => {
    try {
      const response = await FileConfigApi.testFileConfig(id);
      await message.confirm('是否要访问该文件？', '测试上传成功');
      window.open(response, '_blank');
    } catch {}
  };
</script>
