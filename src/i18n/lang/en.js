export const en = {
  layout: {
    home: 'Home',
    header: {
      profile: 'Profile',
      password: 'Password',
      logout: 'SignOut'
    },
    logout: {
      title: 'Confirm',
      message: 'Are you sure you want to log out?'
    },
    tabs: {
      reload: 'Refresh',
      close: 'Close',
      closeLeft: 'Close Left',
      closeRight: 'Close Right',
      closeOther: 'Close Other',
      closeAll: 'Close All',
      fullscreen: 'Fullscreen',
      fullscreenExit: 'Fullscreen'
    },
    setting: {
      title: 'Theme Setting',
      sideStyles: {
        dark: 'Dark Sidebar',
        light: 'Light Sidebar'
      },
      headStyles: {
        light: 'Light Header',
        dark: 'Dark Header',
        primary: 'Primary Header'
      },
      layoutStyles: {
        side: 'Side Menu Layout',
        top: 'Top Menu Layout',
        mix: 'Mix Menu Layout'
      },
      colors: {
        default: 'Daybreak Blue',
        dust: 'Dust Blue',
        sunset: 'Sunset Orange',
        volcano: 'Volcano',
        purple: 'Golden Purple',
        green: 'Polar Green',
        geekblue: 'Geek Blue'
      },
      darkMode: 'Dark Mode',
      roundedTheme: 'Rounded Theme',
      layout: 'Navigation Mode',
      sidebarLayout: 'Sidebar Double Menu',
      fluid: 'Full Body Width',
      other: 'Other Setting',
      fixedHeader: 'Fixed Header',
      fixedSidebar: 'Fixed Sidebar',
      fixedBody: 'Fixed Body',
      logoInHeader: 'Logo In Header',
      colorfulIcon: 'Colorful Icon',
      uniqueOpened: 'Menu Unique Open',
      responsive: 'Responsive',
      weakMode: 'Weak Mode',
      showTabs: 'Show Tabs',
      fixedHome: 'Fixed Home Tab',
      tabInHeader: 'Tab In Header',
      tabStyle: 'Tab Style',
      tabStyles: {
        default: 'Default',
        dot: 'Dot',
        tag: 'Tag',
        card: 'Card'
      },
      menuItemTrigger: 'Menu Division',
      transitionName: 'Transition',
      transitions: {
        slideRight: 'Slide Right',
        slideBottom: 'Slide Bottom',
        zoomIn: 'Zoom In',
        zoomOut: 'Zoom Out',
        fade: 'Fade'
      },
      reset: 'Reset'
    }
  },
  login: {
    title: 'User Login',
    username: 'please input username',
    password: 'please input password',
    code: 'please input code',
    remember: 'remember',
    login: 'Login',
    passwordType: 'Password',
    qrcodeType: 'QR Code',
    refreshQrcode: 'Refresh'
  },
  list: {
    // 基础列表
    basic: {
      table: {
        avatar: 'Avatar',
        username: 'Username',
        nickname: 'Nickname',
        organizationName: 'Organization',
        phone: 'Phone',
        email: 'Email',
        roles: 'Roles',
        sexName: 'Sex',
        createTime: 'CreateTime',
        status: 'Status',
        action: 'Action'
      }
    }
  },
  common: {
    inputText: 'Please input',
    selectText: 'Please select',
    startTimeText: 'Start time',
    endTimeText: 'End time',
    login: 'Login',
    required: 'This is required',
    loginOut: 'Login out',
    document: 'Document',
    profile: 'User Center',
    reminder: 'Reminder',
    loginOutMessage: 'Exit the system?',
    back: 'Back',
    ok: 'OK',
    save: 'Save',
    cancel: 'Cancel',
    close: 'Close',
    reload: 'Reload current',
    success: 'Success',
    closeTab: 'Close current',
    closeTheLeftTab: 'Close left',
    closeTheRightTab: 'Close right',
    closeOther: 'Close other',
    closeAll: 'Close all',
    prevLabel: 'Prev',
    nextLabel: 'Next',
    skipLabel: 'Jump',
    doneLabel: 'End',
    menu: 'Menu',
    menuDes: 'Menu bar rendered in routed structure',
    collapse: 'Collapse',
    collapseDes: 'Expand and zoom the menu bar',
    tagsView: 'Tags view',
    tagsViewDes: 'Used to record routing history',
    tool: 'Tool',
    toolDes: 'Used to set up custom systems',
    query: 'Query',
    reset: 'Reset',
    shrink: 'Put away',
    expand: 'Expand',
    confirmTitle: 'System Hint',
    exportMessage: 'Whether to confirm export data item?',
    importMessage: 'Whether to confirm import data item?',
    createSuccess: 'Create Success',
    updateSuccess: 'Update Success',
    delMessage: 'Delete the selected data?',
    delDataMessage: 'Delete the data?',
    delNoData: 'Please select the data to delete',
    delSuccess: 'Deleted successfully',
    index: 'Index',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    copy: 'Copy',
    copySuccess: 'Copy Success',
    copyError: 'Copy Error'
  },
  common: {
    inputText: 'Please input',
    selectText: 'Please select',
    startTimeText: 'Start time',
    endTimeText: 'End time',
    login: 'Login',
    required: 'This is required',
    loginOut: 'Login out',
    document: 'Document',
    profile: 'User Center',
    reminder: 'Reminder',
    loginOutMessage: 'Exit the system?',
    back: 'Back',
    ok: 'OK',
    save: 'Save',
    cancel: 'Cancel',
    close: 'Close',
    reload: 'Reload current',
    success: 'Success',
    closeTab: 'Close current',
    closeTheLeftTab: 'Close left',
    closeTheRightTab: 'Close right',
    closeOther: 'Close other',
    closeAll: 'Close all',
    prevLabel: 'Prev',
    nextLabel: 'Next',
    skipLabel: 'Jump',
    doneLabel: 'End',
    menu: 'Menu',
    menuDes: 'Menu bar rendered in routed structure',
    collapse: 'Collapse',
    collapseDes: 'Expand and zoom the menu bar',
    tagsView: 'Tags view',
    tagsViewDes: 'Used to record routing history',
    tool: 'Tool',
    toolDes: 'Used to set up custom systems',
    query: 'Query',
    reset: 'Reset',
    shrink: 'Put away',
    expand: 'Expand',
    confirmTitle: 'System Hint',
    exportMessage: 'Whether to confirm export data item?',
    importMessage: 'Whether to confirm import data item?',
    createSuccess: 'Create Success',
    saveSuccess: 'Save Success',
    updateSuccess: 'Update Success',
    delMessage: 'Delete the selected data?',
    delDataMessage: 'Delete the data?',
    delNoData: 'Please select the data to delete',
    delSuccess: 'Deleted successfully',
    index: 'Index',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    copy: 'Copy',
    copySuccess: 'Copy Success',
    copyError: 'Copy Error'
  },
  action: {
    create: 'Create',
    add: 'Add',
    del: 'Delete',
    delete: 'Delete',
    edit: 'Edit',
    update: 'Update',
    preview: 'Preview',
    more: 'More',
    sync: 'Sync',
    save: 'Save',
    detail: 'Detail',
    export: 'Export',
    import: 'Import',
    generate: 'Generate',
    logout: 'Login Out',
    test: 'Test',
    typeCreate: 'Dict Type Create',
    typeUpdate: 'Dict Type Eidt',
    dataCreate: 'Dict Data Create',
    dataUpdate: 'Dict Data Eidt',
    fileUpload: 'File Upload'
  }
};
