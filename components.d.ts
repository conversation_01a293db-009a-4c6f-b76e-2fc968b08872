import 'element-plus/global';
import 'ele-admin-plus/typings/global';

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    DictData: (typeof import('@/components/DictData/index.vue'))['default'];
    UploadImg: (typeof import('@/components/UploadFile/src/UploadImg.vue'))['default'];
    UploadImgs: (typeof import('@/components/UploadFile/src/UploadImgs.vue'))['default'];
    UploadFile: (typeof import('@/components/UploadFile/src/UploadFile.vue'))['default'];
  }
}

export {};
