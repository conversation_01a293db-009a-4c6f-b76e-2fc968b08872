// vite.config.js
import { defineConfig } from "file:///D:/File/WorkSpace/VsCode/weiheng/weiheng-front/weiheng-front/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/File/WorkSpace/VsCode/weiheng/weiheng-front/weiheng-front/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import Compression from "file:///D:/File/WorkSpace/VsCode/weiheng/weiheng-front/weiheng-front/node_modules/vite-plugin-compression/dist/index.mjs";
import AutoImport from "file:///D:/File/WorkSpace/VsCode/weiheng/weiheng-front/weiheng-front/node_modules/unplugin-auto-import/dist/vite.js";
import UnoCSS from "file:///D:/File/WorkSpace/VsCode/weiheng/weiheng-front/weiheng-front/node_modules/unocss/dist/vite.mjs";
var vite_config_default = defineConfig(({ command }) => {
  const alias = {
    "@/": resolve("src") + "/",
    "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js"
  };
  const plugins = [
    vue(),
    UnoCSS(),
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
      imports: [
        "vue",
        "vue-router",
        // 可额外添加需要 autoImport 的组件
        {
          "vue-i18n": ["useI18n"],
          "@/hooks/web/useMessage": ["useMessage"],
          "@/utils/dict": ["DICT_TYPE"],
          "@/utils/use-form-data": ["useFormData"]
        }
      ],
      //注意这个配置和src同级
      dts: "./auto-imports.d.ts"
    })
  ];
  alias["./as-needed"] = "./global-import";
  plugins.push(
    Compression({
      disable: false,
      threshold: 10240,
      algorithm: "gzip",
      ext: ".gz"
    })
  );
  return {
    resolve: { alias },
    plugins,
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`
        }
      }
    },
    optimizeDeps: {
      include: [
        "echarts/core",
        "echarts/charts",
        "echarts/renderers",
        "echarts/components",
        "vue-echarts",
        "vuedraggable",
        "sortablejs"
      ]
    },
    build: {
      target: "chrome63",
      chunkSizeWarningLimit: 2e3
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
