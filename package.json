{"name": "we<PERSON>eng-front", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "vite --host", "serve": "vite build && vite preview --host", "build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^7.0.2", "@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.11", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "axios": "^1.6.8", "@vueuse/core": "^10.9.0", "diagram-js": "^12.8.0", "min-dash": "^4.1.1", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "bpmn-js-token-simulation": "^0.10.0", "camunda-bpmn-moddle": "^7.0.1", "countup.js": "^2.8.0", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "ele-admin-plus": "1.1.9", "element-plus": "2.8.4", "highlight.js": "^11.9.0", "jsbarcode": "^3.11.6", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.12.0", "sortablejs": "^1.15.2", "steady-xml": "^0.1.0", "tinymce": "^5.10.9", "unocss": "^0.58.5", "unplugin-auto-import": "^0.16.7", "vue": "^3.4.21", "vue-demi": "^0.14.10", "vue-dompurify-html": "^4.1.4", "vue-echarts": "^6.6.9", "vue-i18n": "^9.12.0", "vue-router": "^4.3.0", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xgplayer": "^3.0.16"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.21", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.24.1", "postcss": "^8.4.38", "prettier": "^3.2.5", "rimraf": "^5.0.5", "sass": "1.75.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.8", "vite-plugin-compression": "^0.5.1", "vue-eslint-parser": "^9.4.2"}}